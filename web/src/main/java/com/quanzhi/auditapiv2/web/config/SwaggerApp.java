package com.quanzhi.auditapiv2.web.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;


@Configuration
@ConditionalOnProperty(name = "swagger.enable",  havingValue = "true")
@EnableSwagger2
@EnableKnife4j
public class SwaggerApp {
    //是否启用swagger
    @NacosValue(value = "${swagger.enable:false}", autoRefreshed = true)
    private boolean enableSwagger;

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enableSwagger)
                .apiInfo(apiInfo())
                .select()
                //为当前包路径
                .apis(scanBasePackage("com.quanzhi.auditapiv2;com.quanzhi.audit.mix"))
                .paths(PathSelectors.any())
                .build();
//        return new Docket(DocumentationType.SWAGGER_2).select().apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class)).build();
    }

    //构建 api文档的详细信息函数,注意这里的注解引用的是哪个
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                //页面标题
                .title("Api接口文档")
                //版本号
                .version("2.1.0")
                //描述
                .description("API 描述")
                .build();
    }

    /**
     * 切割扫描的包生成Predicate<RequestHandler>
     * @param basePackage
     * @return
     */
    public static Predicate<RequestHandler> scanBasePackage(final String basePackage) {
        if(DataUtil.isEmpty(basePackage)){
            throw new NullPointerException("basePackage不能为空，多个包扫描使用;分隔");
        }
        String[] controllerPack = basePackage.split(";");
        Predicate<RequestHandler> predicate = null;
        for (int i = controllerPack.length -1; i >= 0; i--) {
            String strBasePackage = controllerPack[i];
            if(DataUtil.isNotEmpty(strBasePackage)){
                Predicate<RequestHandler> tempPredicate = RequestHandlerSelectors.basePackage(strBasePackage);
                predicate = predicate == null ? tempPredicate : Predicates.or(tempPredicate,predicate);
            }
        }
        if(predicate == null){
            throw new NullPointerException("basePackage配置不正确，多个包扫描使用;分隔");
        }
        return predicate;
    }
}
