package com.quanzhi.auditapiv2.web.controller;

import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/httpApp")
public class ZRHttpAppController extends HttpAppController{
    private Logger logger = LoggerFactory.getLogger(ZRHttpAppController.class);

    @RequestMapping(value = "editAppCustomField", method = RequestMethod.POST)
    @ApiOperation(value = "编辑应用自定义字段", tags = "API3.0")
    public ResponseVo<Boolean> editAppCustomField(String appId, String customField, String value) {
        try{
            boolean editAppCustomField = httpAppServiceImpl.editAppCustomField(appId, customField, value);
            if(editAppCustomField){
                return ResponseVo.ok();
            }else{
                return ResponseVo.error(500,"该部门已分配资产权限，不允许删除/修改");
            }
        }catch (Exception e){
            logger.error("编辑应用自定义字段异常",e);
            return ResponseVo.error(500,"编辑应用自定义字段异常");
        }
    }
}
