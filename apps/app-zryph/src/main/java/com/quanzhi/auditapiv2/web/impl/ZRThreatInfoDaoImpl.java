package com.quanzhi.auditapiv2.web.impl;

import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum;
import com.quanzhi.auditapiv2.common.risk.repository.ThreatInfoDaoImpl;
import com.quanzhi.auditapiv2.web.dto.ZRThreatInfoSearchDto;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@Repository
public class ZRThreatInfoDaoImpl  extends ThreatInfoDaoImpl {
    public ZRThreatInfoDaoImpl(MongoTemplate mongoTemplate) {
        super(mongoTemplate);
    }
    public Criteria getCriteria(ZRThreatInfoSearchDto threatInfoSearchDto) {
        Criteria criteria = new Criteria();
        criteria.and("delFlag").is(false);
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getThreatEntity())) {
            criteria.and("threatEntity").regex(com.quanzhi.auditapiv2.common.util.utils.DataUtil.regexStrEscape(threatInfoSearchDto.getThreatEntity()));
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getIsAssetAuthorization()) && threatInfoSearchDto.getIsAssetAuthorization() && DataUtil.isNotEmpty(threatInfoSearchDto.getAppUriSet())) {
            criteria.and("appUri").in(threatInfoSearchDto.getAppUriSet());
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getThreatType())) {
            criteria.and("threatType").is(threatInfoSearchDto.getThreatType());
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getLocation())) {
            criteria.and("location").is(threatInfoSearchDto.getLocation());
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getIsMalicious())) {
            criteria.and("isMalicious").is(threatInfoSearchDto.getIsMalicious());
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getSeverity())) {
            criteria.and("severity").is(threatInfoSearchDto.getSeverity());
        }
        if(DataUtil.isNotEmpty(threatInfoSearchDto.getJudgments())){
            criteria.and("judgments").in(threatInfoSearchDto.getJudgments());
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getNetworkSegment())
                && threatInfoSearchDto.getNetworkSegment().size() > 0
                && !"ALL".equals(threatInfoSearchDto.getNetworkSegment().get(0))) {
            String operator = "";
            if (threatInfoSearchDto.getFieldOperateMap() != null) {
                operator = threatInfoSearchDto.getFieldOperateMap().get("networkSegment");
            } else {
                operator = "or";
            }
            if (DBOperatorEnum.OR.operator().equals(operator)) {
                criteria.and("networkSegment").in(threatInfoSearchDto.getNetworkSegment());
            } else {
                criteria.and("networkSegment").all(threatInfoSearchDto.getNetworkSegment());
            }
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getRiskNum())) {
            criteria.and("riskNum").is(threatInfoSearchDto.getRiskNum());
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getConfirmRiskNum())) {
            criteria.and("confirmRiskNum").is(threatInfoSearchDto.getConfirmRiskNum());
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getRiskNames())) {
            if (threatInfoSearchDto.getRiskNames().contains("ALL")) {
                Query query = new Query();
                query.addCriteria(Criteria.where("delFlag").is(false));
                query.fields().include("name");
                // 风险事件全部筛选
                List<Map> riskPolicy = mongoTemplate.find(query, Map.class, "riskPolicy");
                List<String> riskNames = riskPolicy.stream().filter(i -> i != null && !i.equals("null") && !i.equals("")).map(i -> i.get("name").toString()).collect(Collectors.toList());
                threatInfoSearchDto.setRiskNames(riskNames);
            }
            String operator = threatInfoSearchDto.getFieldOperateMap().get("riskNames");
            if (DBOperatorEnum.OR.operator().equals(operator)) {
                criteria.and("riskNames").in(threatInfoSearchDto.getRiskNames());
            } else {
                criteria.and("riskNames").all(threatInfoSearchDto.getRiskNames());
            }
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getThreatLabels())
                && threatInfoSearchDto.getThreatLabels().size() > 0
                && !"ALL".equals(threatInfoSearchDto.getThreatLabels().get(0))) {
            String operator = threatInfoSearchDto.getFieldOperateMap().get("threatLabels");
            if (DBOperatorEnum.OR.operator().equals(operator)) {
                criteria.and("threatLabels").in(threatInfoSearchDto.getThreatLabels());
            } else {
                criteria.and("threatLabels").all(threatInfoSearchDto.getThreatLabels());
            }
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getFirstTimeStart()) && DataUtil.isNotEmpty(threatInfoSearchDto.getFirstTimeEnd())) {
            criteria.and("firstTime").gt(threatInfoSearchDto.getFirstTimeStart()).lte(threatInfoSearchDto.getFirstTimeEnd());
        }
        if (DataUtil.isNotEmpty(threatInfoSearchDto.getLastTimeStart()) && DataUtil.isNotEmpty(threatInfoSearchDto.getLastTimeEnd())) {
            criteria.and("lastTime").gt(threatInfoSearchDto.getLastTimeStart()).lte(threatInfoSearchDto.getLastTimeEnd());
        }
        return criteria;
    }
}
