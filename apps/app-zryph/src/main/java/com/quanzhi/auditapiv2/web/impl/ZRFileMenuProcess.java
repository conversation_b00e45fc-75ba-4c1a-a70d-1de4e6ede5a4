package com.quanzhi.auditapiv2.web.impl;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.cache.CaffeineCache;
import com.quanzhi.auditapiv2.core.service.manager.dto.FileSearchDto;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.viewMenu.FileMenuProcess;
import com.quanzhi.auditapiv2.web.dto.ZRFileSearchDto;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class ZRFileMenuProcess extends FileMenuProcess {
    String collection = "fileInfo";
    @Override
    public Long countViewMenu(String searchParams, String viewId, String moduleId) throws Exception {
        Query query = new Query();
        if (DataUtil.isNotEmpty(searchParams)) {
            JSONObject jsonObject = JSONObject.parseObject(searchParams);
            ZRFileSearchDto fileSearchDto = jsonObject.toJavaObject(ZRFileSearchDto.class);
            if (DataUtil.isNotEmpty(jsonObject) && jsonObject.containsKey("isAssetAuthorization") && jsonObject.getBoolean("isAssetAuthorization") &&
                    fileSearchDto.getAppUriSet() == null) {
                Set<String> set = new HashSet<>();
                set.add("test*");
                fileSearchDto.setAppUriSet(set);
            }
            Criteria criteria = getCriteria(fileSearchDto);
            query.addCriteria(criteria);
        }
        long count = mongoTemplate.count(query, collection);
        try {
            CaffeineCache.getInstance().put(moduleId + viewId, count);
        } catch (Exception e) {
            return count;
        }
        return count;
    }
    private Criteria getCriteria(ZRFileSearchDto fileSearchDto) {

        Criteria criteria = new Criteria();

        if (DataUtil.isNotEmpty(fileSearchDto)) {
            if (DataUtil.isNotEmpty(fileSearchDto.getFileNameList())) {
                criteria.and(FileSearchDto.FieldEnum.fileNameList.getName()).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(fileSearchDto.getFileNameList())), Pattern.CASE_INSENSITIVE));
            }
            if (DataUtil.isNotEmpty(fileSearchDto.getFileType())) {
                criteria.and(FileSearchDto.FieldEnum.fileType.getName()).in(fileSearchDto.getFileType());
            }
            if (DataUtil.isNotEmpty(fileSearchDto.getAppUriSet())) {
                criteria.and(ZRFileSearchDto.FieldEnum.appUriList.getName()).in(fileSearchDto.getAppUriSet());
            }
            if (DataUtil.isNotEmpty(fileSearchDto.getFileLevel())) {
                criteria.and(FileSearchDto.FieldEnum.fileLevel.getName()).in(fileSearchDto.getFileLevel());
            }
            if (DataUtil.isNotEmpty(fileSearchDto.getDataLabelList())) {
                if (fileSearchDto.getDataLabelList().contains("ALL")) {
                    List<String> dataLabelIds = dataLabelNacosDao.getAll().stream().map(dataLabel -> dataLabel.getId()).collect(Collectors.toList());
                    fileSearchDto.setDataLabelList(dataLabelIds);
                }
//                List<String> list = new ArrayList<>();
//                for (String dataLabelList : fileSearchDto.getDataLabelList()) {
//                    if (dataLabelList.contains(",") && dataLabelList.contains("[")) {
//                        String str = dataLabelList.split(",")[1];
//                        list.add(str.replaceAll("]", "").replaceAll("\"", ""));
//                    }
//                }
//                fileSearchDto.setDataLabelList(list);
                if (FileSearchDto.FieldEnum.dataLabelListOperatorOr.getName().equals(fileSearchDto.getDataLabelListOperator())) {
                    criteria.and(FileSearchDto.FieldEnum.sensitiveDataLabels.getName()).in(fileSearchDto.getDataLabelList());
                } else if (FileSearchDto.FieldEnum.dataLabelListOperatorAnd.getName().equals(fileSearchDto.getDataLabelListOperator())) {
                    criteria.and(FileSearchDto.FieldEnum.sensitiveDataLabels.getName()).all(fileSearchDto.getDataLabelList());
                }
            }
            if (DataUtil.isNotEmpty(fileSearchDto.getFileLen())) {
                criteria.and(FileSearchDto.FieldEnum.fileLen.getName()).gte(fileSearchDto.getFileLen().get(0)).lte(fileSearchDto.getFileLen().get(1));
            }
            if (DataUtil.isNotEmpty(fileSearchDto.getUpdateTime())) {
                criteria.and(FileSearchDto.FieldEnum.timestamp.getName()).gte(fileSearchDto.getUpdateTime().get(0)).lte(fileSearchDto.getUpdateTime().get(1));
            }

        }

        return criteria;
    }
}
