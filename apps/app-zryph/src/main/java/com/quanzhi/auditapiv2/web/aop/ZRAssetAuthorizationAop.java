package com.quanzhi.auditapiv2.web.aop;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.authorization.dto.model.AssetAuthorizationDto;
import com.quanzhi.audit.mix.authorization.service.IAssetAuthorizationService;
import com.quanzhi.auditapiv2.biz.risk.dto.search.ThreatIpSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.ThreatInfoSearchDto;
import com.quanzhi.auditapiv2.web.dto.ZRThreatInfoSearchDto;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;

@Aspect
@Component
public class ZRAssetAuthorizationAop extends AssetAuthorizationAop {
    private Logger logger = LoggerFactory.getLogger(ZRAssetAuthorizationAop.class);
    @Autowired
    private HttpServletRequest request;
    @NacosValue(value = "${isAssetAuthorization:false}", autoRefreshed = true)
    private boolean isAssetAuthorization = false;
    @Autowired
    private IAssetAuthorizationService assetAuthorizationServiceImpl;
    @NacosValue(value = "${openapi.header.username:openapi}", autoRefreshed = true)
    private String username;

    @Pointcut("execution(public * com.quanzhi.auditapiv2.web.controller.ViewMenuController.countViewMenu(..))")
    public void countViewMenu() {
    }

    @Pointcut("execution(public * com.quanzhi.auditapiv2.web.controller.CommonActionController.getActionByConfig(..))")
    public void getActionByConfig() {
    }

    @Pointcut("execution(public * com.quanzhi.auditapiv2.web.controller.ThreatInfoController.listThreatInfo(..))")
    public void listThreatInfo() {
    }

    @Around("listThreatInfo() ")
    public Object toListThreatInfo(ProceedingJoinPoint point) {
        Object object = null;

        try {
            //获取搜索条件参数
            Object[] args = point.getArgs();
            ZRThreatInfoSearchDto infoSearchDto = (ZRThreatInfoSearchDto) args[0];

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("isAssetAuthorization", isAssetAuthorization);

            if (isAssetAuthorization && getOpenApiUsername(request)) {

                //补充资产权限控制参数
                Map<String, Object> map = getAssetAuthorizationList();
                for (String key : map.keySet()) {
                    jsonObject.put(key, map.get(key));
                }
            }
            boolean assetAuthorization = (Boolean) jsonObject.get("isAssetAuthorization");
            if(assetAuthorization){
                Set<String> uriSet = (Set<String>) jsonObject.get("appUriSet");
                if(DataUtil.isNotEmpty(uriSet))
                    infoSearchDto.setAppUriSet(uriSet);
            }
            infoSearchDto.setIsAssetAuthorization(assetAuthorization);
            //执行请求
            args[0] = infoSearchDto;
            object = point.proceed(args);

        } catch (Throwable e) {

            logger.error("资产权限AOP：应用", e);
        }

        return object;
    }

    /**
     * 审计权限控制
     */
    @Around("getActionByConfig()")
    public Object ActionByConfig(ProceedingJoinPoint point) {

        Object object = null;

        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("isAssetAuthorization", isAssetAuthorization);

            if (isAssetAuthorization && getOpenApiUsername(request)) {

                //补充资产权限控制参数
                Map<String, Object> map = getAssetAuthorizationList();
                for (String key : map.keySet()) {

                    jsonObject.put(key, map.get(key));
                }
            }
            // 将允许访问的风险指数存储在当前会话中，以便在方法中使用
            HttpSession session = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest().getSession();
            session.setAttribute("jsonObject", jsonObject);

            object = point.proceed();

        } catch (Throwable e) {

            logger.error("资产权限AOP：应用", e);
        }

        return object;
    }

    @Around("appList() || appGroup() || appExport()|| countViewMenu()")
    public Object app(ProceedingJoinPoint point) {

        Object object = null;

        try {

            //获取搜索条件参数
            Object[] args = point.getArgs();
            Object arg = args[0];

            JSONObject jsonObject = JSONObject.parseObject(arg.toString());
            if (DataUtil.isEmpty(jsonObject)) {
                jsonObject = new JSONObject();
            }

            jsonObject.put("isAssetAuthorization", isAssetAuthorization);

            if (isAssetAuthorization && getOpenApiUsername(request)) {

                //补充资产权限控制参数
                Map<String, Object> map = getAssetAuthorizationList();
                for (String key : map.keySet()) {

                    jsonObject.put(key, map.get(key));
                }
            }

            //执行请求
            args[0] = jsonObject.toJSONString();
            object = point.proceed(args);

        } catch (Throwable e) {

            logger.error("资产权限AOP：应用", e);
        }

        return object;
    }

    @Around("apiList() || apiGroup() || apiExport()")
    public Object api(ProceedingJoinPoint point) {

        Object object = null;

        try {

            //获取搜索条件参数
            Object[] args = point.getArgs();
            String name = point.getSignature().getName();
            if ("getHttpApis".equals(name) || "apiGroup".equals(name)) {

                HttpApiSearchDto httpApiSearchDto = (HttpApiSearchDto) args[0];
                httpApiSearchDto.setIsAssetAuthorization(isAssetAuthorization);

                if (isAssetAuthorization && getOpenApiUsername(request)) {

                    //补充资产权限控制参数
                    Map<String, Object> map = getAssetAuthorizationList();
                    if (DataUtil.isNotEmpty(map.get("appUriSet"))) {

                        httpApiSearchDto.setAppUriSet((Set) map.get("appUriSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("departmentSet"))) {

                        httpApiSearchDto.setDepartmentSet((Set) map.get("departmentSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("isAssetAuthorization"))) {

                        httpApiSearchDto.setIsAssetAuthorization((Boolean) map.get("isAssetAuthorization"));
                    }
                }

                args[0] = httpApiSearchDto;

            } else if ("export".equals(name)) {

                Map<String, Object> requestBody = (Map) args[0];
                String taskType = requestBody.get("taskType").toString();
                if (ExportTaskType.EXPORT_API.name().equals(taskType)) {

                    String json = JSONObject.toJSONString(requestBody.get("httpApiSearchDto"));
                    HttpApiSearchDto httpApiSearchDto = JSONObject.parseObject(json, HttpApiSearchDto.class);
                    httpApiSearchDto.setIsAssetAuthorization(isAssetAuthorization);

                    if (isAssetAuthorization && getOpenApiUsername(request)) {

                        //补充资产权限控制参数
                        Map<String, Object> map = getAssetAuthorizationList();
                        if (DataUtil.isNotEmpty(map.get("appUriSet"))) {

                            httpApiSearchDto.setAppUriSet((Set) map.get("appUriSet"));
                        }

                        if (DataUtil.isNotEmpty(map.get("departmentSet"))) {

                            httpApiSearchDto.setDepartmentSet((Set) map.get("departmentSet"));
                        }

                        if (DataUtil.isNotEmpty(map.get("isAssetAuthorization"))) {

                            httpApiSearchDto.setIsAssetAuthorization((Boolean) map.get("isAssetAuthorization"));
                        }
                    }

                    requestBody.put("httpApiSearchDto", httpApiSearchDto);

                    args[0] = requestBody;
                }
            }

            object = point.proceed(args);

        } catch (Throwable e) {

            logger.error("资产权限AOP：API", e);
        }

        return object;
    }

    @Around("weaknessList() || weaknessGroup() || weaknessExport()")
    public Object weakness(ProceedingJoinPoint point) {

        Object object = null;

        try {

            //获取搜索条件参数
            Object[] args = point.getArgs();
            Object arg = args[0];

            JSONObject jsonObject = JSONObject.parseObject(arg.toString());
            if (DataUtil.isEmpty(jsonObject)) {
                jsonObject = new JSONObject();
            }

            jsonObject.put("isAssetAuthorization", isAssetAuthorization);

            if (isAssetAuthorization && getOpenApiUsername(request)) {

                //补充资产权限控制参数
                Map<String, Object> map = getAssetAuthorizationList();
                for (String key : map.keySet()) {

                    jsonObject.put(key, map.get(key));
                }
            }

            //执行请求
            args[0] = jsonObject.toJSONString();
            object = point.proceed(args);

        } catch (Throwable e) {

            logger.error("资产权限AOP：弱点", e);
        }

        return object;
    }

    @Around("riskList() || riskGroup() || riskExport()")
    public Object risk(ProceedingJoinPoint point) {

        Object object = null;

        try {

            //获取搜索条件参数
            Object[] args = point.getArgs();

            String name = point.getSignature().getName();

            if ("getRiskInfoList".equals(name) || "getRiskInfoGroup".equals(name)) {

                RiskSearchDto riskSearchDto = (RiskSearchDto) args[0];
                riskSearchDto.setIsAssetAuthorization(isAssetAuthorization);

                if (isAssetAuthorization && getOpenApiUsername(request)) {

                    //补充资产权限控制参数
                    Map<String, Object> map = getAssetAuthorizationList();
                    if (DataUtil.isNotEmpty(map.get("appUriSet"))) {

                        riskSearchDto.setAppUriSet((Set) map.get("appUriSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("departmentSet"))) {

                        riskSearchDto.setDepartmentSet((Set) map.get("departmentSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("isAssetAuthorization"))) {

                        riskSearchDto.setIsAssetAuthorization((Boolean) map.get("isAssetAuthorization"));
                    }
                }

                args[0] = riskSearchDto;

            } else if ("export".equals(name)) {

                Map<String, Object> requestBody = (Map) args[0];
                String taskType = requestBody.get("taskType").toString();
                if (ExportTaskType.EXPORT_RISK_INFO.name().equals(taskType)) {

                    String json = JSONObject.toJSONString(requestBody.get("riskSearchDto"));
                    RiskSearchDto riskSearchDto = JSONObject.parseObject(json, RiskSearchDto.class);
                    riskSearchDto.setIsAssetAuthorization(isAssetAuthorization);

                    if (isAssetAuthorization && getOpenApiUsername(request)) {

                        //补充资产权限控制参数
                        Map<String, Object> map = getAssetAuthorizationList();
                        if (DataUtil.isNotEmpty(map.get("appUriSet"))) {

                            riskSearchDto.setAppUriSet((Set) map.get("appUriSet"));
                        }

                        if (DataUtil.isNotEmpty(map.get("departmentSet"))) {

                            riskSearchDto.setDepartmentSet((Set) map.get("departmentSet"));
                        }

                        if (DataUtil.isNotEmpty(map.get("isAssetAuthorization"))) {

                            riskSearchDto.setIsAssetAuthorization((Boolean) map.get("isAssetAuthorization"));
                        }
                    }

                    requestBody.put("riskSearchDto", riskSearchDto);

                    args[0] = requestBody;
                }
            }

            object = point.proceed(args);

        } catch (Throwable e) {

            logger.error("资产权限AOP：API", e);
        }

        return object;
    }

    @Around("threatIpExport()")
    public Object threatIp(ProceedingJoinPoint point) {

        Object object = null;

        try {

            //获取搜索条件参数
            Object[] args = point.getArgs();

            String name = point.getSignature().getName();

            if ("getThreatIpList".equals(name) || "getThreatIpGroup".equals(name)) {

                ThreatIpSearchDto threatIpSearchDto = (ThreatIpSearchDto) args[0];
                threatIpSearchDto.setIsAssetAuthorization(isAssetAuthorization);

                if (isAssetAuthorization && getOpenApiUsername(request)) {

                    //补充资产权限控制参数
                    Map<String, Object> map = getAssetAuthorizationList();
                    if (DataUtil.isNotEmpty(map.get("appUriSet"))) {

                        threatIpSearchDto.setAppUriSet((Set) map.get("appUriSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("departmentSet"))) {

                        threatIpSearchDto.setDepartmentSet((Set) map.get("departmentSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("isAssetAuthorization"))) {

                        threatIpSearchDto.setIsAssetAuthorization((Boolean) map.get("isAssetAuthorization"));
                    }
                }

                args[0] = threatIpSearchDto;

            } else if ("export".equals(name)) {

                Map<String, Object> requestBody = (Map) args[0];
                String taskType = requestBody.get("taskType").toString();
                if (ExportTaskType.EXPORT_THREAT_IP.name().equals(taskType)) {

                    String json = JSONObject.toJSONString(requestBody.get("threatIpSearchDto"));
                    ThreatIpSearchDto threatIpSearchDto = JSONObject.parseObject(json, ThreatIpSearchDto.class);
                    threatIpSearchDto.setIsAssetAuthorization(isAssetAuthorization);

                    if (isAssetAuthorization && getOpenApiUsername(request)) {

                        //补充资产权限控制参数
                        Map<String, Object> map = getAssetAuthorizationList();
                        if (DataUtil.isNotEmpty(map.get("appUriSet"))) {

                            threatIpSearchDto.setAppUriSet((Set) map.get("appUriSet"));
                        }

                        if (DataUtil.isNotEmpty(map.get("departmentSet"))) {

                            threatIpSearchDto.setDepartmentSet((Set) map.get("departmentSet"));
                        }

                        if (DataUtil.isNotEmpty(map.get("isAssetAuthorization"))) {

                            threatIpSearchDto.setIsAssetAuthorization((Boolean) map.get("isAssetAuthorization"));
                        }
                    }

                    requestBody.put("threatIpSearchDto", threatIpSearchDto);

                    args[0] = requestBody;
                }
            }

            object = point.proceed(args);

        } catch (Throwable e) {

            logger.error("资产权限AOP：API", e);
        }

        return object;
    }

    public Boolean getOpenApiUsername(HttpServletRequest request) {
        if (DataUtil.isNotEmpty(username) && username.equals(request.getHeader("username"))) {
            return false;
        }
        return true;
    }

    private Map<String, Object> getAssetAuthorizationList() throws Exception {

        Map<String, Object> map = new HashMap<String, Object>();
        Set<String> appUriSet = new HashSet<>();
        Set<String> departmentSet = new HashSet<String>();

        String username = (String) request.getSession().getAttribute("username");

        if ("webadmin".equals(username)) {

            map.put("isAssetAuthorization", false);
            return map;
        }

        List<AssetAuthorizationDto> list = assetAuthorizationServiceImpl.getAssetAuthorizationByUsername(username);
        for (AssetAuthorizationDto dto : list) {

            if (DataUtil.isNotEmpty(dto.getAppUriList())) {

                appUriSet.addAll(dto.getAppUriList());
            }

            if (DataUtil.isNotEmpty(dto.getDepartmentList())) {

                departmentSet.addAll(dto.getDepartmentList());
            }
        }

        if (DataUtil.isNotEmpty(appUriSet)) {

            map.put("appUriSet", appUriSet);
        } else {
            Set<String> appUri = new HashSet<>();
            appUri.add("test*");
            map.put("appUriSet", appUri);
        }

        if (DataUtil.isNotEmpty(departmentSet)) {

            map.put("departmentSet", departmentSet);
        }

        return map;
    }
}
