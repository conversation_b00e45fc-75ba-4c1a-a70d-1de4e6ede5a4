package com.quanzhi.auditapiv2.web.impl;

import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.auditapiv2.common.dal.dao.convert.AssetLifeStateConfigConvert;
import com.quanzhi.auditapiv2.common.dal.dao.impl.HttpAppDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.Module;
import com.quanzhi.auditapiv2.common.dal.enums.DBOperatorEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.UrlUtil;
import com.quanzhi.metabase.core.model.enums.AssetFlagEnum;
import com.quanzhi.metabase.core.model.http.HttpApiFlagComposite;
import com.quanzhi.metabase.core.model.http.constant.AssetLifeStateEnum;
import com.quanzhi.metabase.core.model.http.constant.LevelEnum;
import com.quanzhi.metabase.core.model.query.Criteria;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class ZRHttpAppDaoImpl extends HttpAppDaoImpl {
    private MetabaseQuery getMetabaseQuery(Map<String, Object> map) {

        MetabaseQuery metabaseQuery = new MetabaseQuery();
        //且或map
        Map<String, String> fieldOperateMap = map.get("fieldOperateMap") == null ? new HashMap<>() : (Map<String, String>) map.get("fieldOperateMap");
        //查询条件
        if (DataUtil.isNotEmpty(map)) {
            try {
                specialQueryFieldConvert.specialQueryFieldConvert(map);
            } catch (Exception e) {
                log.error("specialQueryFieldConvert error", e);
            }
            for (String key : map.keySet()) {

                Object value = map.get(key);
                if (DataUtil.isNotEmpty(value)) {
                    if ("host".equals(key)) {
                        boolean ignoreCase = map.get("hostIgnoreCase") == null ? false : (boolean) map.get("hostIgnoreCase");
                        if (Boolean.TRUE.equals(ignoreCase)) {
                            metabaseQuery.where(key, Predicate.REGEX, Pattern.compile(String.valueOf(value), Pattern.CASE_INSENSITIVE));
                        } else {
                            metabaseQuery.where(key, Predicate.REGEX, Pattern.quote(String.valueOf(value)));
                        }
                    } else if ("name".equals(key)) {
                        String name = String.valueOf(value);
                        if (name.startsWith("http")){
                            // 写了全限定名称，这里将其修改为hots
                            name = UrlUtil.getUrlHost(name);
                        }
                        metabaseQuery.where(key, Predicate.REGEX, Pattern.compile(DataUtil.regexStrEscape(name), Pattern.CASE_INSENSITIVE));
                    } else if ("featureLabels".equals(key)) {
                        metabaseQuery.where(key, Predicate.ALL, value);
                    } else if ("appClassifications".equals(key)) {
                        metabaseQuery.where(key, Predicate.ALL, value);
                    } else if ("visitDomains".equals(key)) {
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            metabaseQuery.where(key, Predicate.IN, value);
                        } else {
                            metabaseQuery.where(key, Predicate.ALL, value);
                        }
                    } else if ("deployDomains".equals(key)) {
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            metabaseQuery.where(key, Predicate.IN, value);
                        } else {
                            metabaseQuery.where(key, Predicate.ALL, value);
                        }
                    } else if ("deployIps".equals(key)) {
                        metabaseQuery.where(key, Predicate.REGEX, value);
                    } else if ("reqDataLabels".equals(key)) {
                        List<String> reqDataLabels = (List<String>) value;
                        Map<String, String> dataLabelMap = (Map<String, String>) map.get("dataLabelMap");
                        boolean isAll = reqDataLabels.get(0).equals("ALL");
                        List<String> allDataLabels = isAll ? dataLabelMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            metabaseQuery.where(key, Predicate.IN, isAll ? allDataLabels : reqDataLabels);
                        } else {
                            metabaseQuery.where(key, Predicate.ALL, isAll ? allDataLabels : reqDataLabels);
                        }
                    } else if ("rspDataLabels".equals(key)) {
                        List<String> rspDataLabels = (List<String>) value;
                        Map<String, String> dataLabelMap = (Map<String, String>) map.get("dataLabelMap");
                        boolean isAll = rspDataLabels.get(0).equals("ALL");
                        List<String> allDataLabels = isAll ? dataLabelMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(fieldOperateMap.get(key))) {
                            metabaseQuery.where(key, Predicate.IN, isAll ? allDataLabels : rspDataLabels);
                        } else {
                            metabaseQuery.where(key, Predicate.ALL, isAll ? allDataLabels : rspDataLabels);
                        }
                    } else if ("followed".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("uri".equals(key)) {
                        metabaseQuery.where(key, Predicate.IS, value);
                    } else if ("departments.department".equals(key)) {
                        metabaseQuery.where(key, Predicate.REGEX, Pattern.quote(String.valueOf(value)));
                    } else if ("restfulFlag".equals(key)) {
                        if (HttpApiFlagComposite.RestfulFlag.RESTFUL_CANCELED == (short) value) {
                            metabaseQuery.where(key, Predicate.NE, value);
                        } else {
                            metabaseQuery.where(key, Predicate.IS, value);
                        }
                    } else if ("sensitiveAppFlag".equals(key)) {
                        if (value instanceof String) {
                            if (String.valueOf(value).equals("1")) {
                                metabaseQuery.where("appStat.sensitiveApiCount", Predicate.GT, 0);
                            } else if (String.valueOf(value).equals("0")) {
                                metabaseQuery.where("appStat.sensitiveApiCount", Predicate.LTE, 0);
                            }
                        }
                    } else if ("delFlag".equals(key)) {
                        metabaseQuery.where("delFlag", Predicate.IS, value);
                    } else if ("appStat.apiCount_gte".equals(key)) {
                        metabaseQuery.where("appStat.apiCount", Predicate.GTE, value);
                    } else if ("appStat.allTypeApiCount_gte".equals(key)) {
                        metabaseQuery.where("appStat.allTypeApiCount", Predicate.GTE, value);
                    } else if ("id".equals(key)) {
                        metabaseQuery.where("_id", Predicate.IS, value);
                    } else if ("ids".equals(key)) {
                        metabaseQuery.where("_id", Predicate.IN, value);
                    } else if ("orderFlag".equals(key)) {
                        metabaseQuery.where("orderFlag", Predicate.IS, value);
                    } else if ("flowSource".equals(key)) {
                        metabaseQuery.where("flowSources", Predicate.IS, value);
                    } else if ("appLifeFlag".equals(key)) {
                        // metabaseQuery.where("appLifeFlag", Predicate.IN, value);
                        List<Object> values = (List<Object>) value;
                        short lifeFlag = Short.parseShort(values.get(0) + "");
                        AssetLifeStateEnum assetLifeStateEnum = AssetLifeStateEnum.valueOfNum(lifeFlag);
                        AssetLifeStateConfigConvert.CriteriaTag criteriaTag = assetLifeStateConfigConvert.getCriteria(AssetLifeStateConfig.AssetTypeEnum.APP, assetLifeStateEnum);
                        metabaseQuery.getCriteria().add(criteriaTag.getMetabaseCriteria());
                    } else if ("weaknessIds".equals(key)) {
                        String operate = fieldOperateMap.get("weaknessIds");
                        List<String> values = (List<String>) value;
                        Map<String, String> weaknessMap = (Map<String, String>) map.get("weaknessMap");
                        boolean isAll = values.get(0).equals("ALL");
                        List<String> allWeakness = isAll ? weaknessMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(operate)) {
                            metabaseQuery.where("appStat.weaknessIds", Predicate.IN, isAll ? allWeakness : values);
                        } else {
                            metabaseQuery.where("appStat.weaknessIds", Predicate.ALL, isAll ? allWeakness : values);
                        }
                    } else if ("riskIds".equals(key)) {
                        String operate = fieldOperateMap.get("riskIds");
                        List<String> values = (List<String>) value;
                        Map<String, String> riskMap = (Map<String, String>) map.get("riskMap");
                        boolean isAll = values.get(0).equals("ALL");
                        List<String> allRisks = isAll ? riskMap.keySet().stream().collect(Collectors.toList()) : new ArrayList<>();
                        if (DBOperatorEnum.OR.operator().equals(operate)) {
                            metabaseQuery.where("appStat.riskIds", Predicate.IN, isAll ? allRisks : values);
                        } else {
                            metabaseQuery.where("appStat.riskIds", Predicate.ALL, isAll ? allRisks : values);
                        }
                    } else if ("showInvalid".equals(key)) {
                        if (Boolean.TRUE.equals(value)) {//展示无效应用
                        } else {
                            metabaseQuery.where("assetFlag", Predicate.NE, AssetFlagEnum.INVALID.getCode());
                        }
                    }
                }
            }

            if (DataUtil.isNotEmpty(map.get("isAssetAuthorization")) && (boolean) map.get("isAssetAuthorization") == true) {

                if (DataUtil.isNotEmpty(map.get("appUriSet")) || DataUtil.isNotEmpty(map.get("departmentSet"))) {

                    Criteria criteria = new Criteria();
                    Criteria criteria1 = null;
                    Criteria criteria2 = null;

                    if (DataUtil.isNotEmpty(map.get("appUriSet"))) {
                        criteria1 = Criteria.where("uri").in(map.get("appUriSet"));
                    }

                    if (DataUtil.isNotEmpty(map.get("departmentSet"))) {
                        criteria2 = Criteria.where("departments.properties.value").in(map.get("departmentSet"));
                    }

                    if (DataUtil.isNotEmpty(criteria1) && DataUtil.isEmpty(criteria2)) {
                        criteria = criteria.orOperator(criteria1);
                    } else if (DataUtil.isEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria = criteria.orOperator(criteria2);
                    } else if (DataUtil.isNotEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                        criteria = criteria.orOperator(criteria1, criteria2);
                    }

                    metabaseQuery.getCriteria().add(criteria);
                } else {
                    metabaseQuery.where("_id", Predicate.IS, "");
                }
            }

            if (DataUtil.isNotEmpty(map.get("level")) || DataUtil.isNotEmpty(map.get("isSensitiveApp"))) {

                Criteria criteria = new Criteria();

                if (DataUtil.isNotEmpty(map.get("level"))) {

                    Criteria criteria1 = Criteria.where("level").in(map.get("level"));
                    criteria = criteria.andOperator(criteria1);
                }

                if (DataUtil.isNotEmpty(map.get("isSensitiveApp"))) {

                    Criteria criteria2 = null;

                    if (String.valueOf(map.get("isSensitiveApp")).equals("true")) {

                        criteria2 = Criteria.where("level").in(new ArrayList<String>() {
                            {
                                add(LevelEnum.HIGH.getKey());
                                add(LevelEnum.MIDDLE.getKey());
                                add(LevelEnum.LOW.getKey());
                            }
                        });
                    } else if (String.valueOf(map.get("isSensitiveApp")).equals("false")) {

                        criteria2 = Criteria.where("level").is(LevelEnum.NON.getKey());
                    }
                    criteria = criteria.andOperator(criteria2);
                }

                metabaseQuery.getCriteria().add(criteria);
            }
            //首次发现时间
            if (DataUtil.isNotEmpty(map.get("discoverTimeStart")) && DataUtil.isNotEmpty(map.get("discoverTimeEnd"))) {
                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("discoverTime").gte(map.get("discoverTimeStart")),
                        Criteria.where("discoverTime").lte(map.get("discoverTimeEnd")));
                metabaseQuery.getCriteria().add(criteria);
            }
            //最近活跃时间
            if (DataUtil.isNotEmpty(map.get("activeTimeStart")) && DataUtil.isNotEmpty(map.get("activeTimeStart"))) {
                Criteria criteria = new Criteria();
                criteria = criteria.andOperator(
                        Criteria.where("activeTime").gte(map.get("activeTimeStart")),
                        Criteria.where("activeTime").lte(map.get("activeTimeEnd")));
                metabaseQuery.getCriteria().add(criteria);
            }
            if (map.get("showFields") != null) {
                metabaseQuery.fields((List<String>) map.get("showFields"));
            }
            Criteria customPropertyCriteria = customPropertySearchAdapter
                    .getMetabaseCriteria(customPropertySearchAdapter
                            .buildCustomProperty(map));
            if (customPropertyCriteria != null) {
                metabaseQuery.getCriteria().add(customPropertyCriteria);
            }
            queryAdapterRegistry.module(Module.APP).query(map, metabaseQuery);
        }
        return metabaseQuery;
    }

}
