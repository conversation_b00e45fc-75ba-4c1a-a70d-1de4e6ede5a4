package com.quanzhi.auditapiv2.web.impl;

import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.auditapiv2.common.dal.dao.impl.IpInfoDaoImpl;
import com.quanzhi.auditapiv2.common.dal.dto.IpSearchDetailDto;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.web.dto.ZRIpSearchDetailDto;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
@Repository
public class ZRIpInfoDaoImpl extends IpInfoDaoImpl {
    private Criteria getCriteria(ZRIpSearchDetailDto ipSearchDetailDto) {
        Criteria criteria = new Criteria();
        if (DataUtil.isNotEmpty(ipSearchDetailDto)) {
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getRiskLevel())) {
                criteria.and(IpSearchDetailDto.FieldEnum.riskLevel.getName()).in(ipSearchDetailDto.getRiskLevel());
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getIp())) {
                criteria.and(IpSearchDetailDto.FieldEnum.ip.getName()).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(ipSearchDetailDto.getIp())), Pattern.CASE_INSENSITIVE));
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getAppSet())) {
                criteria.and(IpSearchDetailDto.FieldEnum.appUriList.getName()).in(ipSearchDetailDto.getAppSet());
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getCity())) {
                Criteria criteria1 = Criteria.where(IpSearchDetailDto.FieldEnum.city.getName()).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(ipSearchDetailDto.getCity())), Pattern.CASE_INSENSITIVE));
                Criteria criteria2 = Criteria.where(IpSearchDetailDto.FieldEnum.country.getName()).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(ipSearchDetailDto.getCity())), Pattern.CASE_INSENSITIVE));
                Criteria criteria3 = Criteria.where(IpSearchDetailDto.FieldEnum.province.getName()).regex(Pattern.compile(DataUtil.regexStrEscape(String.valueOf(ipSearchDetailDto.getCity())), Pattern.CASE_INSENSITIVE));
                criteria.orOperator(criteria1, criteria2, criteria3);
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getBlockFlag())) {
                criteria.and(IpSearchDetailDto.FieldEnum.blockFlag.getName()).is(ipSearchDetailDto.getBlockFlag());
            }
//            if (DataUtil.isNotEmpty(ipSearchDetailDto.getAccessDomainIds())) {
//                if (IpSearchDetailDto.FieldEnum.and.getName().equals(ipSearchDetailDto.getAccessDomainIdsOperator())) {
//                    criteria.and(IpSearchDetailDto.FieldEnum.accessDomainIds.getName()).all(ipSearchDetailDto.getAccessDomainIds());
//                } else {
//                    criteria.and(IpSearchDetailDto.FieldEnum.accessDomainIds.getName()).in(ipSearchDetailDto.getAccessDomainIds());
//                }
//            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getRiskNames())) {
                if (ipSearchDetailDto.getRiskNames().contains("ALL")) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("delFlag").is(false));
                    query.fields().include("name");
                    // 风险事件全部筛选
                    List<Map> riskPolicy = mongoTemplate.find(query, Map.class, "riskPolicy");
                    List<String> riskNames = riskPolicy.stream().filter(i -> i != null && !i.equals("null") && !i.equals("")).map(i -> i.get("name").toString()).collect(Collectors.toList());
                    criteria.and(IpSearchDetailDto.FieldEnum.riskNames.getName()).in(riskNames);
                } else {
                    if (IpSearchDetailDto.FieldEnum.and.getName().equals(ipSearchDetailDto.getRiskNamesOperator())) {
                        criteria.and(IpSearchDetailDto.FieldEnum.riskNames.getName()).all(ipSearchDetailDto.getRiskNames());
                    } else {
                        criteria.and(IpSearchDetailDto.FieldEnum.riskNames.getName()).in(ipSearchDetailDto.getRiskNames());
                    }
                }
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getUaTypes())) {
                if (IpSearchDetailDto.FieldEnum.and.getName().equals(ipSearchDetailDto.getUaTypesOperator())) {
                    criteria.and(IpSearchDetailDto.FieldEnum.uaTypes.getName()).all(ipSearchDetailDto.getUaTypes());
                } else {
                    criteria.and(IpSearchDetailDto.FieldEnum.uaTypes.getName()).in(ipSearchDetailDto.getUaTypes());
                }
            }
            if (DataUtil.isNotEmpty(ipSearchDetailDto.getRspDataLabelList())) {
                if (ipSearchDetailDto.getRspDataLabelList().contains("ALL")) {
                    List<String> dataLabelIds = dataLabelNacosDao.getAll().stream().map(dataLabel -> dataLabel.getId()).collect(Collectors.toList());
                    ipSearchDetailDto.setRspDataLabelList(dataLabelIds);
                }
                if (IpSearchDetailDto.FieldEnum.and.getName().equals(ipSearchDetailDto.getRspDataLabelListOperator())) {
                    criteria.and(IpSearchDetailDto.FieldEnum.rspDataLabelList.getName()).all(ipSearchDetailDto.getRspDataLabelList());
                } else {
                    criteria.and(IpSearchDetailDto.FieldEnum.rspDataLabelList.getName()).in(ipSearchDetailDto.getRspDataLabelList());
                }
            }
            if (com.quanzhi.auditapiv2.common.util.utils.DataUtil.isNotEmpty(ipSearchDetailDto.getFirstDate())) {
                String startDate = DateUtil.format(ipSearchDetailDto.getFirstDate().get(0), "yyyyMMdd");
                String endDate = DateUtil.format(ipSearchDetailDto.getFirstDate().get(1), "yyyyMMdd");
                criteria.and(IpSearchDetailDto.FieldEnum.firstDate.getName())
                        .gte(startDate)
                        .lte(endDate);
            }
            if (com.quanzhi.auditapiv2.common.util.utils.DataUtil.isNotEmpty(ipSearchDetailDto.getLastDate())) {
                String startDate = DateUtil.format(ipSearchDetailDto.getLastDate().get(0), "yyyyMMdd");
                String endDate = DateUtil.format(ipSearchDetailDto.getLastDate().get(1), "yyyyMMdd");
                criteria.and(IpSearchDetailDto.FieldEnum.lastDate.getName())
                        .gte(startDate)
                        .lte(endDate);
            }
        }
        return criteria;
    }
}
