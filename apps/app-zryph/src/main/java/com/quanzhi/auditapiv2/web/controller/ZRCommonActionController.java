package com.quanzhi.auditapiv2.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.util.utils.CommonActionCheckUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.model.event.ActionEvent;
import com.quanzhi.operate.atomDefinition.ActionConfig;
import com.quanzhi.operate.atomDefinition.RepositoryOperate;
import com.quanzhi.operate.query.Criteria;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/api/commonAction")
@Slf4j
public class ZRCommonActionController extends CommonActionController{
    @PostMapping("/getActionByConfig.do")
    public String getActionByConfig(@RequestBody ActionConfig actionConfig, HttpServletRequest request) {
        try {
            sysLogService.insertCommonApiLog(actionConfig.getId());
        } catch (Exception e) {
            log.error("日志写入错误,error:{}", e.getMessage());
        }
        // 从当前会话中获取允许访问的风险指数
        HttpSession session = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest().getSession();
        JSONObject jsonObject = (JSONObject) session.getAttribute("jsonObject");
        this.convertQuery(actionConfig);

        Map<String, Object> objectMap = CommonActionCheckUtil.checkIP(actionConfig);
        if (objectMap != null  && !objectMap.isEmpty()) {
            if(objectMap.get("errorMsg") != null){
                return HttpResponseUtil.error(400, objectMap.get("errorMsg").toString());
            }else if(objectMap.get("emptyValue") != null){
                return HttpResponseUtil.success(objectMap.get("emptyValue"));
            }
        }
        boolean flag = true;
        if((actionConfig.getId().equals("APP_DATA") && actionConfig.getRspKeyMap().get("app") != null) ||
                (actionConfig.getId().equals("API_HIGH_WEAKNESS_TYPE") && actionConfig.getRspKeyMap().get("typeList") != null)){
            flag = false;
        }
        if (Boolean.parseBoolean(jsonObject.get("isAssetAuthorization").toString())) {
            if(flag){
                Set<String> appUriSet = (Set<String>) jsonObject.get("appUriSet");
                Set<String> departmentSet = (Set<String>) jsonObject.get("departmentSet");
                List<RepositoryOperate> repositoryOperateList = actionConfig.getRepositoryOperateList();
                if (DataUtil.isNotEmpty(repositoryOperateList)) {
                    for (RepositoryOperate repositoryOperate : repositoryOperateList) {
                        List<Criteria> criteriaList = repositoryOperate.getFrontCriteriaList();
                        Criteria criteria = new Criteria();
                        if (DataUtil.isNotEmpty(appUriSet) || DataUtil.isNotEmpty(departmentSet)) {

                            if (DataUtil.isNotEmpty(appUriSet)) {
                                Criteria criteria1 = Criteria.where("appUriList").in(appUriSet);
                                criteria = criteria.orOperator(criteria1);
                            }
                            if (DataUtil.isNotEmpty(departmentSet)) {
                                Criteria criteria2 = Criteria.where("departments.department").in(departmentSet);
                                criteria = criteria.orOperator(criteria2);
                            }

                        } else {
                            if (DataUtil.isEmpty(appUriSet)) {
                                appUriSet = new HashSet<>();
                                appUriSet.add("test");
                                Criteria criteria1 = Criteria.where("appUriList").in(appUriSet);
                                criteria = criteria.orOperator(criteria1);
                            }
                        }
                        criteriaList.add(criteria);
                    }
                }
            }
        }
        Map<String, Object> result = operateHandlerService.handleActions(actionConfig, request);
        this.fillInfo(actionConfig, result);

        eventPublisher.publishEvent(new ActionEvent(actionConfig));

        return HttpResponseUtil.success(result);
    }
}
