package com.quanzhi.auditapiv2.web.impl;

import com.alibaba.fastjson.JSONObject;

import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.cache.CaffeineCache;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.viewMenu.AccountViewMenuProcess;
import com.quanzhi.auditapiv2.web.dto.ZRAccountSearchDto;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

@Service

public class ZRAccountViewMenuProcess extends AccountViewMenuProcess {
    public ZRAccountViewMenuProcess(ZRAccountInfoDaoImpl  accountInfoDao) {
        super(accountInfoDao);
    }
    @Override
    public Long countViewMenu(String searchParams, String viewId, String moduleId) throws Exception {
        long count = 0L;
        ZRAccountSearchDto accountSearchDto = JSONObject.parseObject(searchParams, ZRAccountSearchDto.class);
        JSONObject object = JSONObject.parseObject(searchParams);
        if (DataUtil.isNotEmpty(object)) {
            Object aa = object.get("appUriSet");
            Set<String> set = new HashSet<>();
            if (DataUtil.isNotEmpty(aa)) {
                String string = aa.toString();
                if (string.charAt(0) == '[' && string.charAt(string.length() - 1) == ']')
                    string = string.substring(1, string.length() - 1);
                String[] split = string.split(",");
                for (int i = 0; i < split.length; i++) split[i] = split[i].replace("\"", "");
                for (int i = 0; i < split.length; i++) {
                    set.add(split[i]);
                }
                accountSearchDto.setAppUriSet(set);
            } else {
                if ((boolean) object.get("isAssetAuthorization")) {
                    set.add("test*");
                    accountSearchDto.setAppUriSet(set);
                }
            }
        }
        count = accountInfoDao.totalCount(accountSearchDto);
        try {
            CaffeineCache.getInstance().put(moduleId + viewId, count);
        } catch (Exception e) {
            return count;
        }
        return count;
    }
}
