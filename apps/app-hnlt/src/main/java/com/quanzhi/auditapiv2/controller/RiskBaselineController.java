package com.quanzhi.auditapiv2.controller;

import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.entity.RiskBaseline;
import com.quanzhi.auditapiv2.service.ActiveBaselineService;
import com.quanzhi.auditapiv2.service.IRiskBaselineService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/riskBaseline")
@Slf4j
public class RiskBaselineController {

    private Logger logger = LoggerFactory.getLogger(RiskBaselineController.class);
    @Autowired
    private IRiskBaselineService iRiskBaselineService;

    @Autowired
    private ActiveBaselineService activeBaselineService;

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ApiOperation(value = "获取风险基线")
    public ResponseVo<ListOutputDto<RiskBaseline>> listBaseline() {
        try{
            return ResponseVo.ok(iRiskBaselineService.listRiskBaseline());
        }catch (Exception e){
            logger.error("获取风险基线失败",e);
            return ResponseVo.error(500,"获取风险基线失败");
        }
    }

    @RequestMapping(value = "detail", method = RequestMethod.GET)
    @ApiOperation(value = "获取风险基线")
    public ResponseVo<RiskBaseline> detailBaseline(String id) {
        try{
            return ResponseVo.ok(iRiskBaselineService.detailRiskBaseline(id));
        }catch (Exception e){
            logger.error("获取风险基线失败",e);
            return ResponseVo.error(500,"获取风险基线失败");
        }
    }

    @RequestMapping(value = "updateBaseline", method = RequestMethod.GET)
    @ApiOperation(value = "内部更新基线范围")
    public void updateBaseline() {
        activeBaselineService.save();
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改风险基线")
    public ResponseVo<Boolean> editBaseline(@RequestBody RiskBaseline riskBaseline) {
        try{
            iRiskBaselineService.updateRiskBaseline(riskBaseline);
            return ResponseVo.ok(true);
        }catch (Exception e){
            logger.error("修改风险基线失败",e);
            return ResponseVo.error(500,"修改风险基线异常");
        }
    }
}
