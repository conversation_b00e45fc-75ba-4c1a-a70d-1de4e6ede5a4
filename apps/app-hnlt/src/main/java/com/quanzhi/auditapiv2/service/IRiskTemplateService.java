package com.quanzhi.auditapiv2.service;

import com.quanzhi.auditapiv2.common.dal.entity.RiskTemplate;
import com.quanzhi.auditapiv2.core.service.manager.web.IBaseNacosService;
import com.quanzhi.auditapiv2.entity.RiskTemplateSearch;

public interface IRiskTemplateService extends IBaseNacosService<RiskTemplate> {
    /**
     * 修改auditapiv2.riskTemplate.json nacos配置
     */
    void updateRiskTemplate(RiskTemplateSearch riskTemplateSearch);


}
