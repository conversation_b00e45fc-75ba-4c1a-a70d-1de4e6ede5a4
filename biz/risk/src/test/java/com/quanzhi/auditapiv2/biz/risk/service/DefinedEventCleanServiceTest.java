package com.quanzhi.auditapiv2.biz.risk.service;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * @Author: HaoJun
 * @Date: 2021/8/14 11:02 上午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = BootstrappingTest.class)
@WebAppConfiguration
@ActiveProfiles("test")
@Ignore
public class DefinedEventCleanServiceTest {

    @Autowired
    private DefinedEventCleanService definedEventCleanService;

    @Test
    public void testClean(){
        definedEventCleanService.clear();
    }
}
