package com.quanzhi.auditapiv2.biz.risk.service.aggRisk;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.auditapiv2.biz.risk.constant.RiskConstant;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskCountDto;
import com.quanzhi.auditapiv2.biz.risk.dto.export.AggRiskExportDto;
import com.quanzhi.auditapiv2.biz.risk.service.RiskPolicyAllowListService;
import com.quanzhi.auditapiv2.common.dal.dao.node.ClusterNodeDao;
import com.quanzhi.auditapiv2.common.dal.dto.AttackInfoDto;
import com.quanzhi.auditapiv2.common.dal.dto.ExporTitleFieldDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.*;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.data.DataInfoDto;
import com.quanzhi.auditapiv2.common.dal.entity.IpCountEntity;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskOperationLog;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import com.quanzhi.auditapiv2.common.dal.entity.securityPosture.BigScreenConfig;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.DesensitizationPairDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DesensitizationUtil;
import com.quanzhi.auditapiv2.common.util.utils.request.HttpServletRequestUtil;
import com.quanzhi.auditapiv2.core.model.event.RiskChangedEvent;
import com.quanzhi.auditapiv2.core.risk.dto.RiskV2Dto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicyAllowList;
import com.quanzhi.auditapiv2.core.risk.entity.RiskSample;
import com.quanzhi.auditapiv2.core.risk.repository.RiskSampleRepository;
import com.quanzhi.auditapiv2.core.risk.repository.aggRisk.AggRiskDao;
import com.quanzhi.auditapiv2.core.risk.repository.aggRisk.AggRiskOperationLogDao;
import com.quanzhi.auditapiv2.core.service.NacosDataServiceBuilder;
import com.quanzhi.auditapiv2.core.service.manager.web.*;
import com.quanzhi.auditapiv2.core.service.manager.web.data.DataService;
import com.quanzhi.auditapiv2.core.service.manager.web.securityPosture.IBigScreenConfigService;
import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.http.Pair;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.bson.Document;
import org.bson.json.Converter;
import org.bson.json.JsonWriterSettings;
import org.bson.json.StrictJsonWriter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: yangzx
 * @Date: 2024/10/9 14:37
 */
@Service
@Slf4j
public class AggRiskService {

    @NacosValue(value = "${auditapiv2.export.singleCount:100}", autoRefreshed = true)
    private Integer singleCount;

    @NacosValue(value = "${auditapiv2.export.maxCount:1000000}", autoRefreshed = true)
    private Integer maxCount;

    private final String collectionName = "aggRiskInfo";

    private final AggRiskDao aggRiskDao;

    private final RiskPolicyV2Service riskPolicyV2Service;

    private final AggRiskOperationLogDao aggRiskOperationLogDao;

    private final ClusterNodeDao clusterNodeDao;

    private final NacosDataServiceBuilder nacosDadaServiceBuilder;

    private final MongoTemplate mongoTemplate;

    private final RiskPolicyAllowListService riskPolicyAllowListService;

    private final IApiWeaknessService apiWeaknessService;

    private final IIpInfoService ipInfoService;

    private final IHttpApiService httpApiService;

    private final IHttpAppService httpAppService;

    private final IAccountInfoService accountInfoService;

    private final DataService dataService;

    private final RiskV2Service riskV2Service;

    private final IBigScreenConfigService bigScreenConfigService;

    private final ApplicationEventPublisher eventPublisher;

    private final RiskSampleRepository riskSampleRepository;

    public AggRiskService(AggRiskDao aggRiskDao, RiskPolicyV2Service riskPolicyV2Service, AggRiskOperationLogDao aggRiskOperationLogDao, ClusterNodeDao clusterNodeDao, NacosDataServiceBuilder nacosDadaServiceBuilder, MongoTemplate mongoTemplate, RiskPolicyAllowListService riskPolicyAllowListService, IApiWeaknessService apiWeaknessService, IIpInfoService ipInfoService, IHttpApiService httpApiService, IHttpAppService httpAppService, IAccountInfoService accountInfoService, DataService dataService, RiskV2Service riskV2Service, IBigScreenConfigService bigScreenConfigService, ApplicationEventPublisher eventPublisher, RiskSampleRepository riskSampleRepository) {
        this.aggRiskDao = aggRiskDao;
        this.riskPolicyV2Service = riskPolicyV2Service;
        this.aggRiskOperationLogDao = aggRiskOperationLogDao;
        this.clusterNodeDao = clusterNodeDao;
        this.nacosDadaServiceBuilder = nacosDadaServiceBuilder;
        this.mongoTemplate = mongoTemplate;
        this.riskPolicyAllowListService = riskPolicyAllowListService;
        this.apiWeaknessService = apiWeaknessService;
        this.ipInfoService = ipInfoService;
        this.httpApiService = httpApiService;
        this.httpAppService = httpAppService;
        this.accountInfoService = accountInfoService;
        this.dataService = dataService;
        this.riskV2Service = riskV2Service;
        this.bigScreenConfigService = bigScreenConfigService;
        this.eventPublisher = eventPublisher;
        this.riskSampleRepository = riskSampleRepository;
    }

    /**
     * 查新风险清单
     *
     * @param aggRiskOperatorDto
     * @return
     * @throws Exception
     */
    public ListOutputDto<AggRiskDto> listAggRisk(AggRiskOperatorDto aggRiskOperatorDto) throws Exception {
        List<AggRiskInfo> aggRiskInfos = aggRiskDao.listAggRisk(aggRiskOperatorDto);
        List<AggRiskDto> result = new ArrayList<>(aggRiskInfos.size());
        Map<String, DataLabel> dataLabelMap = nacosDadaServiceBuilder.getDataLable(true).build().getDataLabelMap();
        Map<String, String> nodeMap = clusterNodeDao.findAll().stream().collect(Collectors.toMap(ClusterNode::getNid, ClusterNode::getName, (k1, k2) -> k1));
        for (AggRiskInfo aggRiskInfo : aggRiskInfos) {
            AggRiskDto aggRiskDto = AggRiskDto.AggRiskDtoMapper.INSTANCE.convert(aggRiskInfo);
            fillInfo(aggRiskDto, dataLabelMap, nodeMap);
            result.add(aggRiskDto);
        }
        Long count = aggRiskDao.totalCount(aggRiskOperatorDto);
        ListOutputDto<AggRiskDto> data = new ListOutputDto<>();
        data.setTotalCount(count);
        data.setRows(result);
        return data;
    }

    private void fillInfo(AggRiskDto aggRiskDto, Map<String, DataLabel> dataLabelMap, Map<String, String> nodeMap) {
        //替换数据主体描述
        if (DataUtil.isNotEmpty(dataLabelMap) && DataUtil.isNotEmpty(aggRiskDto) && aggRiskDto.getEntityType() != null && aggRiskDto.getEntityType().contains("data")) {
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : aggRiskDto.getEntities()) {
                if ("DATA".equals(entity.getType()) && dataLabelMap.containsKey(entity.getValue())) {
                    String name = dataLabelMap.get(entity.getValue()).getName();
                    String newDesc = aggRiskDto.getDesc().replaceFirst(entity.getValue(), name);
                    aggRiskDto.setDesc(newDesc);
                }
            }
        }
        //填充节点信息
        if (DataUtil.isNotEmpty(aggRiskDto.getNodes())) {
            for (ResourceEntity.Node node : aggRiskDto.getNodes()) {
                if (nodeMap.containsKey(node.getNid())) {
                    node.setNname(nodeMap.get(node.getNid()));
                }
            }
        }
    }

    /**
     * 风险事件分组
     *
     * @param aggRiskOperatorDto
     * @return
     * @throws Exception
     */
    public List<CommonGroupDto> groupAggRisk(AggRiskOperatorDto aggRiskOperatorDto) throws Exception {
        String groupField = aggRiskOperatorDto.getGroupField();
        List<CommonGroupDto> commonGroupDtoList = new ArrayList<>();
        GroupDto groupDto = new GroupDto();
        groupDto.setGroupFields(new String[]{groupField});
        groupDto.setUnwind(groupField);
        groupDto.setAggrType(GroupDto.GroupTypeEnum.COUNT.getName());
        try {
            List<AggregationDto> aggRiskInfoGroup = aggRiskDao.groupAggRisk(aggRiskOperatorDto, groupDto);
            aggRiskInfoGroup.forEach(e -> {
                if (AggRiskOperatorDto.GroupFieldEnum.RISK_LEVEL.getName().equals(groupField)) {
                    // 等级
                    Integer level = Integer.valueOf(e.getId());
                    String name = AggRiskInfo.RiskLevelEnum.getRiskLevelEnum(level).getName();
                    commonGroupDtoList.add(CommonGroupDto.builder().name(name).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                } else if (AggRiskOperatorDto.GroupFieldEnum.RISK_MARK.getName().equals(groupField)) {
                    // 置顶
                    String markState = e.getId();
                    String name = "true".equals(markState) ? "已置顶" : "未置顶";
                    commonGroupDtoList.add(CommonGroupDto.builder().name(name).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                } else if (AggRiskOperatorDto.GroupFieldEnum.RISK_STATE.getName().equals(groupField)) {
                    // 状态
                    Integer state = Integer.valueOf(e.getId());
                    String name = AggRiskInfo.RiskStateEnum.getRiskStateEnum(state).getName();
                    commonGroupDtoList.add(CommonGroupDto.builder().name(name).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                } else {
                    commonGroupDtoList.add(CommonGroupDto.builder().name(e.getId()).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                }
            });
        } catch (Exception exception) {
            log.error("风险事件分组出错:", exception);
        }

        // 排序，保证结果顺序一致
        Map<Long, List<CommonGroupDto>> collect = commonGroupDtoList.stream().collect(Collectors.groupingBy(CommonGroupDto::getCount));
        Set<Long> sortKey = new TreeSet<>(Long::compareTo);
        sortKey.addAll(collect.keySet());
        commonGroupDtoList.clear();
        for (Long key : sortKey) {
            if (collect.get(key).size() > 1) {
                collect.get(key).sort(Comparator.comparing(CommonGroupDto::getName));
                commonGroupDtoList.addAll(collect.get(key));
            } else {
                commonGroupDtoList.add(collect.get(key).get(0));
            }
        }
        Collections.reverse(commonGroupDtoList);

        // 等级反序
        if (AggRiskOperatorDto.GroupFieldEnum.RISK_LEVEL.getName().equals(groupField)) {
            commonGroupDtoList.sort(Comparator.comparingInt(o -> Integer.parseInt(o.getId())));
            Collections.reverse(commonGroupDtoList);
        }

        return commonGroupDtoList;
    }

    /**
     * 置顶风险
     *
     * @param aggRiskOperatorDto
     * @return
     */
    public String markAggRisk(AggRiskOperatorDto aggRiskOperatorDto) {
        return aggRiskDao.markAggRisk(aggRiskOperatorDto);
    }

    /**
     * 批量置顶风险
     *
     * @return
     */
    public String batchMarkAggRisk(BatchAggRiskOperatorDto batchAggRiskOperatorDto) {
        return aggRiskDao.batchMarkAggRisk(batchAggRiskOperatorDto);
    }

    /**
     * 导出风险
     *
     * @param aggRiskExportDto
     * @param filePath
     * @return
     * @throws Exception
     */
    public String exportAggRiskInfo(AggRiskExportDto aggRiskExportDto, String filePath) throws Exception {
        // 获取中文列头
        List<String> titles = new ArrayList<String>();
        Map<String, DataLabel> dataLabelMap = nacosDadaServiceBuilder.getDataLable(true).build().getDataLabelMap();
        for (ExporTitleFieldDto exporTitleFieldDto : aggRiskExportDto.getList()) {
            if (exporTitleFieldDto.getField().equals("customField")) {
                // 自定义字段
                titles.addAll(exporTitleFieldDto.getCustomFieldList());
            } else {
                titles.add(exporTitleFieldDto.getTitle());
            }
        }
        // 获取搜索dto
        AggRiskOperatorDto aggRiskOperatorDto = aggRiskExportDto.getAggRiskOperatorDto();
        if (DataUtil.isEmpty(aggRiskOperatorDto)) {
            log.warn("search params is empty,no export");
            return filePath;
        }
        // 获取导出id
        List<String> exportIds = new ArrayList<>();
        if (aggRiskExportDto.getIds() != null) {
            exportIds = aggRiskExportDto.getIds();
        }
        Map<String, String> riskEntityTypeMap = riskPolicyV2Service.getRiskEntityTypeMap();
        // 节点信息
        Map<String, String> nodeMap = clusterNodeDao.findAll().stream().collect(Collectors.toMap(ClusterNode::getNid, ClusterNode::getName, (k1, k2) -> k1));
        CSVFormat format = CSVFormat.DEFAULT.withHeader(titles.toArray(new String[titles.size()]));
        try (FileOutputStream fos = new FileOutputStream(filePath); OutputStreamWriter osw = new OutputStreamWriter(fos, "GBK"); CSVPrinter printer = new CSVPrinter(osw, format);) {
            Long count = aggRiskDao.totalCount(aggRiskOperatorDto);
            if (count == 0 && exportIds.isEmpty()) {
                log.info("query result is empty,no export");
                printer.flush();
                return filePath;
            }

            Query query = new Query();
            if (!exportIds.isEmpty()) {
                query.addCriteria(Criteria.where("_id").in(exportIds));
            } else {
                query.addCriteria(aggRiskDao.getCriteria(aggRiskOperatorDto));
            }

            MongoCursor<Document> cursor = null;
            try {
                cursor = mongoTemplate.getCollection(collectionName)
                        .find(query.getQueryObject())
                        .noCursorTimeout(true).batchSize(singleCount).limit(maxCount).cursor();
                JsonWriterSettings settings = JsonWriterSettings.builder().int64Converter(new Converter<Long>() {

                    public void convert(Long value, StrictJsonWriter writer) {
                        writer.writeNumber(value.toString());
                    }
                }).build();
                List<List<String>> rows = new ArrayList<>(singleCount);
                while (cursor.hasNext()) {
                    AggRiskInfo aggRiskInfo = JSONObject.parseObject(cursor.next().toJson(settings), AggRiskInfo.class);
                    AggRiskDto aggRiskDto = AggRiskDto.AggRiskDtoMapper.INSTANCE.convert(aggRiskInfo);
                    fillInfo(aggRiskDto, dataLabelMap, nodeMap);
                    if (DataUtil.isNotEmpty(aggRiskDto)) {
                        List<String> items = getRow(aggRiskDto, aggRiskExportDto, riskEntityTypeMap, dataLabelMap);
                        rows.add(items);
                    }
                    if (rows.size() >= singleCount) {
                        for (List<String> items : rows) {
                            if (DataUtil.isNotEmpty(items) && !items.isEmpty()) {
                                printer.printRecord(items.stream().map(e -> "\t" + e + "\t").collect(Collectors.toList()));
                            }
                        }
                        rows.clear();
                    }
                }
                // 最后发送剩余数据
                if (!rows.isEmpty()) {
                    for (List<String> items : rows) {
                        if (DataUtil.isNotEmpty(items) && !items.isEmpty()) {
                            printer.printRecord(items.stream().map(e -> "\t" + e + "\t").collect(Collectors.toList()));
                        }
                    }
                }
                printer.flush();
            } catch (Exception e) {
                log.error("exportAggRiskInfo error:", e);
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
        } catch (IOException e) {
            log.error("exportAggRiskInfo error:", e);
        }
        return filePath;
    }

    public List<String> getRow(AggRiskDto aggRiskDto, AggRiskExportDto aggRiskExportDto, Map<String, String> riskEntityTypeMap, Map<String, DataLabel> dataLabelMap) throws Exception {

        List<String> row = new ArrayList<String>();
        for (ExporTitleFieldDto exporTitleFieldDto : aggRiskExportDto.getList()) {
            // 风险标识
            if ("riskMark".equals(exporTitleFieldDto.getField())) {
                row.add(DataUtil.isEmpty(aggRiskDto.getRiskMark()) ? "未置顶" : aggRiskDto.getRiskMark() ? "已置顶" : "未置顶");
            }
            // 风险时间
            if ("firstTime".equals(exporTitleFieldDto.getField())) {
                row.add(com.quanzhi.auditapiv2.common.util.utils.DateUtil.format(aggRiskDto.getFirstTime()));
            }
            // 风险时间
            if ("lastTime".equals(exporTitleFieldDto.getField())) {
                row.add(com.quanzhi.auditapiv2.common.util.utils.DateUtil.format(aggRiskDto.getLastTime()));
            }
            // 风险描述
            if ("desc".equals(exporTitleFieldDto.getField())) {
                row.add(DataUtil.isEmpty(aggRiskDto.getDesc()) ? "--" : aggRiskDto.getDesc().replaceAll("<h>", "").replaceAll("</h>", ""));
            }
            // 来源节点
            if ("nodes".equals(exporTitleFieldDto.getField())) {
                if (aggRiskDto.getNodes() != null) {
                    List<String> nodeNames = new ArrayList<>();
                    for (ResourceEntity.Node node : aggRiskDto.getNodes()) {
                        if (node.getNname() != null) {
                            nodeNames.add(node.getNname());
                        }
                    }
                    if (nodeNames.isEmpty()) {
                        row.add("");
                    } else {
                        row.add(String.join(",", nodeNames));
                    }
                } else {
                    row.add("");
                }

            }
            // 风险名称
            if ("name".equals(exporTitleFieldDto.getField())) {
                row.add(aggRiskDto.getName());
            }
            // 应用
            if ("host".equals(exporTitleFieldDto.getField())) {
                row.add(DataUtil.isEmpty(aggRiskDto.getHost()) ? "--" : String.join(",", aggRiskDto.getHost()));
            }
            // 应用名称
            if ("appName".equals(exporTitleFieldDto.getField())) {
                row.add(DataUtil.isEmpty(aggRiskDto.getAppName()) ? "--" : String.join(",", aggRiskDto.getAppName()));
            }
            // 处理建议
            if ("remark".equals(exporTitleFieldDto.getField())) {
                row.add(aggRiskDto.getRemark() == null || aggRiskDto.getRemark().isEmpty() ? "--" : aggRiskDto.getRemark());
            }
            // 风险类型
            if ("type".equals(exporTitleFieldDto.getField())) {
                row.add(aggRiskDto.getType());
            }
            // 风险主体
            if ("entityValue".equals(exporTitleFieldDto.getField())) {
                List<String> entities = new ArrayList<>();
                for (RiskInfo.Entity entity : aggRiskDto.getEntities()) {
                    if (entity.getType().equals("DATA") && dataLabelMap.containsKey(entity.getValue())) {
                        String name = dataLabelMap.get(entity.getValue()).getName();
                        entity.setValue(name);
                    }
                    entities.add(entity.getName() + "：" + entity.getValue().replaceAll("httpapi:", "").replaceAll("httpapp:", "").split("@")[0]);
                }
                row.add(DataUtil.isEmpty(aggRiskDto.getEntities()) ? "--" : String.join(",", entities));
            }
            // 主体类型
            if ("entityType".equals(exporTitleFieldDto.getField())) {
                if (DataUtil.isNotEmpty(aggRiskDto.getEntityType())) {
                    String type = aggRiskDto.getEntityType();
                    row.add(riskEntityTypeMap.getOrDefault(type, "--"));
                } else {
                    List<String> entityType = new ArrayList<>();
                    for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : aggRiskDto.getEntities()) {
                        entityType.add(entity.getName());
                    }
                    row.add(DataUtil.isEmpty(entityType) ? "--" : String.join(",", entityType));
                }
            }
            // 风险等级
            if ("level".equals(exporTitleFieldDto.getField())) {
                row.add(aggRiskDto.getLevelName());
            }
            // 风险状态
            if ("state".equals(exporTitleFieldDto.getField())) {
                row.add(aggRiskDto.getStateName());
            }
            // 风险运营ID
            if ("operationId".equals(exporTitleFieldDto.getField())) {
                row.add(aggRiskDto.getOperationId());
            }
            //自定义字段
            if ("customField".equals(exporTitleFieldDto.getField())) {

                Map<String, String> pairMap = new HashMap<String, String>();

                if (DataUtil.isNotEmpty(aggRiskDto.getDepartments())) {

                    HttpAppResource.Department department = aggRiskDto.getDepartments().get(0);

                    if (DataUtil.isNotEmpty(department.getProperties())) {

                        for (Pair<String> pair : department.getProperties()) {

                            pairMap.put(pair.getKey(), pair.getValue());
                        }
                    }
                }

                for (String customField : exporTitleFieldDto.getCustomFieldList()) {
                    row.add(pairMap.get(customField) == null ? "" : pairMap.get(customField));
                }
            }
        }

        return row;

    }

    public String dealAggRisk(AggRiskOperatorDto aggRiskOperatorDto) throws Exception {
        AggRiskInfo aggRiskInfo = aggRiskDao.findById(aggRiskOperatorDto.getId());
        StringBuilder option = new StringBuilder();
        if (DataUtil.isNotEmpty(aggRiskInfo)) {
            AggRiskOperationLog aggRiskOperationLog = buildLog(aggRiskInfo, aggRiskOperatorDto.getState(), aggRiskOperatorDto.getOperateName(), aggRiskOperatorDto.getRemark());
            aggRiskOperationLogDao.insertLog(aggRiskOperationLog);
        }
        ignoreRiskInfo(aggRiskInfo.getId(), aggRiskOperatorDto.getOperateName(), aggRiskOperatorDto.getRemark(), aggRiskOperatorDto.getState());
        List<String> ignoreList = aggRiskOperatorDto.getIgnoreList();
        if (aggRiskOperatorDto.getState().get(0) == 1 && DataUtil.isNotEmpty(ignoreList)) {
            for (String value : ignoreList) {
                List<AggRiskInfo> aggRiskInfos = aggRiskDao.findByEntityValue(value, aggRiskOperatorDto.getPolicyId());
                for (AggRiskInfo riskInfo : aggRiskInfos) {
                    AggRiskOperationLog aggRiskOperationLog = buildLog(riskInfo, aggRiskOperatorDto.getState(), aggRiskOperatorDto.getOperateName(), aggRiskOperatorDto.getRemark());
                    aggRiskOperationLogDao.insertLog(aggRiskOperationLog);
                    ignoreRiskInfo(riskInfo.getId(), aggRiskOperatorDto.getOperateName(), aggRiskOperatorDto.getRemark(), aggRiskOperatorDto.getState());
                }
                aggRiskDao.ignoreByEntity(value, aggRiskOperatorDto.getRemark(), aggRiskOperatorDto.getPolicyId());
            }
            option.append("按照\"").append(String.join(",", ignoreList)).append("\"进行忽略；");
        }
        Map<String, String> whiteMap = aggRiskOperatorDto.getWhiteMap();
        if (DataUtil.isNotEmpty(whiteMap)) {
            List<RiskPolicyAllowList> datas = new ArrayList<>();
            for (String type : whiteMap.keySet()) {
                //检查是否已经添加白名单
                RiskPolicyAllowList oldPolicyAllow = riskPolicyAllowListService.getRiskPolicyAllowList(aggRiskInfo.getPolicyId(), type, whiteMap.get(type));
                if (DataUtil.isEmpty(oldPolicyAllow)) {
                    //添加异常策略白名单
                    RiskPolicyAllowList riskPolicyAllowList = new RiskPolicyAllowList();
                    riskPolicyAllowList.setPolicyId(aggRiskInfo.getPolicyId());
                    riskPolicyAllowList.setValue(whiteMap.get(type));
                    riskPolicyAllowList.setType(type);
                    datas.add(riskPolicyAllowList);
                }
            }
            option.append("按照\"").append(String.join(",", whiteMap.keySet())).append("\"进行加白；");
            riskPolicyAllowListService.addRiskPolicyAllowList(datas);
        }
        eventPublisher.publishEvent(new RiskChangedEvent(aggRiskInfo, option.toString()));
        return aggRiskDao.dealAggRisk(aggRiskOperatorDto);
    }

    public void ignoreRiskInfo(String aggRiskId, String operateName, String remark, List<Integer> state) throws Exception {
        //忽略关联异常
        BatchRiskV2OperatorDto batchRiskV2OperatorDto = new BatchRiskV2OperatorDto();
        batchRiskV2OperatorDto.setAggRiskId(aggRiskId);
        batchRiskV2OperatorDto.setOperateName(operateName);
        batchRiskV2OperatorDto.setRemark(remark);
        batchRiskV2OperatorDto.setState(state);
        riskV2Service.batchDealRiskInfo(batchRiskV2OperatorDto);
    }

    public String batchDealAggRisk(BatchAggRiskOperatorDto batchAggRiskOperatorDto) throws Exception {
        List<AggRiskInfo> aggRiskInfos = new ArrayList<>();
        if (DataUtil.isNotEmpty(batchAggRiskOperatorDto.getIds())) {
            aggRiskInfos = aggRiskDao.findByIds(batchAggRiskOperatorDto.getIds());
        } else {
            AggRiskOperatorDto aggRiskOperatorDto = batchAggRiskOperatorDto.getAggRiskOperatorDto();
            aggRiskInfos = aggRiskDao.listAggRisk(aggRiskOperatorDto);
        }
        if (!aggRiskInfos.isEmpty()) {
            for (AggRiskInfo aggRiskInfo : aggRiskInfos) {
                AggRiskOperationLog aggRiskOperationLog = buildLog(aggRiskInfo, batchAggRiskOperatorDto.getState(), batchAggRiskOperatorDto.getOperateName(), batchAggRiskOperatorDto.getRemark());
                aggRiskOperationLogDao.insertLog(aggRiskOperationLog);
                ignoreRiskInfo(aggRiskInfo.getId(), batchAggRiskOperatorDto.getOperateName(), batchAggRiskOperatorDto.getRemark(), batchAggRiskOperatorDto.getState());
                eventPublisher.publishEvent(new RiskChangedEvent(aggRiskInfo));
            }
        }
        return aggRiskDao.batchDealAggRisk(batchAggRiskOperatorDto);
    }

    public List<AggRiskOperationLogDto> listLog(String aggRiskId) {
        List<AggRiskOperationLogDto> result = new ArrayList<>();
        List<AggRiskOperationLog> aggRiskOperationLogs = aggRiskOperationLogDao.listLog(aggRiskId);
        for (AggRiskOperationLog aggRiskOperationLog : aggRiskOperationLogs) {
            AggRiskOperationLogDto operationLogDto = AggRiskOperationLogDto.AggRiskOperationLogDtoMapper.INSTANCE.convert(aggRiskOperationLog);
            operationLogDto.setOldStateName(AggRiskInfo.RiskStateEnum.getRiskStateEnum(operationLogDto.getOldState()).getName());
            operationLogDto.setNewStateName(AggRiskInfo.RiskStateEnum.getRiskStateEnum(operationLogDto.getNewState()).getName());
            result.add(operationLogDto);
        }
        return result;
    }

    private AggRiskOperationLog buildLog(AggRiskInfo aggRiskInfo, List<Integer> state, String operateName, String remark) {
        if (DataUtil.isNotEmpty(operateName)) {
            operateName = HttpServletRequestUtil.getUserName();
        }
        AggRiskOperationLog aggRiskOperationLog = new AggRiskOperationLog();
        aggRiskOperationLog.setRiskId(aggRiskInfo.getId());
        aggRiskOperationLog.setOperateName(operateName);
        aggRiskOperationLog.setOldState(aggRiskInfo.getState());
        aggRiskOperationLog.setNewState(state.get(0));
        aggRiskOperationLog.setOperateTime(System.currentTimeMillis());
        aggRiskOperationLog.setRemark(remark);
        return aggRiskOperationLog;
    }

    /**
     * 查询风险关联弱点信息
     *
     * @param apiUri
     * @return
     */
    public AggRiskWeakDto getRelateWeakName(String apiUri) {
        List<ApiWeakness> weaknesses = apiWeaknessService.getByApiUri(apiUri, "REPAIRING");
        List<String> names = new ArrayList<>();
        weaknesses.forEach(weak -> {
            names.add(weak.getName());
        });
        AggRiskWeakDto aggRiskWeakDto = new AggRiskWeakDto();
        aggRiskWeakDto.setWeakNames(names);
        return aggRiskWeakDto;
    }

    /**
     * 获取风险详情
     *
     * @param id
     * @return
     */
    public AggRiskDto getAggRiskInfo(String id) throws Exception {
        AggRiskInfo aggRiskInfo = aggRiskDao.findById(id);
        AggRiskDto aggRiskDto = AggRiskDto.AggRiskDtoMapper.INSTANCE.convert(aggRiskInfo);
        Map<String, DataLabel> dataLabelMap = nacosDadaServiceBuilder.getDataLable(true).build().getDataLabelMap();
        Map<String, String> nodeMap = clusterNodeDao.findAll().stream().collect(Collectors.toMap(ClusterNode::getNid, ClusterNode::getName, (k1, k2) -> k1));
        fillInfo(aggRiskDto, dataLabelMap, nodeMap);
        // 获取加白结果
        Map<String, String> whiteMap = new HashMap<>();
        List<String> entityValues = aggRiskInfo.getEntities().stream().map(RiskInfo.Entity::getValue).collect(Collectors.toList());
        List<RiskPolicyAllowList> riskPolicyAllowLists = riskPolicyAllowListService.getByPolicyId(aggRiskDto.getPolicyId());
        for (RiskPolicyAllowList riskPolicyAllowList : riskPolicyAllowLists) {
            if (entityValues.contains(riskPolicyAllowList.getValue())) {
                whiteMap.put(riskPolicyAllowList.getType(), riskPolicyAllowList.getValue());
            }
        }
        aggRiskDto.setWhiteMap(whiteMap);
        //登录弱密码风险，提取样例的弱密码账号&密码
        if ("1007".equals(aggRiskDto.getPolicyId())) {
            List<RiskSample> riskSamples = riskSampleRepository.listSampleByAggRiskId(aggRiskDto.getId());
            if (DataUtil.isNotEmpty(riskSamples)) {
                List<AggRiskDto.WeakUP> accountPasswordList = new ArrayList<>();
                Set<String> accountList = new HashSet<>();
                riskSamples.forEach(riskSample -> {
                    if (riskSample.getHttpEvent() != null && riskSample.getHttpEvent().getLoginInfo() != null) {
                        String account = riskSample.getHttpEvent().getLoginInfo().getAccount();
                        String password = riskSample.getHttpEvent().getLoginInfo().getPassword();
                        if (DataUtil.isNotEmpty(account) && DataUtil.isNotEmpty(password) && !accountList.contains(account)) {
                            DesensitizationPairDto dto = DesensitizationUtil.desensitize(password);
                            AggRiskDto.WeakUP weakUP = new AggRiskDto.WeakUP();
                            weakUP.setDesensitizationKey(dto.getKey());
                            weakUP.setPassword(dto.getValue());
                            weakUP.setAccount(account);
                            accountPasswordList.add(weakUP);
                            accountList.add(account);
                        }
                    }
                });
                aggRiskDto.setAccountPasswordList(accountPasswordList);
            }
        }

        return aggRiskDto;
    }

    /**
     * 获取风险主体信息
     *
     * @param entity
     * @return
     * @throws Exception
     */
    public List<JSONObject> getEntityInfo(RiskInfo.Entity entity) throws Exception {
        List<JSONObject> result = new ArrayList<>();
        switch (entity.getType()) {
            case "IP":
                AttackInfoDto ipInfo = ipInfoService.getAttackInfo(entity.getValue());
                result.add((JSONObject) JSON.toJSON(ipInfo));
                break;
            case "ACCOUNT":
                AttackInfoDto accountInfo = accountInfoService.getAttackInfo(entity.getValue());
                result.add((JSONObject) JSON.toJSON(accountInfo));
                break;
            case "API":
                HttpApiDto httpApiDto = httpApiService.getHttpApiDtoByUri(entity.getValue());
                if (DataUtil.isEmpty(httpApiDto)) {
                    JSONObject api = new JSONObject();
                    api.put("unKnow", true);
                    api.put("apiUrl", entity.getValue().replace("httpapi:", "").split("@")[0]);
                    result.add(api);
                } else {
                    result.add((JSONObject) JSON.toJSON(httpApiDto));
                }
                break;
            case "APP":
                HttpAppDto httpApp = httpAppService.getHttpAppByUri(entity.getValue());
                if (DataUtil.isEmpty(httpApp)) {
                    JSONObject app = new JSONObject();
                    app.put("unKnow", true);
                    app.put("host", entity.getValue().replace("httpapp:", "").split("@")[0]);
                    result.add(app);
                } else {
                    result.add((JSONObject) JSON.toJSON(httpApp));
                }
                break;
            case "DATA":
                DataInfoDto dataInfo = dataService.getDataInfoByDataId(entity.getValue());
                if (DataUtil.isEmpty(dataInfo)) {
                    JSONObject data = new JSONObject();
                    data.put("unKnow", true);
                    data.put("dataName", entity.getValue());
                    result.add(data);
                } else {
                    result.add((JSONObject) JSON.toJSON(dataInfo));
                }
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 获取风险关联异常信息
     *
     * @return
     * @throws Exception
     */
    public ListOutputDto<RiskV2Dto> getRiskInfoList(AggRiskOperatorDto aggRiskOperatorDto) throws Exception {
        RiskV2OperatorDto riskV2OperatorDto = RiskV2OperatorDto.RiskV2OperatorDtoMapper.INSTANCE.convert(aggRiskOperatorDto);
        riskV2OperatorDto.setAggRiskId(aggRiskOperatorDto.getId());
        ListOutputDto<RiskV2Dto> riskV2DtoListOutputDto = riskV2Service.listRiskInfo(riskV2OperatorDto);
        return riskV2DtoListOutputDto;
    }

    public Long totalCount() {
        AggRiskOperatorDto aggRiskOperatorDto = new AggRiskOperatorDto();
        return aggRiskDao.totalCount(aggRiskOperatorDto);
    }

    public void upsertAggRiskInfo(AggRiskInfo aggRiskInfo) {
        aggRiskDao.upsertAggRiskInfo(aggRiskInfo);
    }

    public AggRiskInfo getByAggRiskKey(String aggRiskKey) {
        return aggRiskDao.getByAggRiskKey(aggRiskKey);
    }

    public List<AggRiskInfo> getAll() {
        return aggRiskDao.getAllAggRisk();
    }

    public List<AggRiskInfo> getByDataId(String dataId) {
        return aggRiskDao.getByDataId(dataId);
    }

    public List<Long> getStatistics() {
        return aggRiskDao.getStatistics();
    }

    public List<AggRiskInfo> listAggRiskInfoByDate() throws Exception {
        BigScreenConfig bigScreenConfig = bigScreenConfigService.selectBigScreenConfig();
        List<String> domains = new ArrayList<>();
        for (int i = 0; i < bigScreenConfig.getNetworkOne().getMonitorDomain().size(); i++) {
            String newAppUri = "httpapp:" + bigScreenConfig.getNetworkOne().getMonitorDomain().get(i);
            domains.add(newAppUri);
        }
        for (int i = 0; i < bigScreenConfig.getNetworkTwo().getMonitorDomain().size(); i++) {
            String newAppUri = "httpapp:" + bigScreenConfig.getNetworkTwo().getMonitorDomain().get(i);
            domains.add(newAppUri);
        }
        Calendar calendar = Calendar.getInstance();
        // 获取今天的日期和时间
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);

        Date todayEndTime = calendar.getTime();
        long endTime = todayEndTime.getTime();
        long startTime = endTime - 1000 * 60 * 60 * 24 * 3;
        return aggRiskDao.listAggRiskInfoByDate(startTime, endTime, domains);
    }

    public List<RiskCountDto> getAggRiskTrend(int days) {
        //初始化
        List<RiskCountDto> resultList = initTrendList(days);

        try {
            for (int i = 0; i < resultList.size(); i++) {
                String startDate = resultList.get(i).getDate();
                long startTime = com.quanzhi.auditapiv2.common.util.utils.DateUtil.getDateZeroTimestamp(com.quanzhi.auditapiv2.common.util.utils.DateUtil.stringToDate(startDate));
                long endTime = com.quanzhi.auditapiv2.common.util.utils.DateUtil.getDateLastTimestamp(com.quanzhi.auditapiv2.common.util.utils.DateUtil.stringToDate(startDate));
                AggRiskOperatorDto aggRiskOperatorDto = new AggRiskOperatorDto();
                aggRiskOperatorDto.setFirstTimeStart(startTime);
                aggRiskOperatorDto.setFirstTimeEnd(endTime);
                List<Integer> states = new ArrayList<>(2);
                states.add(com.quanzhi.auditapiv2.core.risk.entity.RiskInfo.RiskStateEnum.HAS_HANDLE.getState());
                states.add(com.quanzhi.auditapiv2.core.risk.entity.RiskInfo.RiskStateEnum.NOT_HANDLE.getState());
                aggRiskOperatorDto.setState(states);
                for (int i1 = 1; i1 <= 3; i1++) {
                    aggRiskOperatorDto.setLevel(Arrays.asList(i1));
                    Long count = aggRiskDao.totalCount(aggRiskOperatorDto);
                    switch (i1) {
                        case 1:
                            resultList.get(i).setLowRiskNum(count);
                            break;
                        case 2:
                            resultList.get(i).setMidRiskNum(count);
                            break;
                        case 3:
                            resultList.get(i).setHighRiskNum(count);
                            break;
                        default:
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取态势大屏异常趋势出错：" + e.getMessage());
        }

        return resultList;
    }

    private List<RiskCountDto> initTrendList(int beforeDay) {
        List<RiskCountDto> list = new ArrayList<>();
        DateTime startDate = DateUtil.offsetDay(new Date(), 0 - beforeDay);
        List<DateTime> dateTimes = DateUtil.rangeToList(startDate, new Date(), DateField.DAY_OF_WEEK);
        for (DateTime dateTime : dateTimes) {
            RiskCountDto riskCountDto = RiskCountDto.builder().date(DateUtil.date(dateTime).toString("yyyyMMdd")).highRiskNum(0L).midRiskNum(0L).lowRiskNum(0L).build();
            list.add(riskCountDto);
        }
        return list;
    }

    public List<IpCountEntity> getRiskCountryDistribute(List<Integer> state, List<String> appUris) {
        return aggRiskDao.getRiskDistributeByIp(state, appUris);
    }

    public List<IpCountEntity> getRiskCountryDistributeByDataReveal(List<String> appUris) {
        return aggRiskDao.getRiskCountryDistributeByDataReveal(RiskConstant.DATA_REVEAL_IP_RISK, appUris);
    }

    public long getIpConfirmRiskNum(String ip, List<String> appUris) {
        AggRiskOperatorDto aggRiskOperatorDto = new AggRiskOperatorDto();
        aggRiskOperatorDto.setEntityValue(ip);
        aggRiskOperatorDto.setState(Arrays.asList(com.quanzhi.auditapiv2.core.risk.entity.RiskInfo.RiskStateEnum.HAS_HANDLE.getState()));
        aggRiskOperatorDto.setName(RiskConstant.DATA_REVEAL_IP_RISK);
        aggRiskOperatorDto.setAppUris(appUris);

        long count = aggRiskDao.totalCount(aggRiskOperatorDto);
        return count;
    }

    public Set<String> selectThreatIp(Integer count, Integer limit) {
        List<AggRiskInfo> ipRisks = aggRiskDao.selectIpRisk(count, limit);
        Set<String> threatIps = new HashSet<>();
        for (AggRiskInfo ipRisk : ipRisks) {
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : ipRisk.getEntities()) {
                if (entity.getType().equals("IP")) {
                    threatIps.add(entity.getValue());
                    break;
                }
            }
        }
        return threatIps;
    }

    public Set<String> selectThreatAccount(Integer count, Integer limit) {
        List<AggRiskInfo> accountRisks = aggRiskDao.selectAccountRisk(count, limit);
        Set<String> threatAccounts = new HashSet<>();
        for (AggRiskInfo accountRisk : accountRisks) {
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : accountRisk.getEntities()) {
                if (entity.getType().equals("ACCOUNT")) {
                    threatAccounts.add(entity.getValue());
                    break;
                }
            }
        }
        return threatAccounts;
    }

    public Set<String> selectThreatApi(Integer count, Integer limit) {
        List<AggRiskInfo> apiRisks = aggRiskDao.selectApiRisk(count, limit);
        Set<String> threatApis = new HashSet<>();
        for (AggRiskInfo apiRisk : apiRisks) {
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : apiRisk.getEntities()) {
                if (entity.getType().equals("API")) {
                    threatApis.add(entity.getValue());
                    break;
                }
            }
        }
        return threatApis;
    }

    public Set<String> selectThreatApp(Integer count, Integer limit) {
        List<AggRiskInfo> appRisks = aggRiskDao.selectAppRisk(count, limit);
        Set<String> threatApps = new HashSet<>();
        for (AggRiskInfo appRisk : appRisks) {
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : appRisk.getEntities()) {
                if (entity.getType().equals("APP")) {
                    threatApps.add(entity.getValue());
                    break;
                }
            }
        }
        return threatApps;
    }

    public void simpleIgnore(String policyId, String type, String value, Boolean isAddWhiteList, String operateName) throws Exception {
        List<AggRiskInfo> aggRiskInfos = mongoTemplate.find(new Query().addCriteria(
                Criteria.where("entities.type").is(type)
                        .and("entities.value").is(value)
                        .and("policyId").is(policyId)
        ), AggRiskInfo.class, collectionName);
        if (!aggRiskInfos.isEmpty()) {
            for (AggRiskInfo aggRiskInfo : aggRiskInfos) {
                //添加日志
                AggRiskOperationLog aggRiskOperationLog = buildLog(aggRiskInfo, Collections.singletonList(com.quanzhi.auditapiv2.core.risk.entity.RiskInfo.RiskStateEnum.HAS_IGNORE.getState()), operateName, null);
                aggRiskOperationLogDao.insertLog(aggRiskOperationLog);
                //忽略异常
                ignoreRiskInfo(aggRiskInfo.getId(), operateName, null, Collections.singletonList(com.quanzhi.auditapiv2.core.risk.entity.RiskInfo.RiskStateEnum.HAS_IGNORE.getState()));
                //通知
                eventPublisher.publishEvent(new RiskChangedEvent(aggRiskInfo));
            }
        }
        //忽略所有关联风险
        Update update = new Update();
        update.set("state", com.quanzhi.auditapiv2.core.risk.entity.RiskInfo.RiskStateEnum.HAS_IGNORE.getState());
        update.set("operateName", operateName);
        update.set("operateTime", System.currentTimeMillis());
        aggRiskDao.ignoreByEntityValue(policyId, value, null, operateName);
        if (isAddWhiteList) {
            //检查是否已经添加白名单
            RiskPolicyAllowList oldPolicyAllow = riskPolicyAllowListService.getRiskPolicyAllowList(policyId, type, value);
            if (DataUtil.isEmpty(oldPolicyAllow)) {
                //添加异常策略白名单
                RiskPolicyAllowList riskPolicyAllowList = new RiskPolicyAllowList();
                riskPolicyAllowList.setPolicyId(policyId);
                riskPolicyAllowList.setType(type);
                riskPolicyAllowList.setValue(value);
                riskPolicyAllowListService.addRiskPolicyAllowList(Collections.singletonList(riskPolicyAllowList));
            } else {
                throw new Exception("已存在排除名单中！");
            }
        }
    }

}
