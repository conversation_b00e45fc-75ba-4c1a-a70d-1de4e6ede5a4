package com.quanzhi.auditapiv2.biz.risk.export;

import com.quanzhi.auditapiv2.biz.risk.dto.export.AggRiskExportDto;
import com.quanzhi.auditapiv2.common.dal.entity.ExportTaskModel;
import com.quanzhi.auditapiv2.common.util.constant.ConfigContants;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;
import com.quanzhi.auditapiv2.core.service.manager.export.AbstractExportTaskRunner;
import com.quanzhi.auditapiv2.core.service.manager.export.TaskRunnerException;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.AggRiskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AggRiskExportTaskRunner extends AbstractExportTaskRunner<AggRiskExportDto> {

    @Autowired
    private AggRiskService aggRiskService;


    public AggRiskExportTaskRunner() {
        super(ExportTaskType.EXPORT_AGGRISK_INFO);
    }

    @Override
    public void runTask(ExportTaskModel taskModel, AggRiskExportDto aggRiskExportDto, String workDir) throws TaskRunnerException {

        try {
            aggRiskService.exportAggRiskInfo(
                    aggRiskExportDto,
                    workDir + "/" + ConfigContants.exportPathMap.get(ExportTaskType.EXPORT_AGGRISK_INFO.name()));
        } catch (Exception e) {
            throw new TaskRunnerException(e);
        }
    }

    @Override
    public Class<AggRiskExportDto> getParamsClass() {
        return AggRiskExportDto.class;
    }


}