package com.quanzhi.auditapiv2.biz.risk.dto.export;

import com.quanzhi.auditapiv2.biz.risk.dto.search.ThreatIpSearchDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * create at 2021/8/20 上午
 * @description: 威胁ip导出dto
 **/
@Data
@ToString(callSuper = true)
public class ThreatIpExportDto extends CommonExportDto {

    private ThreatIpSearchDto threatIpSearchDto;

    private String applicant_id;
    private String tags;
    private String reasons;
    private String usage;

}