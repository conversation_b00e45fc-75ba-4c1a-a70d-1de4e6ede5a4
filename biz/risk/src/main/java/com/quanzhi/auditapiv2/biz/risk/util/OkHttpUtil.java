package com.quanzhi.auditapiv2.biz.risk.util;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;

/**
 * @author: yangzixian
 * @date: 2023.09.20 11:34
 * @description:
 */
@Slf4j
public class OkHttpUtil {

    public static OkHttpClient getUnSafeClient() {
        try {
            // 创建一个自定义的信任管理器，该管理器跳过SSL证书验证
            TrustManager[] trustAllCertificates = new TrustManager[]{
                    new X509TrustManager() {
                        public X509Certificate[] getAcceptedIssuers() {
                            return new X509Certificate[0];
                        }

                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        }

                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        }
                    }
            };

            // 创建一个SSL上下文，使用自定义的信任管理器
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCertificates, new java.security.SecureRandom());

            // 创建OkHttpClient并设置SSL上下文

            return new OkHttpClient.Builder()
                    .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCertificates[0])
                    .hostnameVerifier((hostname, session) -> true) // 简单的主机名验证，始终返回true
                    .build();
        } catch (Exception e) {
            log.error("初始化okHttp出错：", e);
        }
        return null;
    }

    public static OkHttpClient getSafeClient() {
        return new OkHttpClient();
    }

}
