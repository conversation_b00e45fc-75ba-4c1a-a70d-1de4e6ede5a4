package com.quanzhi.auditapiv2.biz.risk.service;


import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.enums.EntityTypeEnum;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.audit_core.common.model.NetworkDomain;
import com.quanzhi.audit_core.common.model.NetworkSegment;
import com.quanzhi.audit_core.common.risk.Expression;
import com.quanzhi.audit_core.common.risk.Policy;
import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.audit_core.resource.fetcher.client.fetcher.IpNetworkDomainFetcher;
import com.quanzhi.audit_core.resource.fetcher.client.resolve.convert.NetworkSegmentConverter;
import com.quanzhi.audit_core.resource.fetcher.client.resolve.model.NetworkSegmentIpInfo;
import com.quanzhi.auditapiv2.biz.risk.constant.RiskConstant;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskCountDto;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskDescTemplate;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskIngoresDto;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskThreatIpInfoDto;
import com.quanzhi.auditapiv2.biz.risk.dto.export.RiskInfoExportDto;
import com.quanzhi.auditapiv2.common.dal.dao.IRiskCountryCountDao;
import com.quanzhi.auditapiv2.common.dal.dao.node.ClusterNodeDao;
import com.quanzhi.auditapiv2.common.dal.dto.AttackInfoDto;
import com.quanzhi.auditapiv2.common.dal.dto.ExporTitleFieldDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.risk.SystemRiskTrendDto;
import com.quanzhi.auditapiv2.common.dal.entity.CountEntity;
import com.quanzhi.auditapiv2.common.dal.entity.IpCountEntity;
import com.quanzhi.auditapiv2.common.dal.entity.RiskCountryCount;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import com.quanzhi.auditapiv2.common.dal.entity.securityPosture.BigScreenConfig;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.dto.ResponseVo;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.MD5Util;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import com.quanzhi.auditapiv2.common.util.utils.push.WphFileGatewayDto;
import com.quanzhi.auditapiv2.common.util.utils.request.HttpServletRequestUtil;
import com.quanzhi.auditapiv2.core.model.event.AppNameChangeEvent;
import com.quanzhi.auditapiv2.core.model.event.RiskChangToIpEvent;
import com.quanzhi.auditapiv2.core.model.event.RiskChangedEvent;
import com.quanzhi.auditapiv2.core.model.event.RiskMarkActionEvent;
import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.risk.dto.RiskPolicyAllowListDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.GroupDataDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskDescribeDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEvent;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskOperationLog;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicyAllowList;
import com.quanzhi.auditapiv2.core.risk.entity.RiskSample;
import com.quanzhi.auditapiv2.core.risk.entity.RiskTrend;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskInfoDao;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskOperationLogDao;
import com.quanzhi.auditapiv2.core.risk.repository.RiskSampleRepository;
import com.quanzhi.auditapiv2.core.service.NacosDataServiceBuilder;
import com.quanzhi.auditapiv2.core.service.impl.search.RiskSearchExecutor;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpApiService;
import com.quanzhi.auditapiv2.core.service.manager.web.push.WphSendService;
import com.quanzhi.auditapiv2.core.service.manager.web.securityPosture.IBigScreenConfigService;
import com.quanzhi.dsl.syntax.VariableType;
import com.quanzhi.metabase.core.model.ResourceEntity;
import com.quanzhi.metabase.core.model.enums.OrderFlagEnum;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.http.Pair;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import com.quanzhi.metabase.core.model.query.ResourceUpdates;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.bson.Document;
import org.bson.json.Converter;
import org.bson.json.JsonWriterSettings;
import org.bson.json.StrictJsonWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;


/**
 * 《异常事件业务层》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Service
@Slf4j
public class RiskInfoService implements RiskSearchExecutor.RiskSearcher {
    private Logger logger = LoggerFactory.getLogger(RiskInfoService.class);

    @Autowired
    private IRiskInfoDao riskInfoDaoImpl;

    private final ClusterNodeDao clusterNodeDao;

    @NacosValue(value = "${auditapiv2.export.singleCount:100}", autoRefreshed = true)
    private Integer singleCount;

    @NacosValue(value = "${auditapiv2.export.maxCount:1000000}", autoRefreshed = true)
    private Integer maxCount;

    @Autowired
    private RiskPolicyAllowListService riskPolicyAllowListService;

    @Autowired
    private IHttpApiService httpApiServiceImpl;

    @Autowired
    private ThreatIpService threatIpService;

    @Autowired
    private RiskThreatPortraitService riskThreatPortraitService;

    @Autowired
    private DefinedEventService definedEventService;

    @Autowired
    private IRiskCountryCountDao riskCountryCountDao;

    @Autowired
    private IBigScreenConfigService bigScreenConfigService;

    @DynamicValue(dataId = "auditapiv2.risk.template.json", groupId = "auditapiv2", typeClz = RiskDescTemplate.class)
    private List<RiskDescTemplate> riskDescTemplateList;

    @DynamicValue(dataId = "common.networksegment.json", groupId = "common", typeClz = NetworkSegment.class)
    private List<NetworkSegment> networkSegments;

    @Autowired
    private IRiskOperationLogDao riskOperationLogDao;

//    @Autowired
//    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private RiskSampleRepository riskSampleRepository;

    //nacos缓存数据
    @Autowired
    private NacosDataServiceBuilder nacosDadaServiceBuilder;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    @Autowired
    private WphSendService wphSendService;

    private final MongoTemplate mongoTemplate;

    private final String collectionName = "riskInfo";

    private static long TIME = 1000 * 60 * 60 * 24 * 7;

    public RiskInfoService(ClusterNodeDao clusterNodeDao, MongoTemplate mongoTemplate) {
        this.clusterNodeDao = clusterNodeDao;
        this.mongoTemplate = mongoTemplate;
    }

    public AttackInfoDto getSessionAttackInfo(String id) {
        RiskInfo riskInfo = riskInfoDaoImpl.getById(id);
        AttackInfoDto attackInfoDto = new AttackInfoDto();
        if (riskInfo != null) {
            Set<String> ips = new HashSet<>();
            Set<String> accounts = new HashSet<>();
            List<RiskSample> riskSample = riskSampleRepository.getAllByCriteria(new Criteria().and("riskId").is(riskInfo.getId()), Arrays.asList("httpEvent.ip", "httpEvent.account"));
            for (RiskSample sample : riskSample) {
                if (sample.getHttpEvent() != null && sample.getHttpEvent().getIp() != null) {
                    ips.add(sample.getHttpEvent().getIp());
                } else if (sample.getHttpEvent() != null && sample.getHttpEvent().getAccount() != null) {
                    accounts.add(sample.getHttpEvent().getAccount());
                }
            }
            attackInfoDto.setAccountNum(accounts.size());
            attackInfoDto.setIpNum(ips.size());
        }
        return attackInfoDto;
    }

    /**
     * 获取异常最新的一条操作记录
     *
     * @param riskId
     * @return
     */
    public RiskOperationLog getLastRiskOperationLog(String riskId) {
        List<RiskOperationLog> riskOperationLogs = riskOperationLogDao.getAllByCriteria(Criteria.where("riskId").is(riskId));
        RiskOperationLog riskOperationLog = new RiskOperationLog();
        if (riskOperationLogs != null && riskOperationLogs.size() > 0) {
            riskOperationLog = riskOperationLogs.stream().sorted(Comparator.comparing(RiskOperationLog::getCreateTime)).collect(Collectors.toList()).get(0);
        } else {
            RiskInfo riskInfo = riskInfoDaoImpl.getById(riskId);
            riskOperationLog.setRiskId(riskId);
            riskOperationLog.setOperatorName("系统");
            riskOperationLog.setDetailLog("系统将异常状态改为待确认");
            riskOperationLog.setCreateTime(riskInfo.getFirstTime());
        }
        return riskOperationLog;
    }

    public void simpleIgnore(String id, String type, String value, Boolean isAddWhiteList, String operateName) throws Exception {
        RiskInfo riskInfo = riskInfoDaoImpl.getById(id);
        //忽略所有关联异常
        Update update = new Update();
        update.set("state", RiskInfo.RiskStateEnum.HAS_IGNORE.getState());
        update.set("operateName", operateName);
        riskInfoDaoImpl.updateMulti(Criteria.where("policySnapshot.name").is(riskInfo.getPolicySnapshot().getName()).orOperator(Criteria.where("entities.value").regex(value), Criteria.where("channels.value").regex(value)), update);
        if (isAddWhiteList) {
            //检查是否已经添加白名单
            RiskPolicyAllowList oldPolicyAllow = riskPolicyAllowListService.getRiskPolicyAllowList(riskInfo.getPolicySnapshot().getId(), type, value);
            if (DataUtil.isEmpty(oldPolicyAllow)) {
                //添加异常策略白名单
                RiskPolicyAllowList riskPolicyAllowList = new RiskPolicyAllowList();
                riskPolicyAllowList.setPolicyId(riskInfo.getPolicySnapshot().getId());
                riskPolicyAllowList.setType(type);
                riskPolicyAllowList.setValue(value);
                riskPolicyAllowListService.addRiskPolicyAllowList(Collections.singletonList(riskPolicyAllowList));
            } else {
                throw new Exception("已存在排除名单中！");
            }
        }
    }

    public void countSampleThreatInfo(String riskId) {
        RiskInfo riskInfo = riskInfoDaoImpl.getById(riskId);
        if (riskInfo == null) {
            return;
        }

        //获取该异常对应的样例
        List<RiskSample> riskSample = riskSampleRepository.getAllByCriteria(new Criteria().and("riskId").is(riskId), Arrays.asList("httpEvent.ip"));

        //获取样例的IP集合
        List<String> ips = new ArrayList<>();
        //获取样例的账号集合
        for (RiskSample sample : riskSample) {
            if (sample != null && sample.getHttpEvent() != null && sample.getHttpEvent().getIp() != null) {
                if (!ips.contains(sample.getHttpEvent().getIp())) {
                    ips.add(sample.getHttpEvent().getIp());
                }
            }
        }
        if (riskInfo.getEntities() != null && riskInfo.getEntities().size() > 0) {
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : riskInfo.getEntities()) {
                if (entity.getType() != null && "IP".equals(entity.getType())) {
                    if (!ips.contains(entity.getValue())) {
                        ips.add(entity.getValue());
                    }
                }
            }
        }
        if (riskInfo.getChannels() != null && riskInfo.getChannels().size() > 0) {
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : riskInfo.getChannels()) {
                if (entity.getType() != null && "IP".equals(entity.getType())) {
                    if (!ips.contains(entity.getValue())) {
                        ips.add(entity.getValue());
                    }
                }
            }
        }
        riskInfo.setThreatIps(ips);
        ResourceUpdates resourceUpdates = new ResourceUpdates();
        resourceUpdates.set("threatIps", riskInfo.getThreatIps());
        this.update(riskInfo.getId(), resourceUpdates);
    }

    /**
     * 获取过去一周新增的riskId信息
     *
     * @return
     */
    public List<String> getWeekRiskIds() {
        List<String> riskIds = new ArrayList<>();
        for (RiskInfo riskInfo : riskInfoDaoImpl.getWeekRiskInfos()) {
            riskIds.add(riskInfo.getId());
        }
        return riskIds;
    }

    /**
     * 标记被清理了样例的异常，页面做提示
     */
    public void markCleanSamplesRisk() {
        riskInfoDaoImpl.markCleanSamplesRisk();
    }

    /**
     * 根据网段筛选该网段下异常数量前五的app
     *
     * @return
     */
    public List<String> selectRiskAppTop5ByAppUri(List<String> hosts) {
        List<String> result = new ArrayList<>();
        List<CountEntity> countEntities = riskInfoDaoImpl.selectRiskAppTop5ByAppUri(hosts);
        for (CountEntity countEntity : countEntities) {
            result.add(countEntity.getName());
        }
        return result;
    }

    /**
     * 查询异常事件列表(分页)
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public ListOutputDto<RiskInfoDto> getRiskInfoList(RiskSearchDto riskSearchDto) throws Exception {
        ListOutputDto<RiskInfoDto> data = new ListOutputDto<RiskInfoDto>();
        //列表内容
        List<RiskInfo> riskInfoList = riskInfoDaoImpl.selectRiskInfoList(riskSearchDto, riskSearchDto.getSortField(), riskSearchDto.getSort(), riskSearchDto.getPage(), riskSearchDto.getLimit());
        List<RiskInfoDto> riskInfoDtoList = new ArrayList<RiskInfoDto>();
        //节点信息map
        Map<String, String> nodeMap = clusterNodeDao.findAll().stream().collect(Collectors.toMap(ClusterNode::getNid, ClusterNode::getName, (k1, k2) -> k1));
        for (RiskInfo riskInfo : riskInfoList) {
            RiskInfoDto riskInfoDto = RiskInfoDto.RiskInfoDtoMapper.INSTANCE.convert(riskInfo);
            //操作时间
            riskInfoDto.setOperateTime(riskInfo.getHandleTime() != null ? riskInfo.getHandleTime() : riskInfoDto.getFirstTime());
            if (DataUtil.isNotEmpty(riskInfoDto.getOrderFlag())) {
                riskInfoDto.setOrderFlagName(OrderFlagEnum.getOrderFlagEnum(riskInfoDto.getOrderFlag()).getName());
            } else {
                riskInfoDto.setOrderFlag(OrderFlagEnum.ORDER_INIT.getCode());
                riskInfoDto.setOrderFlagName(OrderFlagEnum.ORDER_INIT.getName());
            }
            //状态
            if (DataUtil.isNotEmpty(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()))) {
                riskInfoDto.setStateName(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()).getName());
            }
            //等级
            if (DataUtil.isNotEmpty(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()))) {
                riskInfoDto.setLevelName(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()).getName());
            }
            //攻击结果
            if (DataUtil.isNotEmpty(riskInfoDto.getAttackSuccess())) {
                riskInfoDto.setAttackSuccessName(GroupDataDto.AttackSuccessEnum.getAttackSuccessEnum(riskInfoDto.getAttackSuccess()).getName());
            }
            //返回标签
            if (DataUtil.isNotEmpty(riskInfoDto.getRspLabelList())) {
                riskInfoDto.setRspLabelListValue(transformationByDataLabe(riskInfoDto.getRspLabelList()));
            } else {
                riskInfoDto.setRspLabelListValue(new ArrayList<>());
            }
            //异常主体
            if (DataUtil.isNotEmpty(riskInfoDto.getEntities())) {
                List<RiskInfoDto.Entity> entities = riskInfoDto.getEntities();
                for (RiskInfoDto.Entity entity : entities) {
                    String value = entity.getValue();
                    if (DataUtil.isNotEmpty(value)) {
                        entity.setValue(value.replaceAll("(httpapp:|httpapi:)", ""));
                    }
                }
            }
            //主体类型
            if (DataUtil.isNotEmpty(riskInfoDto.getEntities()) && !riskInfoDto.getEntities().isEmpty()) {
                RiskInfoDto.Entity entity = riskInfoDto.getEntities().get(0);
                if (DataUtil.isNotEmpty(entity.getValue()) && DataUtil.isNotEmpty(entity.getType())) {
                    String type = riskInfoDto.getEntities().get(0).getType();
                    String value = riskInfoDto.getEntities().get(0).getValue();
                    riskInfoDto.setEntityType(type);
                    riskInfoDto.setEntityValue(value);
                }
            }
            //涉及数据量
            if (DataUtil.isEmpty(riskInfoDto.getRspDataDistinctCnt())
                    && DataUtil.isNotEmpty(riskInfo.getDescription())
                    && riskInfo.getDescription().containsKey("dataCnt")
                    && DataUtil.isNotEmpty(riskInfo.getDescription().get("dataCnt"))) {
                riskInfoDto.setRspDataDistinctCnt(riskInfo.getDescription().getLong("dataCnt"));
            }
            //风险描述
            setRiskInfoDesc(riskInfoDto);
            //风险名称
            riskInfoDto.setRiskPolicyName(riskInfo.getPolicySnapshot().getName());
            //风险类型
            riskInfoDto.setRiskPolicyGroup(riskInfo.getPolicySnapshot().getGroup());
            //策略id
            riskInfoDto.setRiskPolicyId(riskInfo.getPolicySnapshot().getId());
            //忽略类型不回显
            riskInfoDto.setIgnoreType(null);
            //填充节点信息
            if (DataUtil.isNotEmpty(riskInfo.getNodes())) {
                riskInfoDto.setNodes(new ArrayList<>());
                List<ResourceEntity.Node> nodes = riskInfo.getNodes();
                for (ResourceEntity.Node node : nodes) {
                    if (nodeMap.containsKey(node.getNid())) {
                        node.setNname(nodeMap.get(node.getNid()));
                    }
                    riskInfoDto.getNodes().add(node);
                }
            }
            riskInfoDtoList.add(riskInfoDto);
        }
        data.setRows(riskInfoDtoList);

        //列表总数
        Long totalCount = riskInfoDaoImpl.totalCount(riskSearchDto);
        data.setTotalCount(totalCount);

        return data;
    }

    public void setRiskInfoDesc(RiskInfoDto riskInfoDto) throws Exception {
        RiskDescribeDto riskDescribeDto = new RiskDescribeDto();
        //描述（version版本判断新旧风险数据）
        try {
            if (DataUtil.isNotEmpty(riskInfoDto.getVersion())) {
                riskDescribeDto = definedEventService.getDescribeData(riskInfoDto);
            } else {
                if (DataUtil.isEmpty(riskInfoDto.getRiskDescribe())) {
                    String sql = getSql(riskInfoDto.getId());
                    riskDescribeDto = definedEventService.getDescribeData(riskInfoDto, sql);
                }
            }
        } catch (Exception exception) {
            logger.error("获取风险描述出错:", exception);
        }
        riskDescribeDto = definedEventService.supplementaryRiskData(riskInfoDto, riskDescribeDto);
        riskInfoDto.setRiskDescribe(riskDescribeDto);
        //风险描述
        if (DataUtil.isEmpty(riskInfoDto.getRiskDesc()) && riskInfoDto.getPolicySnapshot().getName() != null && riskDescribeDto != null && riskDescribeDto.getDescribeArray() != null) {
            String riskDesc = "";
            riskDesc = getRiskDescByTemplate(riskInfoDto.getPolicySnapshot().getId(), riskDescribeDto.getDescribeArray(), riskInfoDto.getVersion());
            if (DataUtil.isEmpty(riskDesc) || "".equals(riskDesc)) {
                StringBuilder policyType = new StringBuilder();
                if (DataUtil.isEmpty(riskInfoDto.getPolicySnapshot().getType())) {
                    for (Policy.EntityRelation entityConfig : riskInfoDto.getPolicySnapshot().getEntityConfigs()) {
                        policyType.append(entityConfig.getType());
                    }
                } else {
                    policyType.append(riskInfoDto.getPolicySnapshot().getType());
                }
                boolean baseLine = false;
                boolean activeTime = false;
                if (DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot()) && DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot().getQuotaRule())) {
                    if (DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot().getQuotaRule().getExprList())) {
                        for (Expression expression : riskInfoDto.getPolicySnapshot().getQuotaRule().getExprList()) {
                            if (DataUtil.isNotEmpty(expression.getRight())
                                    && DataUtil.isNotEmpty(expression.getRight().getType())
                                    && VariableType.NAMED.name().equals(expression.getRight().getType().name())
                                    && "baseline".equals(expression.getRight().getVarName())) {
                                baseLine = true;
                            }
                            if (expression.getLeft().getName().equals("活跃时间范围")) {
                                activeTime = true;
                            }
                        }
                    }
                }
                if (baseLine) {
                    //基线描述特殊处理
                    Long baseline = riskDescribeDto.getBaseline();
                    Integer visitCnt = riskDescribeDto.getVisitCnt();
                    if (activeTime) {
                        if ("IP".equals(riskInfoDto.getEntityType())) {
                            riskDesc = "IP <h>" + riskInfoDto.getIp() + "</h> 于 <h>" + riskInfoDto.getDate() + "</h> 在API <h>" + riskInfoDto.getApiUrl() + "</h> 上有 <h>" + riskInfoDto.getSampleCount() + "</h> 次访问偏离活跃时间范围。";
                        } else {
                            riskDesc = "账号 <h>" + riskInfoDto.getEntityValue() + "</h> 于 <h>" + riskInfoDto.getDate() + "</h> 在API <h>" + riskInfoDto.getApiUrl() + "</h> 上有 <h>" + riskInfoDto.getSampleCount() + "</h> 次访问偏离活跃时间范围。";
                        }
                    } else {
                        riskDesc = getCustomRiskDescByTemplate(policyType.toString(), riskDescribeDto.getDescribeArray(), baseline, visitCnt);
                    }
                } else {
                    riskDesc = getCustomRiskDescByTemplate(policyType.toString(), riskDescribeDto.getDescribeArray(), null, null);
                }
            }
            riskInfoDto.setRiskDesc(riskDesc);
        }
    }

    public RiskInfoDto getRiskInfoDto(RiskInfo riskInfo, Map<String, String> nodeMap) throws Exception {
        //列表内容
        RiskInfoDto riskInfoDto = RiskInfoDto.RiskInfoDtoMapper.INSTANCE.convert(riskInfo);
        if (DataUtil.isNotEmpty(riskInfoDto.getOrderFlag())) {
            riskInfoDto.setOrderFlagName(OrderFlagEnum.getOrderFlagEnum(riskInfoDto.getOrderFlag()).getName());
        } else {
            riskInfoDto.setOrderFlag(OrderFlagEnum.ORDER_INIT.getCode());
            riskInfoDto.setOrderFlagName(OrderFlagEnum.ORDER_INIT.getName());
        }
        //状态
        if (DataUtil.isNotEmpty(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()))) {
            riskInfoDto.setStateName(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()).getName());
        }
        //等级
        if (DataUtil.isNotEmpty(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()))) {
            riskInfoDto.setLevelName(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()).getName());
        }
        //攻击结果
        if (DataUtil.isNotEmpty(riskInfoDto.getAttackSuccess())) {
            riskInfoDto.setAttackSuccessName(GroupDataDto.AttackSuccessEnum.getAttackSuccessEnum(riskInfoDto.getAttackSuccess()).getName());
        }
        //返回标签
        if (DataUtil.isNotEmpty(riskInfoDto.getRspLabelList())) {

            riskInfoDto.setRspLabelListValue(transformationByDataLabe(riskInfoDto.getRspLabelList()));
        }

        //风险描述
        setRiskInfoDesc(riskInfoDto);

        //涉及数据量
        if (DataUtil.isEmpty(riskInfoDto.getRspDataDistinctCnt())
                && DataUtil.isNotEmpty(riskInfo.getDescription())
                && riskInfo.getDescription().containsKey("dataCnt")
                && DataUtil.isNotEmpty(riskInfo.getDescription().get("dataCnt"))) {
            riskInfoDto.setRspDataDistinctCnt(riskInfo.getDescription().getLong("dataCnt"));
        }

        //异常主体
        if (DataUtil.isNotEmpty(riskInfoDto.getEntities())) {
            List<RiskInfoDto.Entity> entities = riskInfoDto.getEntities();
            for (RiskInfoDto.Entity entity : entities) {
                String value = entity.getValue();
                if (DataUtil.isNotEmpty(value)) {
                    entity.setValue(value.replaceAll("(httpapp:|httpapi:)", ""));
                }
            }
        }

        //主体类型
        if (DataUtil.isNotEmpty(riskInfoDto.getEntities()) && !riskInfoDto.getEntities().isEmpty()) {
            RiskInfoDto.Entity entity = riskInfoDto.getEntities().get(0);
            if (DataUtil.isNotEmpty(entity.getValue()) && DataUtil.isNotEmpty(entity.getType())) {
                String type = riskInfoDto.getEntities().get(0).getType();
                String value = riskInfoDto.getEntities().get(0).getValue();
                riskInfoDto.setEntityType(type);
                riskInfoDto.setEntityValue(value);
            }
        }

        //异常名称
        riskInfoDto.setRiskPolicyName(riskInfo.getPolicySnapshot().getName());

        //异常类型
        riskInfoDto.setRiskPolicyGroup(riskInfo.getPolicySnapshot().getGroup());

        //策略id
        riskInfoDto.setRiskPolicyId(riskInfo.getPolicySnapshot().getId());

        //填充节点信息
        if (DataUtil.isNotEmpty(riskInfo.getNodes())) {
            riskInfoDto.setNodes(new ArrayList<>());
            List<ResourceEntity.Node> nodes = riskInfo.getNodes();
            for (ResourceEntity.Node node : nodes) {
                if (nodeMap.containsKey(node.getNid())) {
                    node.setNname(nodeMap.get(node.getNid()));
                }
                riskInfoDto.getNodes().add(node);
            }
        }

        return riskInfoDto;
    }

    public ListOutputDto<RiskInfoDto> getRiskInfoListSort(RiskSearchDto riskSearchDto) throws Exception {
//        if ("rspLabelList".equals(riskSearchDto.getSortField())) {
//            riskSearchDto.setSortField("rspLabelListCount");
//        }
        ListOutputDto<RiskInfoDto> data = new ListOutputDto<RiskInfoDto>();
        //列表内容
        List<RiskInfo> riskInfoList = riskInfoDaoImpl.selectRiskInfoList(riskSearchDto, riskSearchDto.getSortField(), riskSearchDto.getSort(), riskSearchDto.getPage(), riskSearchDto.getLimit());
        List<RiskInfoDto> riskInfoDtoList = new ArrayList<RiskInfoDto>();
        for (RiskInfo riskInfo : riskInfoList) {
            RiskInfoDto riskInfoDto = RiskInfoDto.RiskInfoDtoMapper.INSTANCE.convert(riskInfo);
            if (DataUtil.isNotEmpty(riskInfoDto.getOrderFlag())) {
                riskInfoDto.setOrderFlagName(OrderFlagEnum.getOrderFlagEnum(riskInfoDto.getOrderFlag()).getName());
            } else {
                riskInfoDto.setOrderFlag(OrderFlagEnum.ORDER_INIT.getCode());
                riskInfoDto.setOrderFlagName(OrderFlagEnum.ORDER_INIT.getName());
            }
            //状态
            if (DataUtil.isNotEmpty(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()))) {
                riskInfoDto.setStateName(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()).getName());
            }
            //等级
            if (DataUtil.isNotEmpty(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()))) {
                riskInfoDto.setLevelName(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()).getName());
            }
            //攻击结果
            if (DataUtil.isNotEmpty(riskInfoDto.getAttackSuccess())) {
                riskInfoDto.setAttackSuccessName(GroupDataDto.AttackSuccessEnum.getAttackSuccessEnum(riskInfoDto.getAttackSuccess()).getName());
            }
            //返回标签
            if (DataUtil.isNotEmpty(riskInfoDto.getRspLabelList())) {

                riskInfoDto.setRspLabelListValue(transformationByDataLabe(riskInfoDto.getRspLabelList()));
            }

            RiskDescribeDto riskDescribeDto = new RiskDescribeDto();
            //描述（version版本判断新旧异常数据）
            try {
                if (DataUtil.isNotEmpty(riskInfoDto.getVersion())) {

                    riskDescribeDto = definedEventService.getDescribeData(riskInfoDto);
                } else {
                    if (DataUtil.isEmpty(riskInfoDto.getRiskDescribe())) {

                        String sql = getSql(riskInfoDto.getId());
                        riskDescribeDto = definedEventService.getDescribeData(riskInfoDto, sql);
                    }
                }
            } catch (Exception exception) {
                logger.error("获取异常描述出错:{}", exception);
            }
            riskDescribeDto = definedEventService.supplementaryRiskData(riskInfoDto, riskDescribeDto);

            //风险描述
            setRiskInfoDesc(riskInfoDto);

            //补充异常描述数据
            definedEventService.supplementaryRiskData(riskInfoDto, riskInfoDto.getRiskDescribe() != null ? riskInfoDto.getRiskDescribe() : new RiskDescribeDto());
            //涉及数据量
            if (DataUtil.isEmpty(riskInfoDto.getRspDataDistinctCnt())
                    && DataUtil.isNotEmpty(riskInfo.getDescription())
                    && riskInfo.getDescription().containsKey("dataCnt")
                    && DataUtil.isNotEmpty(riskInfo.getDescription().get("dataCnt"))) {
                riskInfoDto.setRspDataDistinctCnt(riskInfo.getDescription().getLong("dataCnt"));
            }
            //异常主体
            if (DataUtil.isNotEmpty(riskInfoDto.getEntities())) {
                List<RiskInfoDto.Entity> entities = riskInfoDto.getEntities();
                for (RiskInfoDto.Entity entity : entities) {
                    String value = entity.getValue();
                    if (DataUtil.isNotEmpty(value)) {
                        entity.setValue(value.replaceAll("(httpapp:|httpapi:)", ""));
                    }
                }
            }

            //主体类型
            if (DataUtil.isNotEmpty(riskInfoDto.getEntities()) && !riskInfoDto.getEntities().isEmpty()) {
                RiskInfoDto.Entity entity = riskInfoDto.getEntities().get(0);
                if (DataUtil.isNotEmpty(entity.getValue()) && DataUtil.isNotEmpty(entity.getType())) {
                    String type = riskInfoDto.getEntities().get(0).getType();
                    String value = riskInfoDto.getEntities().get(0).getValue();
                    riskInfoDto.setEntityType(type);
                    riskInfoDto.setEntityValue(value);
                }
            }

            //自定义异常的涉及数据量
            if (riskInfoDto.getRspDataDistinctCnt() == null && riskInfoDto.getRiskDescribe().getDataCnt() != null) {
                riskInfoDto.setRspDataDistinctCnt(Long.valueOf(riskInfoDto.getRiskDescribe().getDataCnt()));
            }

            //异常名称
            riskInfoDto.setRiskPolicyName(riskInfo.getPolicySnapshot().getName());

            //异常类型
            riskInfoDto.setRiskPolicyGroup(riskInfo.getPolicySnapshot().getGroup());

            //策略id
            riskInfoDto.setRiskPolicyId(riskInfo.getPolicySnapshot().getId());

            riskInfoDtoList.add(riskInfoDto);
        }
        data.setRows(riskInfoDtoList);

        //列表总数
        Long totalCount = riskInfoDaoImpl.totalCount(riskSearchDto);
        data.setTotalCount(totalCount);

        return data;
    }

    public Long totalCount(RiskSearchDto riskSearchDto) {
        try {
            Long aLong = riskInfoDaoImpl.totalCount(riskSearchDto);
            return aLong;
        } catch (Exception exception) {
            return 0L;
        }
    }

    /**
     * 查询异常事件列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public List<RiskInfoDto> getRiskInfoDtoList(RiskSearchDto riskSearchDto) throws Exception {

        //列表内容
        List<RiskInfo> riskInfoList = riskInfoDaoImpl.selectRiskInfoList(riskSearchDto, null, null);
        List<RiskInfoDto> riskInfoDtoList = new ArrayList<RiskInfoDto>();
        for (RiskInfo riskInfo : riskInfoList) {

            RiskInfoDto riskInfoDto = RiskInfoDto.RiskInfoDtoMapper.INSTANCE.convert(riskInfo);

            //状态
            if (DataUtil.isNotEmpty(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()))) {

                riskInfoDto.setStateName(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()).getName());
            }
            //等级
            if (DataUtil.isNotEmpty(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()))) {

                riskInfoDto.setLevelName(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()).getName());
            }
            //攻击结果
            if (DataUtil.isNotEmpty(riskInfoDto.getAttackSuccess())) {
                riskInfoDto.setAttackSuccessName(GroupDataDto.AttackSuccessEnum.getAttackSuccessEnum(riskInfoDto.getAttackSuccess()).getName());
            }
            //返回标签
            if (DataUtil.isNotEmpty(riskInfoDto.getRspLabelList())) {

                riskInfoDto.setRspLabelListValue(transformationByDataLabe(riskInfoDto.getRspLabelList()));
            }

            //异常名称
            riskInfoDto.setRiskPolicyName(riskInfo.getPolicySnapshot().getName());

            //异常类型
            riskInfoDto.setRiskPolicyGroup(riskInfo.getPolicySnapshot().getGroup());

            //策略id
            riskInfoDto.setRiskPolicyId(riskInfo.getPolicySnapshot().getId());

            //白名单类型
            riskInfoDto.setWhiteTypeList(getRiskWhiteType(riskInfoDto));

            riskInfoDtoList.add(riskInfoDto);
        }

        return riskInfoDtoList;
    }

    /**
     * id查询异常事件详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public RiskInfoDto getRiskInfoById(String id) throws Exception {

        RiskInfo riskInfo = riskInfoDaoImpl.selectRiskInfoById(id);

        if (DataUtil.isNotEmpty(riskInfo)) {

            RiskInfoDto riskInfoDto = RiskInfoDto.RiskInfoDtoMapper.INSTANCE.convert(riskInfo);
            //操作时间
            riskInfoDto.setOperateTime(riskInfo.getHandleTime() != null ? riskInfo.getHandleTime() : riskInfoDto.getFirstTime());
            //状态
            if (DataUtil.isNotEmpty(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()))) {

                riskInfoDto.setStateName(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()).getName());
            }
            //等级
            if (DataUtil.isNotEmpty(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()))) {

                riskInfoDto.setLevelName(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()).getName());
            }
            //攻击结果
            if (DataUtil.isNotEmpty(riskInfoDto.getAttackSuccess())) {
                riskInfoDto.setAttackSuccessName(GroupDataDto.AttackSuccessEnum.getAttackSuccessEnum(riskInfoDto.getAttackSuccess()).getName());
            }
            //返回标签
            if (DataUtil.isNotEmpty(riskInfoDto.getRspLabelList())) {

                riskInfoDto.setRspLabelListValue(transformationByDataLabe(riskInfoDto.getRspLabelList()));
            }
            //威胁状态与威胁标签
            for (RiskInfoDto.Entity entity : riskInfoDto.getEntities()) {
                if (Policy.EntityEnum.IP.name().equals(entity.getType())) {
                    RiskThreatIpInfoDto riskThreatIpInfo = riskThreatPortraitService.getRiskThreatIpInfo(entity.getValue());
                    if (DataUtil.isNotEmpty(riskThreatIpInfo)) {
                        riskInfoDto.setThreatState(riskThreatIpInfo.getThreatState());
                        riskInfoDto.setThreatLabels(riskThreatIpInfo.getThreatLabels());
                    }
                }
            }
            //涉及数据量
            if (DataUtil.isEmpty(riskInfoDto.getRspDataDistinctCnt())
                    && DataUtil.isNotEmpty(riskInfo.getDescription())
                    && riskInfo.getDescription().containsKey("dataCnt")
                    && DataUtil.isNotEmpty(riskInfo.getDescription().get("dataCnt"))) {
                riskInfoDto.setRspDataDistinctCnt(riskInfo.getDescription().getLong("dataCnt"));
            }
            //风险描述
            setRiskInfoDesc(riskInfoDto);
            // 补充开放接口返回信息
//            this.fillRiskInfo(riskInfoDto);
            //异常名称
            riskInfoDto.setRiskPolicyName(riskInfo.getPolicySnapshot().getName());
            //异常类型
            riskInfoDto.setRiskPolicyGroup(riskInfo.getPolicySnapshot().getGroup());
            //策略id
            riskInfoDto.setRiskPolicyId(riskInfo.getPolicySnapshot().getId());
            //白名单类型
            riskInfoDto.setWhiteTypeList(getRiskWhiteType(riskInfoDto));
            //主体类型、值
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : riskInfo.getEntities()) {
                if (DataUtil.isNotEmpty(entity.getType())) {
                    riskInfoDto.setEntityType(entity.getType());
                }
                if (DataUtil.isNotEmpty(entity.getValue())) {
                    riskInfoDto.setEntityValue(entity.getValue());
                }
            }

            //忽略类型不回显
            riskInfoDto.setIgnoreType(null);

            return riskInfoDto;
        } else {
            return new RiskInfoDto();
        }
    }

    public String getRiskDescById(RiskInfo riskInfo) throws Exception {
        if (DataUtil.isNotEmpty(riskInfo)) {
            RiskInfoDto riskInfoDto = RiskInfoDto.RiskInfoDtoMapper.INSTANCE.convert(riskInfo);
            RiskDescribeDto riskDescribeDto = new RiskDescribeDto();
            //描述（version版本判断新旧异常数据）
            try {
                if (DataUtil.isNotEmpty(riskInfoDto.getVersion())) {

                    riskDescribeDto = definedEventService.getDescribeData(riskInfoDto);
                } else {
                    if (DataUtil.isEmpty(riskInfoDto.getRiskDescribe())) {

                        String sql = getSql(riskInfoDto.getId());
                        riskDescribeDto = definedEventService.getDescribeData(riskInfoDto, sql);
                    }
                }
            } catch (Exception exception) {
                logger.error("generate risk desc error", exception);
            }
            riskDescribeDto = definedEventService.supplementaryRiskData(riskInfoDto, riskDescribeDto);
            riskInfoDto.setRiskDescribe(riskDescribeDto);
            //异常描述
            if (DataUtil.isEmpty(riskInfoDto.getRiskDesc()) && riskInfo.getPolicySnapshot().getName() != null && riskDescribeDto != null && riskDescribeDto.getDescribeArray() != null) {
                String riskDesc = "";
                riskDesc = getRiskDescByTemplate(riskInfo.getPolicySnapshot().getId(), riskDescribeDto.getDescribeArray(), riskInfo.getVersion());
                if (DataUtil.isEmpty(riskDesc) || "".equals(riskDesc)) {
                    StringBuilder policyType = new StringBuilder();
                    if (DataUtil.isEmpty(riskInfo.getPolicySnapshot().getType())) {
                        for (Policy.EntityRelation entityConfig : riskInfo.getPolicySnapshot().getEntityConfigs()) {
                            policyType.append(entityConfig.getType());
                        }
                    } else {
                        policyType.append(riskInfo.getPolicySnapshot().getType());
                    }
                    boolean baseLine = false;
                    if (DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot()) && DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot().getQuotaRule())) {
                        if (DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot().getQuotaRule().getExprList())) {
                            for (Expression expression : riskInfo.getPolicySnapshot().getQuotaRule().getExprList()) {
                                if (DataUtil.isNotEmpty(expression.getRight())
                                        && DataUtil.isNotEmpty(expression.getRight().getType())
                                        && VariableType.NAMED.name().equals(expression.getRight().getType().name())
                                        && "baseline".equals(expression.getRight().getVarName())) {
                                    baseLine = true;
                                }
                            }
                        }
                    }
                    if (baseLine) {
                        //基线描述特殊处理
                        Long baseline = riskDescribeDto.getBaseline();
                        Integer visitCnt = riskDescribeDto.getVisitCnt();
                        riskDesc = getCustomRiskDescByTemplate(policyType.toString(), riskDescribeDto.getDescribeArray(), baseline, visitCnt);
                    } else {
                        riskDesc = getCustomRiskDescByTemplate(policyType.toString(), riskDescribeDto.getDescribeArray(), null, null);
                    }
                }
                return riskDesc;
            }
        }
        return "";
    }

    /**
     * 异常处理
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public RiskInfo handleRiskInfo(RiskInfoDto riskInfoDto) throws Exception {
        RiskInfo riskInfo = riskInfoDaoImpl.selectRiskInfoById(riskInfoDto.getId());
        String operator = HttpServletRequestUtil.getUserName();
        if (org.apache.commons.lang3.StringUtils.isEmpty(operator)) {
            if (HttpServletRequestUtil.checkHeader()) {
                operator = "主节点";
            } else {
                operator = "web";
            }
        }
        String newRiskStateName = "";
        boolean isBatchIngore = false;
        boolean editIpInfo = false;
        //状态和忽略类型
        if (DataUtil.isNotEmpty(riskInfoDto.getState())) {
            if (!riskInfoDto.getState().equals(riskInfo.getState())) {
                editIpInfo = true;
            }
            riskInfo.setState(riskInfoDto.getState());
            newRiskStateName = RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()).getName();
            if (RiskInfo.RiskStateEnum.HAS_IGNORE.getState().equals(riskInfoDto.getState())) {
                if (DataUtil.isNotEmpty(riskInfoDto.getIgnoreType())) {
                    riskInfo.setIgnoreType(riskInfoDto.getIgnoreType());
                    //批量忽略
                    ingoreRiskInfo(riskInfo, operator, riskInfoDto.getRemark());
                    isBatchIngore = true;
                }
            } else {
                riskInfo.setIgnoreType(null);
            }
        }
        //处理建议
        if (DataUtil.isNotEmpty(riskInfoDto.getRemark())) {
            riskInfo.setRemark(riskInfoDto.getRemark());
        } else {
            riskInfo.setRemark("");
        }
        if (DataUtil.isNotEmpty(riskInfoDto.getRspLabelList())) {
            riskInfo.setRspLabelListCount((long) riskInfoDto.getRspLabelList().size());
        } else {
            if (DataUtil.isNotEmpty(riskInfo.getRspLabelList())) {
                riskInfo.setRspLabelListCount((long) riskInfo.getRspLabelList().size());
            }
        }
        //操作人
//        if (DataUtil.isNotEmpty(riskInfoDto.getOperateName())) {
//            riskInfo.setOperateName(riskInfoDto.getOperateName());
//        } else {
        riskInfo.setOperateName(operator);
//        }
        //白名单
        whiteRiskInfo(riskInfoDto.getWhiteTypeList(), riskInfo);
        riskInfoDaoImpl.updateRiskInfo(riskInfo);
        if (!isBatchIngore) {
            insertRiskOperationLog(riskInfo.getId(), operator, newRiskStateName, riskInfoDto.getRemark());
        }
//        threatIpService.updateThreatIpInfo(riskInfo);
        StringBuilder option = new StringBuilder();
        if (!StringUtils.isNullOrEmpty(riskInfoDto.getIgnoreType())) {
            option.append("并按照\"" + riskInfoDto.getIgnoreType() + "\"忽略；");
        }
        if (!CollectionUtils.isEmpty(riskInfoDto.getWhiteTypeList())) {
            option.append("按照\"" + riskInfoDto.getWhiteTypeList().stream().collect(Collectors.joining(",")) + "\"进行加白；");
        }
        if (editIpInfo) {
            if (DataUtil.isNotEmpty(riskInfo.getEntities())) {
                for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : riskInfo.getEntities()) {
                    if ("IP".equals(entity.getType())) {
//                        eventPublisher.publishEvent(new RiskChangToIpEvent(entity.getValue(), riskInfo.getState()));
                    }
                }
            }
        }
//        eventPublisher.publishEvent(new RiskChangedEvent(riskInfo.getId(), riskInfo.getEntities().get(0).getValue(), riskInfo.getPolicySnapshot().getName(), riskInfo.getState(), newRiskStateName, option.toString(), riskInfo));
        return riskInfo;
    }

    /**
     * 批量异常忽略
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2022-04-21 9:56
     */
    public void ignoreRiskInfos(RiskIngoresDto riskIngoresDto) throws Exception {

        //批量加白
        if (DataUtil.isNotEmpty(riskIngoresDto.getRiskPolicyAllowList())) {

            List<RiskPolicyAllowList> updatelist = new ArrayList<RiskPolicyAllowList>();

            for (RiskPolicyAllowListDto riskPolicyAllowListDto : riskIngoresDto.getRiskPolicyAllowList()) {

                RiskPolicyAllowList riskPolicyAllowList = riskPolicyAllowListService.getRiskPolicyAllowList(riskPolicyAllowListDto.getPolicyId(), riskPolicyAllowListDto.getType(), riskPolicyAllowListDto.getValue());

                if (DataUtil.isEmpty(riskPolicyAllowList)) {

                    riskPolicyAllowList = RiskPolicyAllowList.RiskPolicyAllowListMapper.INSTANCE.convert(riskPolicyAllowListDto);
                    updatelist.add(riskPolicyAllowList);
                }
            }

            riskPolicyAllowListService.addRiskPolicyAllowList(updatelist);
        }

        //批量操作1
        if (DataUtil.isNotEmpty(riskIngoresDto.getIdList())) {

            riskInfoDaoImpl.batchIgored(riskIngoresDto.getIdList(), riskIngoresDto.getState(), riskIngoresDto.getOperateName(), riskIngoresDto.getRemark());
        } else {
            //批量操作2
            riskInfoDaoImpl.ignoreRiskInfos(riskIngoresDto.getRiskSearchDto(), riskIngoresDto.getState(), riskIngoresDto.getRemark(), riskIngoresDto.getOperateName());
        }

    }

    /**
     * @param id
     * @param operator
     * @param newRiskStateName
     * @param remark
     * <AUTHOR>
     * @description: 异常操作日志记录
     * @Return void
     */
    private void insertRiskOperationLog(String id, String operator, String newRiskStateName, String remark) {
        String detailLog = operator + "将异常状态修改为" + newRiskStateName;
        if (DataUtil.isNotEmpty(remark)) {
            detailLog += ",处理意见为:" + remark;
        }
        RiskOperationLog riskOperationLog = RiskOperationLog.builder().createTime(System.currentTimeMillis()).riskId(id).detailLog(detailLog).operatorName(operator).build();
        riskOperationLogDao.save(riskOperationLog);
    }

    /**
     * 编辑异常事件
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public RiskInfo editRiskInfo(RiskInfoDto riskInfoDto) throws Exception {

        RiskInfo riskInfo = RiskInfo.RiskInfoMapper.INSTANCE.convert(riskInfoDto);

        return riskInfoDaoImpl.updateRiskInfo(riskInfo);
    }

    /**
     * 异常事件分组
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-4-08 9:56
     */
    public List<AggregationDto> getRiskInfoGroup(RiskSearchDto riskSearchDto, GroupDto groupDto, String field, Integer sort, Integer page, Integer limit) throws Exception {

        return riskInfoDaoImpl.selectRiskInfoGroup(riskSearchDto, groupDto, field, sort, page, limit);
    }

    /**
     * <AUTHOR>
     * @description: 异常总体概览统计
     * @date: 2021/8/10
     * @Return java.lang.String
     */
    public RiskCountDto countRiskGeneralOverview() {
        List<RiskInfo> allRiskList = riskInfoDaoImpl.getAllByCriteria(Criteria.where("state").in(RiskInfo.RiskStateEnum.NOT_HANDLE.getState(), RiskInfo.RiskStateEnum.HAS_HANDLE.getState()));
        long highRiskNum = allRiskList.stream().filter(e -> e.getLevel().equals(RiskInfo.RiskLevelEnum.HIGH.getLevel())).count();
        long revealDataRiskNum = allRiskList.stream().filter(e -> "数据泄漏类".equals(e.getPolicySnapshot().getGroup())).count();
        long highRevealDataRiskNum = allRiskList.stream().filter(e -> e.getLevel().equals(RiskInfo.RiskLevelEnum.HIGH.getLevel()) && "数据泄漏类".equals(e.getPolicySnapshot().getGroup())).count();
        RiskCountDto riskCountDto = RiskCountDto.builder().totalRiskNum((long) allRiskList.size()).totalHighRiskNum(highRiskNum).revealDataRiskNum(revealDataRiskNum).highRevealDataRiskNum(highRevealDataRiskNum).build();
        return riskCountDto;
    }

    /**
     * <AUTHOR>
     * @description: 获取近30天的异常趋势
     * @date: 2021/8/10
     * @Return java.lang.String
     */
    public List<RiskCountDto> getRiskTrend(int days) {
        //初始化
        List<RiskCountDto> resultList = initTrendList(days);

        try {
            for (int i = 0; i < resultList.size(); i++) {
                String startDate = resultList.get(i).getDate();
                long startTime = com.quanzhi.auditapiv2.common.util.utils.DateUtil.getDateZeroTimestamp(com.quanzhi.auditapiv2.common.util.utils.DateUtil.stringToDate(startDate));
                long endTime = com.quanzhi.auditapiv2.common.util.utils.DateUtil.getDateLastTimestamp(com.quanzhi.auditapiv2.common.util.utils.DateUtil.stringToDate(startDate));
                RiskSearchDto riskSearchDto = new RiskSearchDto();
                riskSearchDto.setFirstTimeStart(startTime);
                riskSearchDto.setFirstTimeEnd(endTime);
                List<Integer> states = new ArrayList<>(2);
                states.add(RiskInfo.RiskStateEnum.HAS_HANDLE.getState());
                states.add(RiskInfo.RiskStateEnum.NOT_HANDLE.getState());
                riskSearchDto.setState(states);
                for (int i1 = 1; i1 <= 3; i1++) {
                    riskSearchDto.setLevel(Arrays.asList(i1));
                    Long count = riskInfoDaoImpl.totalCount(riskSearchDto);
                    switch (i1) {
                        case 1:
                            resultList.get(i).setLowRiskNum(count);
                            break;
                        case 2:
                            resultList.get(i).setMidRiskNum(count);
                            break;
                        case 3:
                            resultList.get(i).setHighRiskNum(count);
                            break;
                        default:
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取态势大屏异常趋势出错：" + e.getMessage());
        }

        return resultList;
    }

    /**
     * @param beforeDay 往前推多少天
     * <AUTHOR>
     * @description: 初始化一个日期范围
     * @date: 2021/8/11
     */
    private List<RiskCountDto> initTrendList(int beforeDay) {
        List<RiskCountDto> list = new ArrayList<>();
        DateTime startDate = DateUtil.offsetDay(new Date(), 0 - beforeDay);
        List<DateTime> dateTimes = DateUtil.rangeToList(startDate, new Date(), DateField.DAY_OF_WEEK);
        for (DateTime dateTime : dateTimes) {
            RiskCountDto riskCountDto = RiskCountDto.builder().date(DateUtil.date(dateTime).toString("yyyyMMdd")).highRiskNum(0L).midRiskNum(0L).lowRiskNum(0L).build();
            list.add(riskCountDto);
        }
        return list;
    }

    /**
     * <AUTHOR>
     * @description: 异常规则排行
     * @date: 2021/8/10
     */
    public List<CommonDto> getRiskTypeTop() {
        List<CommonDto> list = new ArrayList<>();
        GroupDto groupDto = new GroupDto();
        groupDto.setAggrType(GroupDto.GroupTypeEnum.COUNT.getName());
        groupDto.setGroupFields(new String[]{RiskSearchDto.GroupFieldEnum.RISK_NAME.getName()});
        List<AggregationDto> aggregationDtos = null;
        try {
            aggregationDtos = riskInfoDaoImpl.selectRiskInfoGroup(new RiskSearchDto(), groupDto, null, ConstantUtil.Sort.DESC, null, null);
            aggregationDtos.forEach(aggregationDto -> list.add(CommonDto.builder().key(aggregationDto.getId()).value(aggregationDto.getResultAlias()).build()));
        } catch (Exception exception) {
            logger.error("异常规则排行分组统计出错:{}", exception);
        }
        return list;
    }

    /**
     * <AUTHOR>
     * @description: 泄露地域分布
     * @date: 2021/8/10
     * @Return java.lang.String
     */
    public List<CommonDto> revealAreaDistributed() {
        List<CommonDto> resultList = new ArrayList<>();
        List<RiskCountryCount> list = riskCountryCountDao.getRiskCountryCountByDataReveal();
        list.forEach(e -> {
            CommonDto commonDto = new CommonDto(e.getCountry(), e.getCount());
            resultList.add(commonDto);
        });
        return resultList;
    }

    /**
     * 异常事件查询条件
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public String getSql(String id) throws Exception {

        RiskInfo riskInfo = riskInfoDaoImpl.selectRiskInfoById(id);

        if (DataUtil.isNotEmpty(riskInfo)) {

            return riskInfo.createEventQuerySqlSegment();
        } else {
            return null;
        }
    }

    /**
     * 获取去重威胁IP
     */
    public Set<String> selectThreatIp(Integer count, Integer limit) {
        Set<String> threatIps = new HashSet<>();
        Criteria criteria = new Criteria();
        criteria.and("state").in(RiskInfo.RiskStateEnum.NOT_HANDLE.getState(), RiskInfo.RiskStateEnum.HAS_HANDLE.getState())
                .and("entities.type").is(EntityTypeEnum.IP.name());
        List<RiskInfo> riskInfos = riskInfoDaoImpl.getLimitByCriteria(criteria, count, limit);
        for (RiskInfo riskInfo : riskInfos) {
            threatIps.add(riskInfo.getEntities().get(0).getValue());
        }
        return threatIps;
    }

    /**
     * 获取去重威胁账号
     */
    public Set<String> selectThreatAccount(Integer count, Integer limit) {
        Set<String> threatAccounts = new HashSet<>();
        Criteria criteria = new Criteria();
        criteria.and("state").in(RiskInfo.RiskStateEnum.NOT_HANDLE.getState(), RiskInfo.RiskStateEnum.HAS_HANDLE.getState())
                .and("entities.type").is(EntityTypeEnum.ACCOUNT.name());
        List<RiskInfo> riskInfos = riskInfoDaoImpl.getLimitByCriteria(criteria, count, limit);
        for (RiskInfo riskInfo : riskInfos) {
            threatAccounts.add(riskInfo.getEntities().get(0).getValue());
        }
        return threatAccounts;
    }


    /**
     * <AUTHOR>
     * @description: 获取数据泄漏类的异常
     * @date: 2021/8/13
     * @Return java.util.List<com.quanzhi.auditapiv2.core.risk.entity.RiskInfo>
     */
    public List<RiskInfo> selectDataRevealRisk(Boolean isOldRisk) {
        Criteria criteria = new Criteria();
        criteria.and("state").in(RiskInfo.RiskStateEnum.NOT_HANDLE.getState(), RiskInfo.RiskStateEnum.HAS_HANDLE.getState());
        criteria.and("policySnapshot.group").is("数据泄漏类");
        if (DataUtil.isNotEmpty(isOldRisk)) {
            if (isOldRisk) {
                criteria.and("version").exists(false);
            } else {
                criteria.and("version").exists(true);
            }
        }
        List<RiskInfo> riskInfos = riskInfoDaoImpl.getAllByCriteria(criteria);
        return riskInfos;
    }

    /**
     * @param id
     * <AUTHOR>
     * @description: 置顶异常
     * @date: 2021/8/17
     * @Return void
     */
    public void markRisk(String id, Boolean riskMark) throws Exception {
        RiskInfo riskInfo = riskInfoDaoImpl.selectRiskInfoById(id);
        if (riskInfo == null) {
            throw new IllegalArgumentException("异常不存在");
        }
        ResourceUpdates update = new ResourceUpdates();
        update.set("riskMark", riskMark);
        riskInfoDaoImpl.update(id, update, null);
//        eventPublisher.publishEvent(new RiskMarkActionEvent(riskInfo, riskInfo.getEntities().get(0).getValue(), riskInfo.getPolicySnapshot().getName(), riskMark));
    }

    /**
     * @param riskInfoExportDto
     * <AUTHOR>
     * @description: 导出异常清单
     * @date: 2021/8/18
     * @Return void
     */
    public String exportRiskInfo(RiskInfoExportDto riskInfoExportDto, String filePath) throws Exception {
        //获取中文列头
        List<String> titles = new ArrayList<String>();
        for (ExporTitleFieldDto exporTitleFieldDto : riskInfoExportDto.getList()) {
            if (exporTitleFieldDto.getField().equals("customField")) {
                //自定义字段
                titles.addAll(exporTitleFieldDto.getCustomFieldList());
            } else {
                titles.add(exporTitleFieldDto.getTitle());
            }
        }
        //获取搜索dto
        RiskSearchDto riskSearchDto = riskInfoExportDto.getRiskSearchDto();
        if (DataUtil.isEmpty(riskSearchDto)) {
            log.warn("search params is empty,no export!");
            return filePath;
        }
        //获取导出id
        List<String> exportIds = new ArrayList<>();
        if (riskInfoExportDto.getIds() != null) {
            exportIds = riskInfoExportDto.getIds();
        }
        //节点信息
        Map<String, String> nodeMap = clusterNodeDao.findAll().stream().collect(Collectors.toMap(ClusterNode::getNid, ClusterNode::getName, (k1, k2) -> k1));
//        int singleCount = 100;
        CSVFormat format = CSVFormat.DEFAULT.withHeader(titles.toArray(new String[titles.size()]));
        try (FileOutputStream fos = new FileOutputStream(filePath); OutputStreamWriter osw = new OutputStreamWriter(fos, "GBK"); CSVPrinter printer = new CSVPrinter(osw, format);) {
            Long count = riskInfoDaoImpl.totalCount(riskSearchDto);
            if (count == 0 && exportIds.isEmpty()) {
                log.info("query result is empty,no export!");
                printer.flush();
                return filePath;
            }

            Query query = new Query();
            if (!exportIds.isEmpty()) {
                query.addCriteria(Criteria.where("_id").in(exportIds));
            } else {
                query.addCriteria(riskInfoDaoImpl.getCriteria(riskSearchDto));
            }

            try (MongoCursor<Document> cursor =
                         mongoTemplate.getCollection(collectionName)
                                 .find(query.getQueryObject())
                                 .noCursorTimeout(true).batchSize(singleCount).limit(maxCount).cursor()) {
                JsonWriterSettings settings = JsonWriterSettings.builder().int64Converter(new Converter<Long>() {
                    public void convert(Long value, StrictJsonWriter writer) {
                        writer.writeNumber(value.toString());
                    }
                }).build();
                List<List<String>> rows = new ArrayList<>(singleCount);
                while (cursor.hasNext()) {
                    RiskInfo riskInfo = JSONObject.parseObject(cursor.next().toJson(settings), RiskInfo.class);
                    RiskInfoDto riskInfoDto = getRiskInfoDto(riskInfo, nodeMap);
                    if (DataUtil.isNotEmpty(riskInfoDto)) {
                        List<String> items = getRow(riskInfoDto, riskInfoExportDto);
                        rows.add(items);
                    }
                    if (rows.size() >= singleCount) {
                        for (List<String> items : rows) {
                            if (DataUtil.isNotEmpty(items) && !items.isEmpty()) {
                                printer.printRecord(items.stream().map(e -> "\t" + e + "\t").collect(Collectors.toList()));
                            }
                        }
                        rows.clear();
                    }
                }
                //最后发送剩余数据
                if (!rows.isEmpty()) {
                    for (List<String> items : rows) {
                        if (DataUtil.isNotEmpty(items) && !items.isEmpty()) {
                            printer.printRecord(items.stream().map(e -> "\t" + e + "\t").collect(Collectors.toList()));
                        }
                    }
                }
                printer.flush();
                if (productType.equals(ProductTypeEnum.wph.name())) {
                    WphFileGatewayDto wphFileGatewayDto = wphSendService.getWphFileGatewayDto(riskInfoExportDto.getApplicant_id(), riskInfoExportDto.getTags(), riskInfoExportDto.getReasons(), riskInfoExportDto.getUsage(), rows.size());
                    ResponseVo responseVo = wphSendService.postSendFile(wphFileGatewayDto, filePath);
                }
            }
        } catch (IOException e) {
            log.error("exportRiskInfo error:", e);
        }
        return filePath;
    }

    public List<String> getRow(RiskInfoDto riskInfoDto, RiskInfoExportDto riskInfoExportDto) throws Exception {

        List<String> row = new ArrayList<String>();
        for (ExporTitleFieldDto exporTitleFieldDto : riskInfoExportDto.getList()) {
            //异常标识
            if ("riskMark".equals(exporTitleFieldDto.getField())) {
                row.add(DataUtil.isEmpty(riskInfoDto.getRiskMark()) ? "未置顶" : riskInfoDto.getRiskMark() == true ? "已置顶" : "未置顶");
            }
            //异常时间
            if ("firstTime".equals(exporTitleFieldDto.getField())) {
                row.add(com.quanzhi.auditapiv2.common.util.utils.DateUtil.format(riskInfoDto.getFirstTime()));
            }
            //异常描述
            if ("riskDesc".equals(exporTitleFieldDto.getField())) {
                row.add(DataUtil.isEmpty(riskInfoDto.getRiskDesc()) ? "--" : riskInfoDto.getRiskDesc().replaceAll("<h>", "").replaceAll("</h>", ""));
            }
            //来源节点
            if ("nodes".equals(exporTitleFieldDto.getField())) {
                if (riskInfoDto.getNodes() != null) {
                    List<String> nodeNames = new ArrayList<>();
                    for (ResourceEntity.Node node : riskInfoDto.getNodes()) {
                        if (node.getNname() != null) {
                            nodeNames.add(node.getNname());
                        }
                    }
                    if (nodeNames.isEmpty()) {
                        row.add("");
                    } else {
                        row.add(String.join(",", nodeNames));
                    }
                } else {
                    row.add("");
                }

            }
            //异常名称
            if ("riskName".equals(exporTitleFieldDto.getField())) {
                row.add(riskInfoDto.getRiskPolicyName());
            }
            //应用
            if ("host".equals(exporTitleFieldDto.getField())) {
                row.add(DataUtil.isEmpty(riskInfoDto.getHost()) ? "--" : riskInfoDto.getHost());
            }
            //应用名称
            if ("appName".equals(exporTitleFieldDto.getField())) {
                row.add(DataUtil.isEmpty(riskInfoDto.getAppName()) ? "--" : riskInfoDto.getAppName());
            }
            //异常类型
            if ("riskType".equals(exporTitleFieldDto.getField())) {
                row.add(riskInfoDto.getRiskPolicyGroup());
            }
            //异常主体
            if ("riskSubject".equals(exporTitleFieldDto.getField())) {
                String value = riskInfoDto.getEntities().get(0).getValue();
                if (DataUtil.isEmpty(value)) {
                    row.add("--");
                } else {
                    String subject = value.replaceAll("(httpapp:|httpapi:)", "");
                    row.add(subject);
                }
            }
            //主体类型
            if ("entitiesType".equals(exporTitleFieldDto.getField())) {
                String type = riskInfoDto.getEntities().get(0).getType();
                if (DataUtil.isEmpty(type)) {
                    row.add("--");
                } else {
                    row.add(type);
                }
            }
            //异常等级
            if ("level".equals(exporTitleFieldDto.getField())) {
                row.add(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()).getName());
            }
            //异常状态
            if ("state".equals(exporTitleFieldDto.getField())) {
                row.add(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()).getName());
            }
            //异常运营ID
            if ("operationId".equals(exporTitleFieldDto.getField())) {
                row.add(riskInfoDto.getOperationId());
            }
            //处理意见
            if ("remark".equals(exporTitleFieldDto.getField())) {
                row.add(DataUtil.isEmpty(riskInfoDto.getRemark()) ? "--" : riskInfoDto.getRemark());
            }
            //攻击结果
            if ("attackSuccess".equals(exporTitleFieldDto.getField())) {
                row.add(riskInfoDto.getAttackSuccess() == null ? "--" : GroupDataDto.AttackSuccessEnum.getAttackSuccessEnum(riskInfoDto.getAttackSuccess()).getName());
            }
            //攻击次数
            if ("attackCount".equals(exporTitleFieldDto.getField())) {
                row.add(riskInfoDto.getAttackCount() == null ? "0" : String.valueOf(riskInfoDto.getAttackCount()));
            }
            //去重返回数据量
            if ("rspDataDistinctCnt".equals(exporTitleFieldDto.getField())) {
                row.add(riskInfoDto.getRspDataDistinctCnt() == null ? "0" : String.valueOf(riskInfoDto.getRspDataDistinctCnt()));
            }
            //返回数据标签
            if ("rspLabelList".equals(exporTitleFieldDto.getField())) {
                row.add(riskInfoDto.getRspLabelListValue() == null ? "--" : String.join(",", riskInfoDto.getRspLabelListValue()));
            }
            //最近发现时间
            if ("lastTime".equals(exporTitleFieldDto.getField())) {
                row.add(com.quanzhi.auditapiv2.common.util.utils.DateUtil.format(riskInfoDto.getLastTime()));
            }
            //自定义字段
            if ("customField".equals(exporTitleFieldDto.getField())) {

                Map<String, String> pairMap = new HashMap<String, String>();

                if (DataUtil.isNotEmpty(riskInfoDto.getDepartments())) {

                    HttpAppResource.Department department = riskInfoDto.getDepartments().get(0);

                    if (DataUtil.isNotEmpty(department.getProperties())) {

                        for (Pair<String> pair : department.getProperties()) {

                            pairMap.put(pair.getKey(), pair.getValue());
                        }
                    }
                }

                for (String customField : exporTitleFieldDto.getCustomFieldList()) {

                    row.add(pairMap.get(customField) == null ? "" : pairMap.get(customField));
                }
            }
            // 建单状态
            if ("orderFlag".equals(exporTitleFieldDto.getField())) {
                row.add(riskInfoDto.getOrderFlagName());
            }
        }

        return row;

    }

    /**
     * @param id
     * @param describeArray
     * <AUTHOR>
     * @description: 拼接异常描述
     * @date: 2021/8/25
     * @Return java.lang.String
     */
    public String getRiskDescByTemplate(String id, String[] describeArray, Integer version) {
        if (DataUtil.isNotEmpty(riskDescTemplateList)) {
            for (RiskDescTemplate riskDescTemplate : riskDescTemplateList) {
                if (riskDescTemplate.getPolicyId().equals(id)) {
                    String describeTemplate = riskDescTemplate.getDescribe();
                    if (riskDescTemplate.getPolicyId().equals(DefinedEvent.PolicyEnum.POLICY_20.getPolicyId())) {
                        //短信炸弹区分版本
                        if (version == null || version != 3) {
                            describeTemplate = "IP <h>{0}</h> 于 <h>{1}</h> 在短信发送API <h>{2}</h> 上对手机号进行短信炸弹攻击";
                        }
                    }
                    if (riskDescTemplate.getPolicyId().equals(DefinedEvent.PolicyEnum.POLICY_1.getPolicyId())) {
                        //api单次返回大量敏感数据区分版本
                        if (version == null || version != 3) {
                            describeTemplate = "API <h>{0}</h> 在当日 <h>{1}</h> 有 <h>{2}</h> 条事件的返回数据量过大，使用IP <h>{3}</h> 个，涉及数据标签种类 <h>{4}</h> 种";
                        }
                    }
                    if (riskDescTemplate.getPolicyId().equals(DefinedEvent.PolicyEnum.POLICY_2.getPolicyId())) {
                        //登录API存在弱密码登录账号区分版本
                        if (version == null || version != 3) {
                            describeTemplate = "登录API <h>{0}</h> 于 <h>{1}</h> 在上共使用弱密码登录成功 <h>{2}</h> 次，使用IP个数 <h>{3}</h> 个，使用账号个数 <h>{4}</h> 个";
                        }
                    }
                    //排除null值
                    for (int i = 0; i < describeArray.length; i++) {
                        if (describeArray[i] == null) {
                            describeArray[i] = "";
                        }
                    }
                    String desc = MessageFormat.format(describeTemplate, describeArray);
                    return desc;
                }
            }
        }
        return "";
    }

    public String getCustomRiskDescByTemplate(String type, String[] describeArray, Long baseLine, Integer visitCnt) {
        if (DataUtil.isEmpty(describeArray)) {
            return "";
        }
        String desc = "";
        if (DataUtil.isNotEmpty(riskDescTemplateList)) {
            if (type.contains("IP")) {
                //基线描述处理
                if (DataUtil.isNotEmpty(baseLine) && DataUtil.isNotEmpty(visitCnt)) {
                    if (baseLine <= 0) {
                        String describe = "IP <h>{0}</h> 于 <h>{1}</h> 在API <h>{2}</h> 上访问 " + visitCnt + " 次，超过历史基线值，涉及数据量 <h>{3}</h> 条，数据标签种类 <h>{4}</h> 种。";
                        desc = MessageFormat.format(describe, describeArray);
                    } else {
                        BigDecimal a = new BigDecimal(visitCnt - baseLine);
                        BigDecimal b = new BigDecimal(baseLine);
                        long r = a.divide(b, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).longValue();
                        String describe;
                        if (r <= 0) {
                            describe = "IP <h>{0}</h> 于 <h>{1}</h> 在API <h>{2}</h> 上访问 " + visitCnt + " 次，超过历史基线值，涉及数据量 <h>{3}</h> 条，数据标签种类 <h>{4}</h> 种。";
                        } else {
                            describe = "IP <h>{0}</h> 于 <h>{1}</h> 在API <h>{2}</h> 上访问 " + visitCnt + " 次，超过历史基线值（" + baseLine + "次）" + r + "%，涉及数据量 <h>{3}</h> 条，数据标签种类 <h>{4}</h> 种。";
                        }
                        desc = MessageFormat.format(describe, describeArray);
                    }
                } else {
                    List<RiskDescTemplate> templates = riskDescTemplateList.stream().filter(riskDescTemplate -> "ip".equals(riskDescTemplate.getPolicyId())).collect(Collectors.toList());
                    if (templates.size() > 0) {
                        RiskDescTemplate ipTemplate = templates.get(0);
                        String describe = ipTemplate.getDescribe();
                        desc = MessageFormat.format(describe, describeArray);
                    }
                }
                return desc;
            } else if (type.contains("ACCOUNT")) {
                if (DataUtil.isNotEmpty(baseLine) && DataUtil.isNotEmpty(visitCnt)) {
                    if (baseLine <= 0) {
                        String describe = "账号 <h>{0}</h> 于 <h>{1}</h> 在API <h>{2}</h> 上访问 " + visitCnt + " 次，超过历史基线值，涉及数据量 <h>{3}</h> 条，数据标签种类 <h>{4}</h> 种。";
                        desc = MessageFormat.format(describe, describeArray);
                    } else {
                        BigDecimal a = new BigDecimal(visitCnt - baseLine);
                        BigDecimal b = new BigDecimal(baseLine);
                        long r = a.divide(b, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).longValue();
                        String describe;
                        if (r < 0) {
                            describe = "账号 <h>{0}</h> 于 <h>{1}</h> 在API <h>{2}</h> 上访问 " + visitCnt + " 次，超过历史基线值，涉及数据量 <h>{3}</h> 条，数据标签种类 <h>{4}</h> 种。";
                        } else {
                            describe = "账号 <h>{0}</h> 于 <h>{1}</h> 在API <h>{2}</h> 上访问 " + visitCnt + " 次，超过历史基线值（" + baseLine + "次）" + r + "%，涉及数据量 <h>{3}</h> 条，数据标签种类 <h>{4}</h> 种。";
                        }
                        desc = MessageFormat.format(describe, describeArray);
                    }
                } else {
                    List<RiskDescTemplate> templates = riskDescTemplateList.stream().filter(riskDescTemplate -> "account".equals(riskDescTemplate.getPolicyId())).collect(Collectors.toList());
                    if (templates.size() > 0) {
                        RiskDescTemplate accountTemplate = templates.get(0);
                        String describe = accountTemplate.getDescribe();
                        desc = MessageFormat.format(describe, describeArray);
                    }
                }
                return desc;
            }
        }
        return "";
    }

    /**
     * @param riskSearchDto
     * <AUTHOR>
     * @description: 异常事件分组
     * @date: 2021/8/20
     * @Return java.util.List<com.quanzhi.auditapiv2.core.risk.dto.common.CommonGroupDto>
     */
    public List<CommonGroupDto> selectRiskInfoGroup(RiskSearchDto riskSearchDto) {
        String groupField = riskSearchDto.getGroupField();
        List<CommonGroupDto> commonGroupDtoList = new ArrayList<>();
        GroupDto groupDto = new GroupDto();
        groupDto.setGroupFields(new String[]{groupField});
        groupDto.setUnwind(groupField);
        groupDto.setAggrType(GroupDto.GroupTypeEnum.COUNT.getName());
        if (RiskSearchDto.GroupFieldEnum.RISK_STATE.getName().equals(groupField)) {
            riskSearchDto.setStateGroup(true);
        }
        try {
            List<AggregationDto> riskInfoGroup = getRiskInfoGroup(riskSearchDto, groupDto, riskSearchDto.getSortField(), riskSearchDto.getSort(), riskSearchDto.getPage(), riskSearchDto.getLimit());
            riskInfoGroup.forEach(e -> {
                if (RiskSearchDto.GroupFieldEnum.RISK_LEVEL.getName().equals(groupField)) {
                    //等级
                    Integer level = Integer.valueOf(e.getId());
                    String name = RiskInfo.RiskLevelEnum.getRiskLevelEnum(level).getName();
                    commonGroupDtoList.add(CommonGroupDto.builder().name(name).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                } else if (RiskSearchDto.GroupFieldEnum.RISK_MARK.getName().equals(groupField)) {
                    //置顶
                    String riskMark = e.getId();
                    String name = "true".equals(riskMark) ? "已置顶" : "未置顶";
                    commonGroupDtoList.add(CommonGroupDto.builder().name(name).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                } else if (RiskSearchDto.GroupFieldEnum.RISK_STATE.getName().equals(groupField)) {
                    //状态
                    Integer state = Integer.valueOf(e.getId());
                    String name = RiskInfo.RiskStateEnum.getRiskStateEnum(state).getName();
                    commonGroupDtoList.add(CommonGroupDto.builder().name(name).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                } else if (RiskSearchDto.GroupFieldEnum.ATTACK_SUCCESS.getName().equals(groupField)) {
                    //攻击结果
                    Integer attackSuccess = Integer.valueOf(e.getId());
                    commonGroupDtoList.add(CommonGroupDto.builder().name(GroupDataDto.AttackSuccessEnum.getAttackSuccessEnum(attackSuccess).getName()).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                } else if (RiskSearchDto.GroupFieldEnum.ORDER_FLAG.getName().equals(groupField)) {
                    //建单状态
                    Integer flagId = Integer.valueOf(e.getId());
                    String name = OrderFlagEnum.getOrderFlagEnum(flagId).getName();
                    commonGroupDtoList.add(CommonGroupDto.builder().name(name).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                } else {
                    commonGroupDtoList.add(CommonGroupDto.builder().name(e.getId()).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build());
                }
            });
        } catch (Exception exception) {
            logger.error("异常事件分组出错:", exception);
        }

        //排序，保证结果顺序一致
        Map<Long, List<CommonGroupDto>> collect = commonGroupDtoList.stream().collect(Collectors.groupingBy(CommonGroupDto::getCount));
        Set<Long> sortKey = new TreeSet<>(Long::compareTo);
        sortKey.addAll(collect.keySet());
        commonGroupDtoList.clear();
        for (Long key : sortKey) {
            if (collect.get(key).size() > 1) {
                collect.get(key).sort(Comparator.comparing(CommonGroupDto::getName));
                commonGroupDtoList.addAll(collect.get(key));
            } else {
                commonGroupDtoList.add(collect.get(key).get(0));
            }
        }
        Collections.reverse(commonGroupDtoList);
        //账号使用中文名
        for (CommonGroupDto commonGroupDto : commonGroupDtoList) {
            if ("ACCOUNT".equals(commonGroupDto.getId())) {
                commonGroupDto.setName("账号");
            }
        }
        //等级反序
        if (RiskSearchDto.GroupFieldEnum.RISK_LEVEL.getName().equals(groupField)) {
            commonGroupDtoList.sort(Comparator.comparingInt(o -> Integer.parseInt(o.getId())));
            Collections.reverse(commonGroupDtoList);
        }

        return commonGroupDtoList;
    }

    /**
     * 获取异常白名单类型
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    private List<String> getRiskWhiteType(RiskInfoDto riskInfoDto) throws Exception {

        String ip = riskInfoDto.getIp();
        String account = Policy.EntityEnum.ACCOUNT.name().equals(riskInfoDto.getEntities().get(0).getType()) ? riskInfoDto.getEntities().get(0).getValue() : null;
        String appUri = riskInfoDto.getAppUri();
        String apiUri = riskInfoDto.getApiUri();

        List<String> list = new ArrayList<String>();

        //异常规则id
        String policyId = riskInfoDto.getRiskPolicyId();

        if (DataUtil.isNotEmpty(ip)) {

            if (DataUtil.isNotEmpty(riskPolicyAllowListService.getRiskPolicyAllowList(policyId, Policy.EntityEnum.IP.name(), ip))) {

                list.add(Policy.EntityEnum.IP.name());
            }
        }

        if (DataUtil.isNotEmpty(account)) {

            if (DataUtil.isNotEmpty(riskPolicyAllowListService.getRiskPolicyAllowList(policyId, Policy.EntityEnum.ACCOUNT.name(), account))) {

                list.add(Policy.EntityEnum.ACCOUNT.name());
            }
        }

        if (DataUtil.isNotEmpty(apiUri)) {

            if (DataUtil.isNotEmpty(riskPolicyAllowListService.getRiskPolicyAllowList(policyId, Policy.EntityEnum.API.name(), apiUri))) {

                list.add(Policy.EntityEnum.API.name());
            }

            HttpApiResource htpApiResource = httpApiServiceImpl.getHttpApiByUri(apiUri);
            if (DataUtil.isNotEmpty(htpApiResource)) {

                appUri = htpApiResource.getAppUri();
            }
        }

        if (DataUtil.isNotEmpty(appUri)) {

            if (DataUtil.isNotEmpty(riskPolicyAllowListService.getRiskPolicyAllowList(policyId, Policy.EntityEnum.APP.name(), appUri))) {

                list.add(Policy.EntityEnum.APP.name());
            }
        }

        return list;
    }

    /**
     * 批量忽略异常事件
     *
     * @param operator 操作账号
     * @param remark   处理意见
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    private void ingoreRiskInfo(RiskInfo riskInfo, String operator, String remark) throws Exception {

        if (DataUtil.isNotEmpty(riskInfo.getIgnoreType())) {

            String policyId = riskInfo.getPolicySnapshot().getId();
            String ingoreType = riskInfo.getIgnoreType();
            String entitiesValue = null;
            String channelsValue = null;

            //获取忽略主体
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : riskInfo.getEntities()) {

                if (RiskInfo.IgnoreTypeEnum.IP.name().equals(riskInfo.getIgnoreType()) || riskInfo.getIgnoreType().contains("IP") && Policy.EntityEnum.IP.name().equals(entity.getType())) {

                    entitiesValue = entity.getValue();
                    break;
                } else if (RiskInfo.IgnoreTypeEnum.ACCOUNT.name().equals(riskInfo.getIgnoreType()) || riskInfo.getIgnoreType().contains("ACCOUNT") && Policy.EntityEnum.ACCOUNT.name().equals(entity.getType())) {

                    entitiesValue = entity.getValue();
                    break;
                }
            }

            if (riskInfo.getVersion() == 3) {
                if ((RiskInfo.IgnoreTypeEnum.IP_HOST.name().equals(riskInfo.getIgnoreType()) || RiskInfo.IgnoreTypeEnum.ACCOUNT_HOST.name().equals(riskInfo.getIgnoreType()))) {

                    channelsValue = riskInfo.getAppUri();
                } else if ((RiskInfo.IgnoreTypeEnum.IP_API.name().equals(riskInfo.getIgnoreType()) || RiskInfo.IgnoreTypeEnum.ACCOUNT_API.name().equals(riskInfo.getIgnoreType()))) {

                    channelsValue = riskInfo.getApiUri();
                }
            } else {
                //获取关联忽略信息
                for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : riskInfo.getChannels()) {

                    if ((RiskInfo.IgnoreTypeEnum.IP_HOST.name().equals(riskInfo.getIgnoreType()) || RiskInfo.IgnoreTypeEnum.ACCOUNT_HOST.name().equals(riskInfo.getIgnoreType())) && Policy.EntityEnum.APP.name().equals(entity.getType())) {

                        channelsValue = entity.getValue();
                        break;
                    } else if ((RiskInfo.IgnoreTypeEnum.IP_API.name().equals(riskInfo.getIgnoreType()) || RiskInfo.IgnoreTypeEnum.ACCOUNT_API.name().equals(riskInfo.getIgnoreType())) && Policy.EntityEnum.API.name().equals(entity.getType())) {

                        channelsValue = entity.getValue();
                        break;
                    }
                }
            }

            if (DataUtil.isNotEmpty(entitiesValue)) {
                Criteria ingoreRiskCriteria = getIngoreRiskCriteria(policyId, ingoreType, entitiesValue, channelsValue, riskInfo.getVersion());
                List<RiskInfo> riskInfos = riskInfoDaoImpl.getAllByCriteria(ingoreRiskCriteria, Arrays.asList("_id"));
                for (RiskInfo risk : riskInfos) {
                    //忽略
                    riskInfoDaoImpl.update(risk.getId(), ResourceUpdates.create().set("state", 1).set("updateTime", System.currentTimeMillis()).set("operateName", operator).set("remark", remark), "riskInfo");
                    //记录操作日志
                    String newRiskStateName = RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfo.getState()).getName();
                    insertRiskOperationLog(risk.getId(), operator, newRiskStateName, remark);
                }
            }
        }
    }

    private Criteria getIngoreRiskCriteria(String policyId, String type, String entitiesValue, String channelsValue, Integer version) {
        Criteria criteria = Criteria.where("policySnapshot.id").is(policyId);
        if (version == 3) {
            criteria.and("entities.value").is(entitiesValue);
            if (RiskInfo.IgnoreTypeEnum.IP_HOST.name().equals(type) || RiskInfo.IgnoreTypeEnum.ACCOUNT_HOST.name().equals(type)) {
                criteria.and("appUri").is(channelsValue);
            }
            if (RiskInfo.IgnoreTypeEnum.IP_API.name().equals(type) || RiskInfo.IgnoreTypeEnum.ACCOUNT_API.name().equals(type)) {
                criteria.and("apiUri").is(channelsValue);
            }
        } else {
            if (RiskInfo.IgnoreTypeEnum.IP.name().equals(type) || RiskInfo.IgnoreTypeEnum.ACCOUNT.name().equals(type)) {
                criteria.and("entities.value").is(entitiesValue);
            } else if (RiskInfo.IgnoreTypeEnum.IP_HOST.name().equals(type) || RiskInfo.IgnoreTypeEnum.IP_API.name().equals(type) || RiskInfo.IgnoreTypeEnum.ACCOUNT_HOST.name().equals(type) || RiskInfo.IgnoreTypeEnum.ACCOUNT_API.name().equals(type)) {
                criteria.and("entities.value").is(entitiesValue);
                criteria.and("channels.value").is(channelsValue);
            }
        }
        return criteria;
    }


    /**
     * 异常白名单
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    private void whiteRiskInfo(List<String> whiteTypeList, RiskInfo riskInfo) throws Exception {

        //异常规则id
        String policyId = riskInfo.getPolicySnapshot().getId();
        List<com.quanzhi.audit_core.common.risk.RiskInfo.Entity> entities = riskInfo.getEntities();
        List<com.quanzhi.audit_core.common.risk.RiskInfo.Entity> channels = riskInfo.getChannels();
        List<com.quanzhi.audit_core.common.risk.RiskInfo.Entity> allEntities = new ArrayList<>();
        allEntities.addAll(entities);
        allEntities.addAll(channels);

        if (DataUtil.isNotEmpty(whiteTypeList)) {
            List<RiskPolicyAllowList> updatelist = new ArrayList<RiskPolicyAllowList>();

            for (String whiteType : whiteTypeList) {

                String whiteValue = null;

                if (Policy.EntityEnum.IP.name().equals(whiteType)) {

                    whiteValue = riskInfo.getIp();
                } else if (Policy.EntityEnum.ACCOUNT.name().equals(whiteType)) {
                    for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : allEntities) {
                        if (Policy.EntityEnum.ACCOUNT.name().equals(entity.getType())) {
                            whiteValue = entity.getValue();
                        }
                    }
                } else if (Policy.EntityEnum.APP.name().equals(whiteType)) {

                    whiteValue = riskInfo.getAppUri();
                } else if (Policy.EntityEnum.API.name().equals(whiteType)) {

                    whiteValue = riskInfo.getApiUri();
                }

                if (DataUtil.isEmpty(whiteValue)) {
                    if (Policy.EntityEnum.IP.name().equals(whiteType)) {
                        for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : allEntities) {
                            if (Policy.EntityEnum.IP.name().equals(entity.getType())) {
                                whiteValue = entity.getValue();
                            }
                        }
                    } else if (Policy.EntityEnum.ACCOUNT.name().equals(whiteType)) {
                        for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : allEntities) {
                            if (Policy.EntityEnum.ACCOUNT.name().equals(entity.getType())) {
                                whiteValue = entity.getValue();
                            }
                        }
                    } else if (Policy.EntityEnum.APP.name().equals(whiteType)) {
                        for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : allEntities) {
                            if (Policy.EntityEnum.APP.name().equals(entity.getType())) {
                                whiteValue = entity.getValue();
                            }
                        }
                    } else if (Policy.EntityEnum.API.name().equals(whiteType)) {
                        for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : allEntities) {
                            if (Policy.EntityEnum.API.name().equals(entity.getType())) {
                                whiteValue = entity.getValue();
                            }
                        }
                    }
                }

                if (DataUtil.isNotEmpty(whiteValue)) {

                    RiskPolicyAllowList riskPolicyAllowList = riskPolicyAllowListService.getRiskPolicyAllowList(policyId, whiteType, whiteValue);
                    if (DataUtil.isEmpty(riskPolicyAllowList)) {

                        riskPolicyAllowList = new RiskPolicyAllowList();
                        //异常规则id
                        riskPolicyAllowList.setPolicyId(policyId);
                        //白名单类型
                        riskPolicyAllowList.setType(whiteType);
                        //白名单内容
                        riskPolicyAllowList.setValue(whiteValue);
                        updatelist.add(riskPolicyAllowList);
                    }
                }
            }

            riskPolicyAllowListService.addRiskPolicyAllowList(updatelist);
        } else {
            //删除对应白名单
            List<String> deletelist = new ArrayList<String>();
            for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity allEntity : allEntities) {
                RiskPolicyAllowList riskPolicyAllowList = riskPolicyAllowListService.getRiskPolicyAllowList(policyId, allEntity.getType(), allEntity.getValue());
                if (DataUtil.isNotEmpty(riskPolicyAllowList)) {
                    deletelist.add(riskPolicyAllowList.getId());
                }
            }
            if (DataUtil.isNotEmpty(riskInfo.getAppUri())) {
                RiskPolicyAllowList riskPolicyAllowList = riskPolicyAllowListService.getRiskPolicyAllowList(policyId, "APP", riskInfo.getAppUri());
                if (DataUtil.isNotEmpty(riskPolicyAllowList) && !deletelist.contains(riskPolicyAllowList.getId())) {
                    deletelist.add(riskPolicyAllowList.getId());
                }
            }
            if (DataUtil.isNotEmpty(riskInfo.getApiUri())) {
                RiskPolicyAllowList riskPolicyAllowList = riskPolicyAllowListService.getRiskPolicyAllowList(policyId, "API", riskInfo.getApiUri());
                if (DataUtil.isNotEmpty(riskPolicyAllowList) && !deletelist.contains(riskPolicyAllowList.getId())) {
                    deletelist.add(riskPolicyAllowList.getId());
                }
            }
            if (DataUtil.isNotEmpty(riskInfo.getIp())) {
                RiskPolicyAllowList riskPolicyAllowList = riskPolicyAllowListService.getRiskPolicyAllowList(policyId, "IP", riskInfo.getIp());
                if (DataUtil.isNotEmpty(riskPolicyAllowList) && !deletelist.contains(riskPolicyAllowList.getId())) {
                    deletelist.add(riskPolicyAllowList.getId());
                }
            }
            riskPolicyAllowListService.delRiskPolicyAllowList(deletelist);
        }
    }

    /**
     * 统计异常事件数量
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-31 16:52
     */
    public List<CommonGroupDto> riskTypeDonutChartByAppUri(String appUri) throws Exception {

        List<CommonGroupDto> list = new ArrayList<CommonGroupDto>();

        GroupDto groupDto = new GroupDto();
        //分组字段
        groupDto.setGroupFields(new String[]{"name"});
        //聚合类型
        groupDto.setAggrType("COUNT");

        List<AggregationDto> aggregationDtoList = riskInfoDaoImpl.riskTypeDonutChartByAppUri(appUri, groupDto);
        for (AggregationDto aggregationDto : aggregationDtoList) {

            CommonGroupDto commonGroupDto = CommonGroupDto.builder().id(aggregationDto.getId()).count(Long.valueOf(String.valueOf(aggregationDto.getResultAlias()))).name(aggregationDto.getId()).build();

            list.add(commonGroupDto);
        }

        return list;
    }

    public long countByRiskLevels(List<Integer> riskLevel) {
        return riskInfoDaoImpl.countByCriteria("riskInfo", Criteria.where("state").in(RiskInfo.RiskStateEnum.NOT_HANDLE.getState()).and("level").in(riskLevel));
    }

    /**
     * 获取系统异常趋势（包含总异常和确认异常）
     *
     * @return
     */
    public List<SystemRiskTrendDto> getSystemRiskTrend() {
        List<SystemRiskTrendDto> result = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        /*//当前时间的时间戳
        Long time = System.currentTimeMillis();
        //今日零点时间戳
        long zero = time / (1000 * 3600 * 24) * (1000 * 3600 * 24) - TimeZone.getDefault().getRawOffset();*/

        long zero = com.quanzhi.auditapiv2.common.util.utils.DateUtil.getTodayZeroTimeStamp();

        long startTime = zero - 6 * 24 * 60 * 60 * 1000;
        long endTime = zero + 24 * 60 * 60 * 1000 - 1;
        List<RiskTrend> systemRiskTrend1 = riskInfoDaoImpl.getSystemRiskTrend(startTime, endTime, true);
        //按日期分组
        Map<String, Integer> groupByDate1 = new HashMap<>();
        for (RiskTrend riskTrend : systemRiskTrend1) {
            groupByDate1.put(riskTrend.getDate(), riskTrend.getCount());
        }
        List<RiskTrend> systemRiskTrend2 = riskInfoDaoImpl.getSystemRiskTrend(startTime, endTime, false);
        //按日期分组
        Map<String, Integer> groupByDate2 = new HashMap<>();
        for (RiskTrend riskTrend : systemRiskTrend2) {
            groupByDate2.put(riskTrend.getDate(), riskTrend.getCount());
        }
        //根据时间戳获取日期对象
        DateTime start = DateUtil.date(startTime);
        DateTime end = DateUtil.date(endTime);
        List<DateTime> dateTimes = DateUtil.rangeToList(start, end, DateField.DAY_OF_WEEK);
        //生成一月数据的数组
        for (DateTime dateTime : dateTimes) {
            SystemRiskTrendDto riskTrendDto = SystemRiskTrendDto.builder().date(DateUtil.date(dateTime).toString("yyyyMMdd")).confirmViews(0).totalViews(0).build();
            //存在对应日期的实际数据，进行数据填充
            if (groupByDate1.containsKey(riskTrendDto.getDate())) {
                riskTrendDto.setConfirmViews(groupByDate1.get(riskTrendDto.getDate()));
            }
            if (groupByDate2.containsKey(riskTrendDto.getDate())) {
                riskTrendDto.setTotalViews(groupByDate2.get(riskTrendDto.getDate()));
            }
            result.add(riskTrendDto);
        }
        return result;
    }

    public List<IpCountEntity> getRiskCountryDistribute(List<Integer> state) {
        return riskInfoDaoImpl.getRiskDistributeByIp(state);
    }

    /**
     * <AUTHOR>
     * @description: 获取主体为ip, 异常类型为数据泄漏类, 状态不为已忽略的ip分组
     * @date: 2021/9/23
     * @Return java.util.List<com.quanzhi.auditapiv2.common.dal.entity.IpCountEntity>
     */
    public List<IpCountEntity> getRiskCountryDistributeByDataReveal() {
        return riskInfoDaoImpl.getRiskCountryDistributeByDataReveal(RiskConstant.DATA_REVEAL_IP_RISK);
    }

    /**
     * @param ip
     * <AUTHOR>
     * @description: 获取ip的确认异常事件数量
     * @date: 2021/9/23
     * @Return long
     */
    public long getIpConfirmRiskNum(String ip) {
        MetabaseQuery query = new MetabaseQuery();
//        query.where("entities.type", Predicate.IS, "IP");
        query.where("entities.value", Predicate.IS, ip);
        query.where("state", Predicate.IS, RiskInfo.RiskStateEnum.HAS_HANDLE.getState());
        query.where("policySnapshot.name", Predicate.IN, RiskConstant.DATA_REVEAL_IP_RISK);
        long count = riskInfoDaoImpl.count(query, "riskInfo");
        return count;
    }

    /**
     * 获取大屏概览信息 - 总异常事件、高异常异常事件
     *
     * @return
     */
    public List<Long> getStatistics() {
        return riskInfoDaoImpl.getStatistics();
    }

    /**
     * 根据日期获取异常信息
     *
     * @return
     */
    public List<RiskInfo> listRiskInfoByDate() throws Exception {
        BigScreenConfig bigScreenConfig = bigScreenConfigService.selectBigScreenConfig();
        List<String> domains = new ArrayList<>();
        for (int i = 0; i < bigScreenConfig.getNetworkOne().getMonitorDomain().size(); i++) {
            String newAppUri = "httpapp:" + bigScreenConfig.getNetworkOne().getMonitorDomain().get(i);
            domains.add(newAppUri);
        }
        for (int i = 0; i < bigScreenConfig.getNetworkTwo().getMonitorDomain().size(); i++) {
            String newAppUri = "httpapp:" + bigScreenConfig.getNetworkTwo().getMonitorDomain().get(i);
            domains.add(newAppUri);
        }
        Calendar calendar = Calendar.getInstance();
        // 获取今天的日期和时间
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);

        Date todayEndTime = calendar.getTime();
        long endTime = todayEndTime.getTime();
        long startTime = endTime - 1000 * 60 * 60 * 24 * 3;
        return riskInfoDaoImpl.listRiskInfoByDate(startTime, endTime, domains);
    }


    /**
     * @param timeStamp
     * <AUTHOR>
     * @description: 获取时间戳后的异常数据
     * @date: 2021/12/7
     * @Return java.util.List<com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto>
     */
    public ListOutputDto<RiskInfoDto> syncRiskData(Integer page, Integer limit, Long timeStamp) {
        ListOutputDto<RiskInfoDto> listOutputDto = new ListOutputDto<>();
        List<RiskInfoDto> riskInfoDtoList = new ArrayList<>();
        Criteria criteria = null;
        if (timeStamp != null) {
            criteria = Criteria.where("updateTime").gte(timeStamp);
        } else {
            criteria = Criteria.where("updateTime").ne(null);
        }
        Long totalCount = riskInfoDaoImpl.countByCriteria("riskInfo", criteria);
        listOutputDto.setTotalCount(totalCount);
        if (totalCount == 0) {
            listOutputDto.setRows(riskInfoDtoList);
            return listOutputDto;
        }
        Pageable pageable = PageRequest.of(page - 1, limit);
        Sort sort = Sort.by(Sort.Direction.ASC, "updateTime");
        List<RiskInfo> riskInfos = riskInfoDaoImpl.pageByCriteria("riskInfo", criteria, sort, pageable);
        //List<RiskInfo> riskInfos = riskInfoDaoImpl.getAllByCriteria(criteria);

        conversionRisk(riskInfos, riskInfoDtoList);
        listOutputDto.setRows(riskInfoDtoList);
        return listOutputDto;
    }

    /**
     * @param riskInfos
     * @param riskInfoDtoList
     * <AUTHOR>
     * @description: 异常一些字段的转换
     * @date: 2021/12/15
     * @Return void
     */
    private void conversionRisk(List<RiskInfo> riskInfos, List<RiskInfoDto> riskInfoDtoList) {
        for (RiskInfo riskInfo : riskInfos) {
            RiskInfoDto riskInfoDto = RiskInfoDto.RiskInfoDtoMapper.INSTANCE.convert(riskInfo);
            //状态
            if (DataUtil.isNotEmpty(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()))) {
                riskInfoDto.setStateName(RiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoDto.getState()).getName());
            }
            //等级
            if (DataUtil.isNotEmpty(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()))) {
                riskInfoDto.setLevelName(RiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoDto.getLevel()).getName());
            }
            //描述
            RiskDescribeDto riskDescribeDto;
            try {
                riskDescribeDto = definedEventService.riskDescribe(riskInfo.getId());
                riskInfoDto.setRiskDescribe(riskDescribeDto);
            } catch (Exception e) {
                logger.error("获取异常描述出错:{}", e);
            }
            //异常描述字符串
            String[] riskDescribes = riskInfoDto.getRiskDescribe() == null ? new String[]{} : riskInfoDto.getRiskDescribe().getDescribeArray();
            String id = riskInfoDto.getPolicySnapshot() == null ? "" : riskInfoDto.getPolicySnapshot().getId();
            String riskDesc = "";
            riskDesc = getRiskDescByTemplate(id, riskDescribes, riskInfoDto.getVersion());
            if (DataUtil.isEmpty(riskDesc) || "".equals(riskDesc)) {
                StringBuilder type = new StringBuilder();
                if (DataUtil.isEmpty(riskInfoDto.getPolicySnapshot().getType())) {
                    for (Policy.EntityRelation entityConfig : riskInfoDto.getPolicySnapshot().getEntityConfigs()) {
                        type.append(entityConfig.getType());
                    }
                } else {
                    type.append(riskInfoDto.getPolicySnapshot().getType());
                }
                boolean baseLine = false;
                boolean activeTime = false;
                if (DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot()) && DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot().getQuotaRule())) {
                    if (DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot().getQuotaRule().getExprList())) {
                        for (Expression expression : riskInfo.getPolicySnapshot().getQuotaRule().getExprList()) {
                            if (DataUtil.isNotEmpty(expression.getRight())
                                    && DataUtil.isNotEmpty(expression.getRight().getType())
                                    && VariableType.NAMED.name().equals(expression.getRight().getType().name())
                                    && "baseline".equals(expression.getRight().getVarName())) {
                                baseLine = true;
                            }
                            if (expression.getLeft().getName().equals("活跃时间范围")) {
                                activeTime = true;
                            }
                        }
                    }
                }
                if (baseLine) {
                    if (activeTime) {
                        if ("IP".equals(riskInfoDto.getEntityType())) {
                            riskDesc = "IP <h>" + riskInfoDto.getIp() + "</h> 于 <h>" + riskInfoDto.getDate() + "</h> 在API <h>" + riskInfoDto.getApiUrl() + "</h> 上有 <h>" + riskInfoDto.getSampleCount() + "</h> 次访问偏离活跃时间范围。";
                        } else {
                            riskDesc = "账号 <h>" + riskInfoDto.getEntityValue() + "</h> 于 <h>" + riskInfoDto.getDate() + "</h> 在API <h>" + riskInfoDto.getApiUrl() + "</h> 上有 <h>" + riskInfoDto.getSampleCount() + "</h> 次访问偏离活跃时间范围。";
                        }
                    } else {
                        //基线描述特殊处理
                        Long baseline = riskInfoDto.getRiskDescribe().getBaseline();
                        Integer visitCnt = riskInfoDto.getRiskDescribe().getVisitCnt();
                        riskDesc = getCustomRiskDescByTemplate(type.toString(), riskInfoDto.getRiskDescribe().getDescribeArray(), baseline, visitCnt);
                    }
                } else {
                    riskDesc = getCustomRiskDescByTemplate(type.toString(), riskInfoDto.getRiskDescribe().getDescribeArray(), null, null);
                }
            }
            riskInfoDto.setRiskDesc(riskDesc);
            //异常主体
            if (DataUtil.isNotEmpty(riskInfoDto.getEntities())) {
                riskInfoDto.getEntities().forEach(e -> e.setValue(e.getValue().replaceAll("(httpapp:|httpapi:)", "")));
            }

            // 补充开放接口返回信息
//            this.fillRiskInfo(riskInfoDto);

            //异常名称
            riskInfoDto.setRiskPolicyName(riskInfo.getPolicySnapshot().getName());

            //异常类型
            riskInfoDto.setRiskPolicyGroup(riskInfo.getPolicySnapshot().getGroup());

            //策略id
            riskInfoDto.setRiskPolicyId(riskInfo.getPolicySnapshot().getId());

            riskInfoDtoList.add(riskInfoDto);
        }
    }

    /**
     * @param page
     * @param limit
     * <AUTHOR>
     * @description: 同步历史数据
     * @date: 2021/12/15
     */
    public ListOutputDto<RiskInfoDto> syncHistoryRiskData(Integer page, Integer limit) {
        ListOutputDto<RiskInfoDto> listOutputDto = new ListOutputDto<>();
        List<RiskInfoDto> riskInfoDtoList = new ArrayList<>();
        Criteria criteria = Criteria.where("updateTime").is(null);
        Long totalCount = riskInfoDaoImpl.countByCriteria("riskInfo", criteria);
        listOutputDto.setTotalCount(totalCount);
        if (totalCount == 0) {
            return listOutputDto;
        }
        Pageable pageable = PageRequest.of(page - 1, limit);
        List<RiskInfo> riskInfos = riskInfoDaoImpl.pageByCriteria("riskInfo", criteria, null, pageable);
        conversionRisk(riskInfos, riskInfoDtoList);
        listOutputDto.setRows(riskInfoDtoList);
        return listOutputDto;
    }

    public List<RiskOperationLog> syncRiskOperationLog(Integer page, Integer limit, Long timeStamp) {
        Criteria criteria = new Criteria();
        if (timeStamp != null) {
            criteria = Criteria.where("createTime").gte(timeStamp);
        }
        Pageable pageable = PageRequest.of(page - 1, limit);
        // Sort sort = Sort.by(Sort.Direction.ASC, "createTime");
        List<RiskOperationLog> logList = riskOperationLogDao.pageByCriteria("riskOperationLog", criteria, null, pageable);
        return logList;
    }

    public void update(String id, ResourceUpdates resourceUpdates) {
        riskInfoDaoImpl.update(id, resourceUpdates, "riskInfo");
    }

    /**
     * 获取全部数据标签
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    private Map<String, String> getDataLabeMap() {
        //数据标签
        Map<String, String> dataLabeMap = new HashMap<String, String>();
        Map<String, DataLabel> dataLabelMap = nacosDadaServiceBuilder.build().getDataLabelMap();
        dataLabelMap.forEach((k, v) -> {
            dataLabeMap.put(k, v.getName());
        });
        return dataLabeMap;
    }

    /**
     * 数据标签id转换
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    private List<String> transformationByDataLabe(List<String> labelList) throws Exception {

        List<String> list = new ArrayList<String>();

        if (DataUtil.isNotEmpty(labelList)) {

            //数据标签
            Map<String, String> dataLabeMap = getDataLabeMap();

            for (String labelId : labelList) {

                if (dataLabeMap.containsKey(labelId)) {
                    list.add(dataLabeMap.get(labelId));
                }
            }
        }

        return list;
    }

    public Long countRiskInfosByApiuri(String apiUri) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        metabaseQuery.where("apiUri", Predicate.IS, apiUri);
        List<Integer> states = new ArrayList<>();
        states.add(RiskInfo.RiskStateEnum.HAS_HANDLE.getState());
        states.add(RiskInfo.RiskStateEnum.NOT_HANDLE.getState());
        metabaseQuery.where("state", Predicate.IN, states);
        return riskInfoDaoImpl.count(metabaseQuery, "riskInfo");
    }

    public List<Integer> getRiskInfoLevelsBuApiUri(String apiUri) {
        List<Map> riskInfoLevelsBuApiUri = riskInfoDaoImpl.getRiskInfoLevelsBuApiUri(apiUri);
        List<Integer> levels = new ArrayList<>();
        for (Map map : riskInfoLevelsBuApiUri) {
            levels.add((Integer) map.get("level"));
        }
        return levels;
    }

    /**
     * @param riskInfoDto
     * @return void
     * <AUTHOR>
     * @date 2022/11/7 5:29 PM
     * @Description 补充开放接口返回信息
     * @Since
     */
    private void fillRiskInfo(RiskInfoDto riskInfoDto) {
        if (DataUtil.isEmpty(riskInfoDto)) {
            return;
        }

        riskInfoDto.setFindTimeMs(riskInfoDto.getFirstTime());
        riskInfoDto.setStatus(riskInfoDto.getStateName());

        if (DataUtil.isNotEmpty(riskInfoDto.getEntities())) {
            //主体类型
            if (DataUtil.isNotEmpty(riskInfoDto.getEntities()) && !riskInfoDto.getEntities().isEmpty()) {
                RiskInfoDto.Entity entity = riskInfoDto.getEntities().get(0);
                if (DataUtil.isNotEmpty(entity.getValue()) && DataUtil.isNotEmpty(entity.getType())) {
                    String type = riskInfoDto.getEntities().get(0).getType();
                    String value = riskInfoDto.getEntities().get(0).getValue();
                    riskInfoDto.setEntityType(type);
                    riskInfoDto.setEntityValue(value);
                    if (EntityTypeEnum.ACCOUNT.name().equals(type)) {
                        if (DataUtil.isNotEmpty(riskInfoDto.getDepartments())) {
                            riskInfoDto.setDepart(value);
                        }
                    } else if (EntityTypeEnum.IP.name().equals(type)) {
                        try {
                            List<NetworkSegment> networkSegmentsList = com.quanzhi.audit_core.resource.fetcher.client.utils.DataUtil.deepCopy(networkSegments);

                            List<NetworkSegmentIpInfo> networkSegmentConfigInfos = NetworkSegmentConverter.convert2(networkSegmentsList);
                            List<NetworkDomain> networkDomains = IpNetworkDomainFetcher.getNetworkDomains(value, networkSegmentConfigInfos);

                            if (DataUtil.isNotEmpty(networkDomains)) {
                                riskInfoDto.setDomainIds(networkDomains.stream().map(domain -> domain.getId()).collect(Collectors.toList()));
                            }
                        } catch (Exception e) {
                            log.error("获取ip网段信息失败", e);
                        }
                    }
                }
            }
        }

        // 异常id  类似operationId
        riskInfoDto.setRiskId("RISK" + riskInfoDto.getFirstTime() + MD5Util.md5(riskInfoDto.getId()));
        if (DataUtil.isEmpty(riskInfoDto.getRemark())) {
            riskInfoDto.setRemark("");
        }
        riskInfoDto.setDistinctSensiValueCnt(riskInfoDto.getRspDataDistinctCnt());

        // 操作时间
        riskInfoDto.setOperateTime(riskInfoDto.getUpdateTime());

        // --------------------------------------
        riskInfoDto.setEventCnt(riskInfoDto.getAttackCount());
        riskInfoDto.setDataDistinctCnt(riskInfoDto.getRspDataDistinctCnt());
    }

    @EventListener(AppNameChangeEvent.class)
    public void appNameChangeEvent(AppNameChangeEvent appNameChangeEvent) {
        String appUri = appNameChangeEvent.getUri();
        String appName = appNameChangeEvent.getName();
        Criteria criteria = new Criteria();
        criteria.and("appUri").is(appUri);
        Update update = new Update();
        update.set("appName", appName);
        riskInfoDaoImpl.updateMulti(criteria, update);
    }

    public List<RiskInfo> getByDataId(String dataId) {
        return riskInfoDaoImpl.getByDataId(dataId);
    }

}
