package com.quanzhi.auditapiv2.biz.risk.dto.search;

import com.quanzhi.auditapiv2.core.risk.dto.common.CommonSearchDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * create at 2021/8/14 2:27 下午
 * @description: 威胁ip查询条件
 **/
@Data
@ApiModel
public class ThreatIpSearchDto extends CommonSearchDto {

    @ApiModelProperty(value = "威胁IP", name = "ip")
    private String ip;

    @ApiModelProperty(value = "IP标签", name = "ipLabel")
    private String ipLabel;

    @ApiModelProperty(value = "国家",name = "country")
    private String country;

    @ApiModelProperty(value = "网段", name = "networkSegment")
    private List<String> networkSegment;

    @ApiModelProperty(value = "威胁状态", name = "threatIpState")
    private String threatState;

    @ApiModelProperty(value = "威胁标签", name = "threatLabel")
    private List<String> threatLabels;

    @ApiModelProperty(value = "首次发现时间-start", name = "firstTimeStart")
    private Long firstTimeStart;

    @ApiModelProperty(value = "首次发现时间-end", name = "firstTimeEnd")
    private Long firstTimeEnd;

    @ApiModelProperty(value = "最近发现时间-start", name = "lastTimeStart")
    private Long lastTimeStart;

    @ApiModelProperty(value = "最近发现时间-end", name = "lastTimeEnd")
    private Long lastTimeEnd;

    @ApiModelProperty(value = "appUri集合", name = "appUriSet")
    private Set<String> appUriSet;

    @ApiModelProperty(value = "部门集合", name = "departmentSet")
    private Set<String> departmentSet;

    @ApiModelProperty(value = "是否执行资产权限控制", name = "isAssetAuthorization")
    private Boolean isAssetAuthorization;

    @ApiModelProperty(value = "关联账号", name = "accounts")
    private List<String> accounts;

}