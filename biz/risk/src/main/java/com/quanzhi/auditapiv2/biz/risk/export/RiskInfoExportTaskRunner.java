package com.quanzhi.auditapiv2.biz.risk.export;

import com.quanzhi.auditapiv2.biz.risk.dto.export.RiskInfoExportDto;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.common.dal.entity.ExportTaskModel;
import com.quanzhi.auditapiv2.common.util.constant.ConfigContants;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;
import com.quanzhi.auditapiv2.core.service.manager.export.AbstractExportTaskRunner;
import com.quanzhi.auditapiv2.core.service.manager.export.TaskRunnerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * create at 2021/8/18 2:40 下午
 * @description: 风险清单导出任务
 **/
@Slf4j
@Service
public class RiskInfoExportTaskRunner extends AbstractExportTaskRunner<RiskInfoExportDto> {

    @Autowired
    private RiskInfoService riskInfoService;


    public RiskInfoExportTaskRunner() {
        super(ExportTaskType.EXPORT_RISK_INFO);
    }

    @Override
    public void runTask(ExportTaskModel taskModel, RiskInfoExportDto riskInfoExportDto, String workDir) throws TaskRunnerException {
        
        try {
            riskInfoService.exportRiskInfo(
                    riskInfoExportDto,
                    workDir+"/" + ConfigContants.exportPathMap.get(ExportTaskType.EXPORT_RISK_INFO.name()));
        } catch (Exception e) {
            throw new TaskRunnerException(e);
        }
    }

    @Override
    public Class<RiskInfoExportDto> getParamsClass() {
        return RiskInfoExportDto.class ;
    }



}