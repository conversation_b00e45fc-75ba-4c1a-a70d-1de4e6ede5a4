package com.quanzhi.auditapiv2.biz.risk.service;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskDistributedDto;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskThreatIpInfoDto;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskTrendDto;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskTypeDistributedDto;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskDescribeDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskIpJoinAccountDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.DefinedEventSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskTrend;
import com.quanzhi.auditapiv2.core.risk.entity.RiskType;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatIp;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskInfoDao;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskThreatPortraitDao;
import com.quanzhi.auditapiv2.core.risk.repository.RiskSampleRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/10 09:42
 * @Description:
 */
@Service
public class RiskThreatPortraitService {

    private Logger logger = LoggerFactory.getLogger(RiskThreatPortraitService.class);

    @Autowired
    private IRiskThreatPortraitDao riskThreatPortraitDaoImpl;

    @Autowired
    private DefinedEventService definedEventService;

    /**
     * 根据ip获取威胁ip信息
     *
     * @param ip 威胁IP
     * @return
     */
    public RiskThreatIpInfoDto getRiskThreatIpInfo(String ip) {
        RiskThreatIpInfoDto riskThreatIpInfoDto = new RiskThreatIpInfoDto();
        ThreatIp threatIp = riskThreatPortraitDaoImpl.selectThreatIp(ip);
        if (DataUtil.isNotEmpty(threatIp)) {
            riskThreatIpInfoDto = RiskThreatIpInfoDto.RiskThreatIpInfoDtoMapper.INSTANCE.convert(threatIp);
        }
        return riskThreatIpInfoDto;
    }

    /**
     * 根据ip获取该ip关联所有风险的占比情况
     *
     * @param ip 威胁IP
     * @return
     */
    public RiskTypeDistributedDto getRiskTypeDistributed(String ip) {
        List<RiskType> typeInfos = new ArrayList<>();
        long totalCount = 0;
        try {
            //风险类型总数
            totalCount = riskThreatPortraitDaoImpl.countRiskInfoByIp(ip);
            //填充数据
            typeInfos = riskThreatPortraitDaoImpl.selectRiskTypeDistributed(ip);
        } catch (Exception e) {
            logger.error("获取威胁ip关联所有风险的占比出错:{}", e);
        }
        return RiskTypeDistributedDto.builder().typeDistributed(typeInfos).totalCount(totalCount).build();
    }

    /**
     * 根据ip获取ip近一月访问量分布
     *
     * @param ip 威胁IP
     * @return
     */
    public List<RiskTrendDto> getRiskTrendByIp(String ip) {
        List<RiskTrendDto> result = new ArrayList<>();
        try {
            long zero = com.quanzhi.auditapiv2.common.util.utils.DateUtil.getTodayZeroTimeStamp();
            long endTime = zero + 24 * 60 * 60 * 1000 - 1;
            Calendar cal = Calendar.getInstance();
            //根据当前时间获取开始时间
            cal.setTime(DateUtil.date(zero));
            //获取当前时间前一月时间作为结束时间
            cal.add(Calendar.MONTH, -1);
            //获取结束时间戳
            long startTime = cal.getTimeInMillis();
            List<RiskTrend> riskTrends = riskThreatPortraitDaoImpl.selectRiskTrend(ip, startTime, endTime);
            //按日期分组
            Map<String, Integer> groupByDate = new HashMap<>();
            for (RiskTrend riskTrend : riskTrends) {
                String date = DateUtil.date(riskTrend.getTime()).toString("yyyyMMdd");
                if (groupByDate.containsKey(date)) {
                    Integer count = groupByDate.get(date);
                    groupByDate.put(date, riskTrend.getCount() + count);
                } else {
                    groupByDate.put(date, riskTrend.getCount());
                }
            }
            DateTime start = DateUtil.date(startTime);
            DateTime end = DateUtil.date(endTime);
            List<DateTime> dateTimes = DateUtil.rangeToList(start, end, DateField.DAY_OF_WEEK);
            //生成一月数据的数组
            for (DateTime dateTime : dateTimes) {
                RiskTrendDto riskTrendDto = RiskTrendDto.builder().date(DateUtil.date(dateTime).toString("yyyyMMdd"))
                        .views(0).build();
                //存在对应日期的实际数据，进行数据填充
                if (groupByDate.containsKey(riskTrendDto.getDate())) {
                    riskTrendDto.setViews(groupByDate.get(riskTrendDto.getDate()));
                }
                result.add(riskTrendDto);
            }
        } catch (Exception e) {
            logger.error("获取威胁ip近一月访问量分布出错:{}", e);
        }
        return result;
    }

    /**
     * 统计全年ip关联风险等级分布情况
     *
     * @param ip 威胁IP
     * @return
     */
    public List<List<RiskDistributedDto>> getRiskDistributed(String ip, String account, String year) {
        List<List<RiskDistributedDto>> years = new ArrayList<>();
        try {
            String startDate = year + "0101";
            String endDate = year + "1231";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            long startTime = simpleDateFormat.parse(startDate).getTime();
            long endTime = simpleDateFormat.parse(endDate).getTime();
            //查询数据
            List<AggRiskInfo> riskInfos = riskThreatPortraitDaoImpl.selectRiskInfo(ip, account, startTime, endTime, true);
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR, Integer.parseInt(year));
            //按发现时间分组
            Map<Long, List<AggRiskInfo>> collect = riskInfos.stream().collect(Collectors.groupingBy(AggRiskInfo::getFirstTime));
            Map<String, List<AggRiskInfo>> mapForDate = new HashMap<>();
            //将时间转换年份
            for (Long time : collect.keySet()) {
                String date = DateUtil.date(time).toString("yyyyMMdd");
                if (mapForDate.containsKey(date)) {
                    List<AggRiskInfo> infos = mapForDate.get(date);
                    infos.addAll(collect.get(time));
                    mapForDate.put(date, infos);
                } else {
                    mapForDate.put(date, collect.get(time));
                }
            }
            //生成一年数据
            for (int i = 0; i < 12; i++) {
                //初始化时间
                cal.set(Calendar.MONTH, i);
                //设置结束时间为当前月份最后一天
                cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
                Date end = cal.getTime();
                //设置开始时间为当前月份第一天
                cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
                Date start = cal.getTime();
                List<DateTime> dateTimes = DateUtil.rangeToList(start, end, DateField.DAY_OF_WEEK);
                List<RiskDistributedDto> months = new ArrayList<>();
                for (DateTime dateTime : dateTimes) {
                    RiskDistributedDto riskDistributedDto = RiskDistributedDto.builder()
                            .date(DateUtil.date(dateTime).toString("yyyyMMdd")).total(0).high(0L).mid(0L).low(0L).build();
                    if (mapForDate.containsKey(riskDistributedDto.getDate())) {
                        //统计高、中、低风险数量
                        riskDistributedDto.setTotal(mapForDate.get(riskDistributedDto.getDate()).size());
                        long highNum = mapForDate.get(riskDistributedDto.getDate()).stream().filter(e -> e.getLevel() == RiskInfo.RiskLevelEnum.HIGH.getLevel()).count();
                        long midNum = mapForDate.get(riskDistributedDto.getDate()).stream().filter(e -> e.getLevel() == RiskInfo.RiskLevelEnum.MEDIUM.getLevel()).count();
                        long lowNum = mapForDate.get(riskDistributedDto.getDate()).stream().filter(e -> e.getLevel() == RiskInfo.RiskLevelEnum.LOW.getLevel()).count();
                        //填充数据
                        riskDistributedDto.setHigh(highNum);
                        riskDistributedDto.setMid(midNum);
                        riskDistributedDto.setLow(lowNum);
                    }
                    months.add(riskDistributedDto);
                }
                years.add(months);
            }
        } catch (Exception e) {
            logger.error("获取威胁ip全年风险等级分布出错:{}", e);
        }
        return years;
    }

    /**
     * 获取威胁IP当日触发风险事件缩略
     *
     * @param ip   威胁IP
     * @param date 日期（********）
     * @return
     */
    public List<AggRiskInfo> getRiskEvent(String ip, String account, String date) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        long startTime = simpleDateFormat.parse(date).getTime();
        long endTime = startTime + 24 * 60 * 60 * 1000 - 1;
        //查询数据
        List<AggRiskInfo> riskInfos = riskThreatPortraitDaoImpl.selectRiskInfo(ip, account, startTime, endTime, true);
        return riskInfos;
    }

    /**
     * IP关联账号列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public ListOutputDto<RiskIpJoinAccountDto> getIpJoinAccountList(DefinedEventSearchDto definedEventSearchDto) throws Exception {

        return definedEventService.getIpJoinAccountList(definedEventSearchDto);
    }

}
