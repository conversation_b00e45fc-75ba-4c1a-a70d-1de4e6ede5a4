package com.quanzhi.auditapiv2.biz.risk.service;

import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.risk.Expression;
import com.quanzhi.audit_core.common.risk.Policy;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskDescTemplate;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskDescribeDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskInfoDao;
import com.quanzhi.auditapiv2.core.risk.service.bridge.IRiskBridgeService;
import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventDto;
import com.quanzhi.dsl.syntax.VariableType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2022/4/1  上午
 * @description:
 **/
@Service
@Slf4j
public class RiskBridgeServiceImpl implements IRiskBridgeService {


    private final RiskInfoService riskInfoService;
    private final DefinedEventService definedEventService;
    private final IRiskInfoDao riskInfoDaoImpl;

    @DynamicValue(dataId = "auditapiv2.risk.template.json", groupId = "auditapiv2", typeClz = RiskDescTemplate.class)
    private List<RiskDescTemplate> riskDescTemplateList;

    public RiskBridgeServiceImpl(RiskInfoService riskInfoService, DefinedEventService definedEventService, IRiskInfoDao riskInfoDaoImpl) {
        this.riskInfoService = riskInfoService;
        this.definedEventService = definedEventService;
        this.riskInfoDaoImpl = riskInfoDaoImpl;
    }


    @Override
    public RiskInfoDto getRiskInfoDto(RiskInfo riskInfo, Map<String, String> nodeMap) {
        try {
            return riskInfoService.getRiskInfoDto(riskInfo, nodeMap);
        } catch (Exception exception) {
            log.error("查询风险出错:", exception);
        }
        return null;
    }

    @Override
    public RiskInfoDto getRiskInfoDtoById(String id) {
        RiskSearchDto riskSearchDto = new RiskSearchDto();
        riskSearchDto.setId(id);
        riskSearchDto.setPage(1);
        riskSearchDto.setLimit(1);
        try {
            ListOutputDto<RiskInfoDto> riskInfoList = riskInfoService.getRiskInfoListSort(riskSearchDto);
            List<RiskInfoDto> rows = riskInfoList.getRows();
            return DataUtil.isNotEmpty(rows) ? rows.get(0) : null;
        } catch (Exception exception) {
            log.error("查询风险列表出错:{}", exception);
        }
        return null;
    }

    /**
     * @param id
     * <AUTHOR>
     * @description: 根据风险id获取一条样例
     * @date: 2022/4/1
     * @Return java.lang.Object
     */
    @Override
    public Object getRiskOneSampleByRiskId(String id) {
        String sampleId = definedEventService.findOneSampleId(id);
        if (sampleId != null) {
            SampleEventDto sampleEventDto = definedEventService.getRiskSampleById(sampleId, false);
            return sampleEventDto;
        }
        return null;
    }

    @Override
    public Object getRiskOneSampleBySampleId(String sampleId) {
        if (sampleId != null) {
            SampleEventDto sampleEventDto = definedEventService.getRiskSampleById(sampleId, false);
            return sampleEventDto;
        }
        return null;
    }


    @Override
    public String getRiskDesc(String id) throws Exception {
        RiskInfoDto riskInfoDto = riskInfoService.getRiskInfoById(id);
        //描述（version版本判断新旧风险数据）
        try {
            if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(riskInfoDto.getVersion())) {

                RiskDescribeDto riskDescribeDto = definedEventService.getDescribeData(riskInfoDto);
                riskInfoDto.setRiskDescribe(riskDescribeDto);
            } else {
                if (com.quanzhi.audit_core.common.utils.DataUtil.isEmpty(riskInfoDto.getRiskDescribe())) {

                    String sql = getSql(riskInfoDto.getId());
                    RiskDescribeDto riskDescribeDto = definedEventService.getDescribeData(riskInfoDto, sql);
                    riskInfoDto.setRiskDescribe(riskDescribeDto);
                }
            }
        } catch (Exception exception) {
            log.error("获取风险描述出错:{}", exception);
        }
        String riskDesc = "";
        if (riskInfoDto.getPolicySnapshot().getName() != null &&
                riskInfoDto.getRiskDescribe() != null &&
                riskInfoDto.getRiskDescribe().getDescribeArray() != null) {
            riskDesc = riskInfoService.getRiskDescByTemplate(riskInfoDto.getPolicySnapshot().getId(), riskInfoDto.getRiskDescribe().getDescribeArray(), riskInfoDto.getVersion());
        }
        if (com.quanzhi.audit_core.common.utils.DataUtil.isEmpty(riskDesc) || "".equals(riskDesc)) {
            if (riskInfoDto.getPolicySnapshot().getName() != null &&
                    riskInfoDto.getRiskDescribe() != null &&
                    riskInfoDto.getRiskDescribe().getDescribeArray() != null) {
                StringBuilder type = new StringBuilder();
                if (com.quanzhi.audit_core.common.utils.DataUtil.isEmpty(riskInfoDto.getPolicySnapshot().getType())) {
                    for (Policy.EntityRelation entityConfig : riskInfoDto.getPolicySnapshot().getEntityConfigs()) {
                        type.append(entityConfig.getType());
                    }
                } else {
                    type.append(riskInfoDto.getPolicySnapshot().getType());
                }
                boolean baseLine = false;
                boolean activeTime = false;
                if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot()) && com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot().getQuotaRule())) {
                    if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(riskInfoDto.getPolicySnapshot().getQuotaRule().getExprList())) {
                        for (Expression expression : riskInfoDto.getPolicySnapshot().getQuotaRule().getExprList()) {
                            if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(expression.getRight()) && com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(expression.getRight().getType()) && expression.getRight().getType().name().equals(VariableType.CUSTOM.name())) {
                                baseLine = true;
                            }
                            if (expression.getLeft().getName().equals("活跃时间范围")) {
                                activeTime = true;
                            }
                        }
                    }
                }
                if (baseLine) {
                    //基线描述特殊处理
                    Long baseline = riskInfoDto.getRiskDescribe().getBaseline();
                    Integer visitCnt = riskInfoDto.getRiskDescribe().getVisitCnt();
                    if (activeTime) {
                        if("IP".equals(riskInfoDto.getEntityType())) {
                            riskDesc = "IP <h>"+riskInfoDto.getIp()+"</h> 于 <h>"+riskInfoDto.getDate()+"</h> 在API <h>"+riskInfoDto.getApiUrl()+"</h> 上有 <h>" + riskInfoDto.getSampleCount() + "</h> 次访问偏离活跃时间范围。";
                        } else {
                            riskDesc = "账号 <h>"+riskInfoDto.getEntityValue()+"</h> 于 <h>"+riskInfoDto.getDate()+"</h> 在API <h>"+riskInfoDto.getApiUrl()+"</h> 上有 <h>" + riskInfoDto.getSampleCount() + "</h> 次访问偏离活跃时间范围。";
                        }
                    }else {
                        riskDesc = riskInfoService.getCustomRiskDescByTemplate(type.toString(), riskInfoDto.getRiskDescribe().getDescribeArray(), baseline, visitCnt);
                    }
                } else {
                    riskDesc = riskInfoService.getCustomRiskDescByTemplate(type.toString(), riskInfoDto.getRiskDescribe().getDescribeArray(), null, null);
                }
            }
        }
        return riskDesc;
    }

    public String getSql(String id) throws Exception {

        RiskInfo riskInfo = riskInfoDaoImpl.selectRiskInfoById(id);

        if (com.quanzhi.audit_core.common.utils.DataUtil.isNotEmpty(riskInfo)) {

            return riskInfo.createEventQuerySqlSegment();
        } else {
            return null;
        }
    }

}