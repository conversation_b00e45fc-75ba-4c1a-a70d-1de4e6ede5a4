package com.quanzhi.auditapiv2.biz.risk.service;

import cn.hutool.core.date.DateUtil;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEvent;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEventCleanLog;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.page.Page;
import com.quanzhi.auditapiv2.core.risk.entity.page.Pageable;
import com.quanzhi.auditapiv2.core.risk.entity.page.Sort;
import com.quanzhi.auditapiv2.core.risk.repository.DefinedEventCleanLogRepository;
import com.quanzhi.auditapiv2.core.risk.repository.DefinedEventRepository;
import com.quanzhi.auditapiv2.core.risk.repository.RiskRepository;
import com.quanzhi.auditapiv2.core.risk.service.AutoFlushBatch;
import com.quanzhi.auditapiv2.core.risk.service.defaults.ConcurrentAutoFlushBatch;
import com.quanzhi.metabase.common.utils.task.IndexedProcessor;
import com.quanzhi.metabase.common.utils.task.ProcessStat;
import com.quanzhi.metabase.common.utils.task.Processor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @Author: HaoJun
 * @Date: 2021/8/11 5:52 下午
 */
@Slf4j
@Service
public class DefinedEventCleanService {

    private final RiskRepository riskRepository;

    private final DefinedEventCleanLogRepository definedEventCleanLogRepository;

    private final DefinedEventRepository definedEventRepository;

    /**
     * 清理 cleanIntervalDays 天的数据；cleanIntervalDays必须大于等于cleanLastDays
     */
    private int cleanIntervalDays = 3;
    /**
     * 清理 cleanLastDays 天前的数据
     */
    private int cleanLastDays = 1;

    private SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd");

    private SimpleDateFormat SDF2 = new SimpleDateFormat("yyyyMMdd");
    /**
     * 保存最大10W条
     */
    private static final int DEFAULT_PAGE = 100000 / Processor.LIMIT;

    private final AutoFlushBatch<DefinedEvent> tempDefinedEventBatch
            = new ConcurrentAutoFlushBatch(new AutoFlushBatch.FlushExecutor<DefinedEvent>() {
        @Override
        public void doFlush(List<DefinedEvent> list) {
            definedEventRepository.insertBatch(list);
        }
    });

    public DefinedEventCleanService(RiskRepository riskRepository,
                                    DefinedEventCleanLogRepository definedEventCleanLogRepository,
                                    DefinedEventRepository definedEventRepository) {
        this.riskRepository = riskRepository;
        this.definedEventCleanLogRepository = definedEventCleanLogRepository;
        this.definedEventRepository = definedEventRepository;
    }

    public void clear() {
        long startTimestamp = System.currentTimeMillis();
        DefinedEventCleanLog definedLog = definedEventCleanLogRepository.lastOne();
        // 如果是第一次清理数据
        if (definedLog == null) {
            definedLog = new DefinedEventCleanLog();
            // 清理cleanIntervalDays天前到昨天的数据
            definedLog.setStartDate(SDF.format(DateUtils.addDays(new Date(), -cleanIntervalDays)));
            definedLog.setEndDate(SDF.format(DateUtils.addDays(new Date(), -cleanLastDays)));
        } else if (definedLog.getState() == DefinedEventCleanLog.CleanState.SUCCESS) {
            // 如果上一次清理数据成功了，准备下一次清理的数据
            // 清理上一次数据前一天
            DefinedEventCleanLog newLog = new DefinedEventCleanLog();
            Date newStartDate = DateUtils.addDays(DateUtil.parse(definedLog.getEndDate()), -cleanIntervalDays + 1);
            Date newEndDate = DateUtils.addDays(new Date(), -cleanLastDays);
            if (newStartDate.compareTo(newEndDate) > 0) {
                log.warn("startDate is bigger than endDate, exit.");
                return;
            }
            newLog.setStartDate(SDF.format(newStartDate));
            newLog.setEndDate(SDF.format(newEndDate));
            definedLog = newLog;
        } else {
            // 如果上一次清理失败了，标记本次清理结束时间为昨天
            definedLog.setEndDate(SDF.format(DateUtils.addDays(new Date(), -cleanLastDays)));
        }
        definedLog.setState(DefinedEventCleanLog.CleanState.RUNNING);
        definedEventCleanLogRepository.save(definedLog);
        // 获取两个日期之间的所有日期
        List<String> dates = getDays(DateUtil.parse(definedLog.getStartDate()),
                DateUtil.parse(definedLog.getEndDate()));
        int errorCount = 0;
        for (String date : dates) {
            try {
                clear(date);
            } catch (Exception e) {
                errorCount++;
                log.error("process risk error", e);
                definedLog.setMsg(e.getMessage());
            }
        }
        if (errorCount > dates.size() / 2) {
            definedLog.setState(DefinedEventCleanLog.CleanState.FAIL);
        }
        definedLog.setDuration((System.currentTimeMillis() - startTimestamp) / 1000);
        if (definedLog.getState() != DefinedEventCleanLog.CleanState.FAIL) {
            definedLog.setState(DefinedEventCleanLog.CleanState.SUCCESS);
        }
        definedEventCleanLogRepository.save(definedLog);
    }

    public void clear(String date) {
        processRisk(date);
        definedEventRepository.delete(date);
    }

    private void processRisk(final String date) {

        IndexedProcessor indexedProcessor = new IndexedProcessor<RiskInfo>() {
            @Override
            public void process(RiskInfo item) {
                processOneRisk(item, date);
            }

            @Override
            protected List<RiskInfo> fetch(String index, int limit) {
                Pageable pageable = new Pageable();
                pageable.setSize(limit);
                pageable.setPage(index == null ? 0 : Integer.parseInt(index));
                pageable.setSort(Sort.asc("date"));
                Page<RiskInfo> riskInfoPage = riskRepository.findByDate(formatDate(date)
                        , pageable);
                return riskInfoPage == null ? Collections.emptyList() : riskInfoPage.getRows();
            }

            @Override
            protected String getIndex(List<RiskInfo> items) {
                return index == null ? "1" : String.valueOf(Integer.valueOf(index) + 1);
            }
        };
        indexedProcessor.process();
        tempDefinedEventBatch.flush();
        ProcessStat stat = indexedProcessor.getStat();
        // 当处理的失败数量过多时，不删除对应的数据以保留现场
        if (stat.getFailCount() > 100
                || stat.getFailCount() > stat.getSuccessCount()) {
            throw new IllegalStateException("process risk date:"
                    + date + " error, too much fail. success:"
                    + stat.getSuccessCount()
                    + " fail:"
                    + stat.getFailCount());
        }
    }

    private void processOneRisk(RiskInfo riskInfo, String date) {
        // 已确认的风险，保留此IP的所有登录事件
        if (RiskInfo.RiskStateEnum.HAS_HANDLE.getState().equals(riskInfo.getState())){
            String ip = null;
            if (riskInfo.getEntities() != null && riskInfo.getEntities().size() > 0){
                for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : riskInfo.getEntities()){
                    if ("IP".equals(entity.getType())){
                        ip = entity.getValue();
                    }
                }
            }
            if (ip != null) {
                definedEventRepository.updateBatch("keepFlag=1",
                        "date='" + date + "'" + " AND " + "ip='" + ip + "'" + " AND has(eventDefineIds, 'login_event') ");
            }
        }
        definedEventRepository.updateBatch("keepFlag=1",
                riskInfo.createEventQuerySqlSegment() + " AND " + "date='" + date + "' ");
//        new IndexedProcessor<DefinedEvent>() {
//            @Override
//            public void process(DefinedEvent item) {
//                if (Boolean.TRUE.equals(item.getKeepFlag())) {
//                    return;
//                }
//                item.setKeepFlag(true);
//                tempDefinedEventBatch.add(item);
//            }
//
//            @Override
//            protected List<DefinedEvent> fetch(String index, int limit) {
//                int page = index == null ? 1 : Integer.parseInt(index);
//                if (page > DEFAULT_PAGE) {
//                    return Collections.emptyList();
//                }
//                Pageable pageable = new Pageable();
//                pageable.setSize(limit);
//                pageable.setPage(page);
//                pageable.setSort(Sort.asc("date"));
//                Page<DefinedEvent> definedEventPage = definedEventRepository
//                        .findByDate(riskInfo.getEventQuerySqlSegment(),
//                                date,
//                                pageable);
//                return definedEventPage == null ? Collections.emptyList() : definedEventPage.getRows();
//            }
//
//            @Override
//            protected String getIndex(List<DefinedEvent> items) {
//                return index == null ? "2" : String.valueOf(Integer.valueOf(index) + 1);
//            }
//        }.process();
    }

    // 返回的日期集合
    private List<String> getDays(Date start, Date end) {
        List<String> days = new ArrayList<>();
        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(start);
        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(end);
        tempEnd.add(Calendar.DATE, 1);
        while (tempStart.before(tempEnd)) {
            days.add(SDF.format(tempStart.getTime()));
            tempStart.add(Calendar.DAY_OF_YEAR, 1);
        }
        return days;
    }

    private String formatDate(String date) {
        if (!date.contains("-")) {
            return date;
        }
        return SDF2.format(DateUtil.parse(date, SDF));
    }
}
