package com.quanzhi.auditapiv2.biz.risk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/9/18 5:23 下午
 */
@Builder
@ApiModel("风险策略")
@Data
public class SimpleRiskPolicyDto {
    @ApiModelProperty("风险ID")
    private String id;
    @ApiModelProperty("风险名称")
    private String name;
    @ApiModelProperty("风险标签id")
    private String threatLabel;
}
