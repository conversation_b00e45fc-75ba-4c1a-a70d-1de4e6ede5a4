package com.quanzhi.auditapiv2.biz.risk.export;

import com.quanzhi.auditapiv2.biz.risk.dto.export.AggRiskExportDto;
import com.quanzhi.auditapiv2.biz.risk.dto.export.RiskV2ExportDto;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.AggRiskService;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.RiskV2Service;
import com.quanzhi.auditapiv2.common.dal.entity.ExportTaskModel;
import com.quanzhi.auditapiv2.common.util.constant.ConfigContants;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;
import com.quanzhi.auditapiv2.core.service.manager.export.AbstractExportTaskRunner;
import com.quanzhi.auditapiv2.core.service.manager.export.TaskRunnerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RiskV2ExportTaskRunner extends AbstractExportTaskRunner<RiskV2ExportDto> {

    @Autowired
    private RiskV2Service riskV2Service;

    public RiskV2ExportTaskRunner() {
        super(ExportTaskType.EXPORT_RISKV2_INFO);
    }

    @Override
    public void runTask(ExportTaskModel taskModel, RiskV2ExportDto riskV2ExportDto, String workDir) throws TaskRunnerException {

        try {
            riskV2Service.exportRiskInfo(
                    riskV2ExportDto,
                    workDir + "/" + ConfigContants.exportPathMap.get(ExportTaskType.EXPORT_RISKV2_INFO.name()));
        } catch (Exception e) {
            throw new TaskRunnerException(e);
        }
    }

    @Override
    public Class<RiskV2ExportDto> getParamsClass() {
        return RiskV2ExportDto.class;
    }


}