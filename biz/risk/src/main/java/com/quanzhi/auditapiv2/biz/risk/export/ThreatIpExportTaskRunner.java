package com.quanzhi.auditapiv2.biz.risk.export;

import com.quanzhi.auditapiv2.biz.risk.dto.export.ThreatIpExportDto;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatIpService;
import com.quanzhi.auditapiv2.common.dal.entity.ExportTaskModel;
import com.quanzhi.auditapiv2.common.util.constant.ConfigContants;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;
import com.quanzhi.auditapiv2.core.service.manager.export.AbstractExportTaskRunner;
import com.quanzhi.auditapiv2.core.service.manager.export.TaskRunnerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * create at 2021/8/18 10:28 上午
 * @description: 威胁ip
 **/
@Service
@Slf4j
public class ThreatIpExportTaskRunner extends AbstractExportTaskRunner<ThreatIpExportDto>{

    @Autowired
    private ThreatIpService threatIpService;

    public ThreatIpExportTaskRunner() {
        super(ExportTaskType.EXPORT_THREAT_IP);
    }

    @Override
    public void runTask(ExportTaskModel taskModel, ThreatIpExportDto threatIpExportDto, String workDir) throws TaskRunnerException {
        try {
            threatIpService.exportThreatIp(threatIpExportDto,workDir, ConfigContants.exportPathMap.get(ExportTaskType.EXPORT_THREAT_IP.name()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Class<ThreatIpExportDto> getParamsClass() {
        return ThreatIpExportDto.class;
    }
}