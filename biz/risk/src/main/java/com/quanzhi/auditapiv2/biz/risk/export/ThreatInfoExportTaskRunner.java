package com.quanzhi.auditapiv2.biz.risk.export;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.biz.risk.dto.export.ThreatInfoExportDto;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatInfoService;
import com.quanzhi.auditapiv2.common.dal.entity.ExportTaskModel;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.util.constant.ConfigContants;
import com.quanzhi.auditapiv2.common.util.entity.ExportTaskType;
import com.quanzhi.auditapiv2.core.service.manager.export.AbstractExportTaskRunner;
import com.quanzhi.auditapiv2.core.service.manager.export.TaskRunnerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * create at 2023/03/01
 * @description: 威胁信息
 **/
@Service
@Slf4j
public class ThreatInfoExportTaskRunner extends AbstractExportTaskRunner<ThreatInfoExportDto> {

    private final ThreatInfoService threatInfoService;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    public ThreatInfoExportTaskRunner(ThreatInfoService threatInfoService) {
        super(ExportTaskType.EXPORT_THREAT_INFO);
        this.threatInfoService = threatInfoService;
    }

    @Override
    public void runTask(ExportTaskModel taskModel, ThreatInfoExportDto threatInfoExportDto, String workDir) throws TaskRunnerException {
        try {
            if (productType.equals(ProductTypeEnum.wph.name())) {
                JSONObject jsonObject = JSONObject.parseObject(taskModel.getSerializedParams());
                threatInfoService.exportThreatInfo(threatInfoExportDto, workDir,
                        ConfigContants.exportPathMap.get(ExportTaskType.EXPORT_THREAT_INFO.name()),
                        jsonObject.getString("applicant_id"),
                        jsonObject.getString("tags"),
                        jsonObject.getString("reasons"),
                        jsonObject.getString("usage")
                );
            }else {
                threatInfoService.exportThreatInfo(threatInfoExportDto, workDir, ConfigContants.exportPathMap.get(ExportTaskType.EXPORT_THREAT_INFO.name()));
            }
        }catch (Exception e) {

        }
    }

    @Override
    public Class<ThreatInfoExportDto> getParamsClass() {
        return ThreatInfoExportDto.class;
    }

}