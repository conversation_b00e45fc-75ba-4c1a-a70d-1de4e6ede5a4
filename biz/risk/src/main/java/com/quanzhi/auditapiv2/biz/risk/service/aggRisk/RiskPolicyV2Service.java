package com.quanzhi.auditapiv2.biz.risk.service.aggRisk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.auditapiv2.common.dal.common.dao.ViewMenuDao;
import com.quanzhi.auditapiv2.common.dal.dto.common.CascadeDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.model.LicenseConfigDto;
import com.quanzhi.auditapiv2.core.risk.entity.Entity;
import com.quanzhi.auditapiv2.core.risk.entity.RiskGroup;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicySearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicyV2;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskGroupDao;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskPolicyV2Dao;
import com.quanzhi.auditapiv2.core.service.facade.impl.LicenseFacadeImpl;
import com.quanzhi.auditapiv2.core.trace.util.StringUtils;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.domain.entity.po.RuleCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 《异常规则业务层》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used: <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Service
@Slf4j
public class RiskPolicyV2Service {

    private final IRiskPolicyV2Dao riskPolicyDaoImpl;

    private final IRiskGroupDao riskGroupDao;

    private final ViewMenuDao viewMenuDao;

    @NacosInjected
    private ConfigService configService;

    private final String RISK_POLICY_ENTITY_DATA_ID = "entity.relation.json";

    private final String EVENT_DEFINE = "handler.eventDefine.json";

    private final String RISK_GROUP_ID = "risk";

    private final String HANDLER_GROUP_ID = "handler";

    private static final long timeoutMs = 5000;

    private final LicenseFacadeImpl licenseFacade;

    @NacosValue(value = "${risk.entity.sort:[\"IP\",\"ACCOUNT\",\"DATA\",\"APP\",\"API\",\"SESSION\"]]}", autoRefreshed = true)
    private List<String> riskEntitySort;

    public RiskPolicyV2Service(
            IRiskPolicyV2Dao riskPolicyDaoImpl,
            IRiskGroupDao riskGroupDao,
            ViewMenuDao viewMenuDao, LicenseFacadeImpl licenseFacade) {
        this.riskPolicyDaoImpl = riskPolicyDaoImpl;
        this.riskGroupDao = riskGroupDao;
        this.viewMenuDao = viewMenuDao;
        this.licenseFacade = licenseFacade;
    }

    public ListOutputDto<RiskPolicyV2> getRiskPolicyV2List(RiskPolicySearchDto riskPolicySearchDto) throws Exception {
        ListOutputDto<RiskPolicyV2> datas = new ListOutputDto<>();
        // 获取授权信息
        LicenseConfigDto licenseConfigDto = licenseFacade.licenseDetail();
        String productName = licenseConfigDto.getProductName();
        if (!productName.contains("旗舰")) {
            riskPolicySearchDto.setUnType(Arrays.asList("account_apiUri", "account", "data"));
        }
        List<RiskPolicyV2> riskPolicies = riskPolicyDaoImpl.selectRiskPolices(riskPolicySearchDto);
        // 计算type
        for (RiskPolicyV2 riskPolicy : riskPolicies) {
            riskPolicy.setType(getType(riskPolicy));
        }
        Long totalCount = riskPolicyDaoImpl.count(riskPolicySearchDto);
        datas.setRows(riskPolicies);
        datas.setTotalCount(totalCount);
        return datas;
    }

    public String getType(RiskPolicyV2 riskPolicy) {
        if (riskPolicy.getEntities() == null
                || (riskPolicy.getEntities().size() == 1 && riskPolicy.getEntities().get(0).getType().equals("DATA"))) {
            return riskPolicy.getType();
        }
        List<String> values = new ArrayList<>();
        for (Entity entity : riskPolicy.getEntities()) {
            values.add(entity.getValue());
        }
        values.sort(String::compareTo);
        StringBuilder sb = new StringBuilder();
        for (String value : values) {
            sb.append(value + "_");
        }
        String type = sb.toString();
        if (type.endsWith("_")) {
            type = type.substring(0, type.length() - 1);
        }
        return type;
    }

    public String updateRiskPolicyV2State(String policyId, Boolean enable) {
        riskPolicyDaoImpl.updateRiskPolicyState(policyId, enable);
        return "修改成功！";
    }

    public String deleteRiskPolicyV2(String policyId) {
        riskPolicyDaoImpl.deleteRiskPolicy(policyId);
        return "删除成功！";
    }

    public List<CascadeDto> getCascades() {
        List<RiskPolicyV2> list = riskPolicyDaoImpl.getAll();
        if (list == null) {
            return Collections.emptyList();
        }
        Map<String, List<RiskPolicyV2>> riskPolicyMap = list.stream()
                .filter(riskPolicy -> riskPolicy.getDelFlag() == null || !riskPolicy.getDelFlag())
                .collect(Collectors.groupingBy(RiskPolicyV2::getGroup));
        List<CascadeDto> cascadeDtos = new ArrayList<>();
        for (Map.Entry<String, List<RiskPolicyV2>> entry : riskPolicyMap.entrySet()) {
            CascadeDto cascadeDto = new CascadeDto();
            cascadeDto.setLabel(entry.getKey());
            cascadeDto.setValue(entry.getValue().get(0).getGroup());
            cascadeDto.setChildren(new ArrayList<>());
            for (RiskPolicyV2 policy : entry.getValue()) {
                CascadeDto child = new CascadeDto();
                child.setLabel(policy.getName());
                child.setValue(policy.getId());
                cascadeDto.getChildren().add(child);
            }
            cascadeDtos.add(cascadeDto);
        }
        return cascadeDtos;
    }

    public List<RiskGroup> getAllGroups() {
        List<RiskGroup> riskGroups = riskGroupDao.getAll();
        return riskGroups;
    }

    public String addRiskGroup(RiskGroup group) throws Exception {
        riskGroupDao.save(group);
        viewMenuDao.initViewMenuState(Collections.singletonList("riskViewMenu"));
        return "添加成功！";
    }

    public Boolean checkRiskGroup(RiskGroup group) {
        long count = riskGroupDao.getCount(Criteria.where("groupName").is(group.getGroupName()));
        return count > 0;
    }

    public String deleteRiskGroup(RiskGroup group) {
        riskGroupDao.delete("riskGroup", Criteria.where("_id").is(group.getId()));
        return "删除成功！";
    }

    public List<String> getRiskGroups() {
        List<RiskPolicyV2> list = riskPolicyDaoImpl.selectRiskPolices(null);
        if (list == null) {
            return Collections.emptyList();
        }
        List<String> groups = list.stream().map(riskPolicy -> riskPolicy.getGroup()).collect(Collectors.toList());
        return groups;
    }

    public List<CommonGroupDto> groupRiskPolices(RiskPolicySearchDto riskPolicySearchDto) throws Exception {
        Map<String, String> riskEntityTypeMap = getRiskEntityTypeMap();
        List<CommonGroupDto> commonGroupDtos = riskPolicyDaoImpl.groupRiskPolices(riskPolicySearchDto);
        if ("level".equals(riskPolicySearchDto.getGroupField())) {
            for (CommonGroupDto commonGroupDto : commonGroupDtos) {
                if ("1.0".equals(commonGroupDto.getId()) || "1".equals(commonGroupDto.getId())) {
                    commonGroupDto.setName("低等级");
                } else if ("2.0".equals(commonGroupDto.getId()) || "2".equals(commonGroupDto.getId())) {
                    commonGroupDto.setName("中等级");
                } else {
                    commonGroupDto.setName("高等级");
                }
            }
        }
        if ("type".equals(riskPolicySearchDto.getGroupField())) {
            for (CommonGroupDto commonGroupDto : commonGroupDtos) {
                if (riskEntityTypeMap.containsKey(commonGroupDto.getId())) {
                    commonGroupDto.setName(riskEntityTypeMap.get(commonGroupDto.getId()));
                }
            }
        }
        // 排序，保证结果顺序一致
        Map<Long, List<CommonGroupDto>> collect = commonGroupDtos.stream()
                .collect(Collectors.groupingBy(CommonGroupDto::getCount));
        Set<Long> sortKey = new TreeSet<>(Long::compareTo);
        sortKey.addAll(collect.keySet());
        List<CommonGroupDto> result = new ArrayList<>();
        for (Long key : sortKey) {
            if (collect.get(key).size() > 1) {
                collect.get(key).sort(Comparator.comparing(CommonGroupDto::getName));
                result.addAll(collect.get(key));
            } else {
                result.add(collect.get(key).get(0));
            }
        }
        Collections.reverse(result);
        return result;
    }

    public RiskPolicyV2 getRiskPolicyV2ById(String id) throws Exception {
        RiskPolicyV2 riskPolicy = riskPolicyDaoImpl.selectRiskPolicyById(id);
        riskPolicy.setType(getType(riskPolicy));
        return riskPolicy;
    }

    public String saveV2(RiskPolicyV2 riskPolicy) throws Exception {
        // 重名检查
        checkPolicy(riskPolicy);
        riskPolicy.setUpdateTime(System.currentTimeMillis());
        riskPolicy.setAllowListTypes(Arrays.asList("IP", "API", "ACCOUNT", "APP"));
        if (DataUtil.isNotEmpty(riskPolicy.getQuotaRule())) {
            MatchRule quotaRule = riskPolicy.getQuotaRule();
            MatchRule sampleRule = quotaRule.clone();
            for (RuleCondition ruleCondition : sampleRule.getRuleConditions()) {
                //EQ 不做处理
                if (ruleCondition.getOperator().equals("EQ")) {
                    continue;
                }
                if (DataUtil.isNotEmpty(ruleCondition.getValue())) {
                    if (ruleCondition.getValue() instanceof Integer) {
                        Integer value = (Integer) ruleCondition.getValue();
                        ruleCondition.setValue(value * 0.9);
                    }
                }
            }
            riskPolicy.getSampleConfig().setLimit(50);
            riskPolicy.getSampleConfig().setPhase("AFTER");
            riskPolicy.getSampleConfig().setProperties(sampleRule);
        }
        if (riskPolicy.getEntities() != null && riskPolicy.getEntities().size() == 1
                && riskPolicy.getEntities().get(0).getType().equals("DATA")) {
            riskPolicy.setType("data");
        }
        riskPolicyDaoImpl.upsertRiskPolicy(riskPolicy);
        return riskPolicy.getId();
    }

    private void checkPolicy(RiskPolicyV2 riskPolicy) throws Exception {
        if (StringUtils.isEmpty(riskPolicy.getName())) {
            throw new Exception("异常名称不可为空！");
        }

        if (riskPolicy.getName().length() > 40) {
            throw new Exception("异常名称长度不可大于40！");
        }

        List<RiskPolicyV2> policies = riskPolicyDaoImpl.listAllPolicies();
        if (DataUtil.isEmpty(policies)) {
            return;
        }
        for (RiskPolicyV2 policy : policies) {
            if (policy.getDelFlag()) {
                continue;
            }
            if (policy.getName().equals(riskPolicy.getName())) {
                if (riskPolicy.getId() == null) {
                    throw new Exception("异常名称不可重复");
                } else if (!policy.getId().equals(riskPolicy.getId())) {
                    throw new Exception("异常名称不可重复");
                }
            }
        }
    }

    public JSONObject getRiskEntityConfig() {

        try {

            // 获取授权信息
            LicenseConfigDto licenseConfigDto = licenseFacade.licenseDetail();
            String productName = licenseConfigDto.getProductName();

            String content = configService.getConfig(RISK_POLICY_ENTITY_DATA_ID, RISK_GROUP_ID, timeoutMs);

            JSONObject riskEntityConfig = JSON.parseObject(content);

            // entities除了旗舰版都排除账号、账号+API、数据
            if (riskEntityConfig.containsKey("entities") && !productName.contains("旗舰")) {
                List<JSONObject> entities = riskEntityConfig.getJSONArray("entities").toJavaList(JSONObject.class);
                List<JSONObject> entitiesResult = new ArrayList<>();
                for (JSONObject entity : entities) {
                    if (entity.getString("type").equals("ACCOUNT") || entity.getString("type").equals("DATA")) {
                        continue;
                    }
                    entitiesResult.add(entity);
                }
                riskEntityConfig.put("entities", entitiesResult);
            }

            // entityRelated除了旗舰版都排除账号、账号+API、数据
            if (riskEntityConfig.containsKey("entityRelated") && !productName.contains("旗舰")) {
                List<JSONObject> entityRelates = riskEntityConfig.getJSONArray("entityRelated")
                        .toJavaList(JSONObject.class);
                List<JSONObject> entityRelatesResult = new ArrayList<>();
                for (JSONObject entityRelate : entityRelates) {
                    List<JSONObject> relateds = entityRelate.getJSONArray("related").toJavaList(JSONObject.class);
                    StringBuilder sb = new StringBuilder();
                    for (JSONObject related : relateds) {
                        sb.append(related.getString("type"));
                    }
                    if (sb.toString().contains("ACCOUNT") || sb.toString().contains("DATA")) {
                        continue;
                    }
                    entityRelatesResult.add(entityRelate);
                }
                riskEntityConfig.put("entityRelated", entityRelatesResult);
            }

            if (riskEntityConfig.containsKey("entityRelated")) {
                List<JSONObject> entityRelates = riskEntityConfig.getJSONArray("entityRelated")
                        .toJavaList(JSONObject.class);
                for (JSONObject entityRelated : entityRelates) {
                    if (entityRelated.containsKey("related")) {
                        List<JSONObject> relates = entityRelated.getJSONArray("related").toJavaList(JSONObject.class);
                        List<String> types = new ArrayList<>();
                        List<String> values = new ArrayList<>();
                        for (JSONObject related : relates) {
                            types.add(related.getString("type"));
                            values.add(related.getString("value"));
                        }
                        types.sort(String::compareTo);
                        values.sort(String::compareTo);
                        StringBuilder sb = new StringBuilder();
                        for (String value : values) {
                            sb.append(value).append("_");
                        }
                        String type = sb.toString();
                        if (type.endsWith("_")) {
                            type = type.substring(0, type.length() - 1);
                        }
                        entityRelated.put("related", types);
                        entityRelated.put("type", type);
                    }
                }
            }

            return riskEntityConfig;

        } catch (Exception e) {

            log.error("getRiskEntityConfig error", e);

            return null;
        }

    }

    public Map<String, String> getRiskEntityTypeMap() {

        try {

            Map<String, String> map = new HashMap<>();

            String content = configService.getConfig(RISK_POLICY_ENTITY_DATA_ID, RISK_GROUP_ID, timeoutMs);

            JSONObject riskEntityConfig = JSON.parseObject(content);

            if (riskEntityConfig.containsKey("entityRelated")) {
                List<JSONObject> entityRelates = riskEntityConfig.getJSONArray("entityRelated")
                        .toJavaList(JSONObject.class);
                for (JSONObject entityRelated : entityRelates) {
                    if (entityRelated.containsKey("related")) {
                        List<JSONObject> relates = entityRelated.getJSONArray("related").toJavaList(JSONObject.class);
                        List<String> values = new ArrayList<>();
                        for (JSONObject related : relates) {
                            values.add(related.getString("value"));
                        }
                        values.sort(String::compareTo);
                        StringBuilder sb = new StringBuilder();
                        for (String value : values) {
                            sb.append(value).append("_");
                        }
                        String type = sb.toString();
                        if (type.endsWith("_")) {
                            type = type.substring(0, type.length() - 1);
                        }
                        map.put(type, entityRelated.getString("name"));
                    }
                }
            }

            return map;

        } catch (Exception e) {

            log.error("getRiskEntityConfig error", e);

            return null;
        }

    }

    public Map<String, String> getEventDefineEnum() {

        try {

            String content = configService.getConfig(EVENT_DEFINE, HANDLER_GROUP_ID, timeoutMs);

            List<JSONObject> eventDefines = JSON.parseArray(content, JSONObject.class);
            Map<String, String> eventDefineMap = new HashMap<>();
            for (JSONObject eventDefine : eventDefines) {
                if (eventDefine.containsKey("id") && eventDefine.containsKey("name")) {
                    eventDefineMap.put(eventDefine.getString("id"), eventDefine.getString("name"));
                }
            }

            return eventDefineMap;

        } catch (Exception e) {

            log.error("getEventDefineEnum error", e);

            return null;
        }

    }

    public List<RiskPolicyV2> getRiskPolicyV2ByIds(List<String> ids) {
        return riskPolicyDaoImpl.selectRiskPolicyByIds(ids);
    }

}
