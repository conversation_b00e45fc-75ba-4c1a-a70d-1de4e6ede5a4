package com.quanzhi.auditapiv2.biz.risk.service;

import com.alibaba.fastjson.JSONArray;
import com.clearspring.analytics.stream.cardinality.HyperLogLogPlus;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.entity.DataRevealLabelCount;
import com.quanzhi.auditapiv2.core.risk.entity.DataRevealLabelCountVo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.repository.IDataRevealLabelCountDao;
import com.quanzhi.auditapiv2.core.service.NacosDataServiceBuilder;
import com.quanzhi.auditapiv2.core.service.manager.web.IDataLabelService;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2021/9/27 3:07 下午
 * @description: 风险数据标签统计service
 **/
@Service
@Slf4j
public class RiskDataLabelCountService {

    //风险事件
    @Autowired
    private RiskInfoService riskInfoService;

    //数据标签service
    @Autowired
    private IDataLabelService dataLabelServiceImpl;

    //nacos缓存数据
    @Autowired
    private NacosDataServiceBuilder nacosDadaServiceBuilder;
    
    @Autowired
    private IDataRevealLabelCountDao dataRevealLabelCountDao;

    private static String dataRevealLabelCountTable = "dataRevealLabelCount_vo";

    /**
     * <AUTHOR>
     * @description: 获取所有统计过标签的风险事件
     * @date: 2021/9/27
     * @Return java.util.List<java.lang.String>
     */
    public List<String> getAllRiskIds() {
        MetabaseQuery query = new MetabaseQuery().where("delFlag", Predicate.IS, false).fields(Arrays.asList(new String[]{"riskId"}));
        List<DataRevealLabelCount> dataRevealLabelCounts = dataRevealLabelCountDao.find(query, null);
        return dataRevealLabelCounts.stream().map(e -> e.getRiskId()).collect(Collectors.toList());
    }

    /**
     * @param dataRevealLabelCounts
     * <AUTHOR>
     * @description: 新增数据标签统计
     * @date: 2021/9/27
     * @Return void
     */
    public void addDataRevealLabelCount(List<DataRevealLabelCount> dataRevealLabelCounts) {
        dataRevealLabelCountDao.batchSave(dataRevealLabelCounts);
    }


    /**
     * <AUTHOR>
     * @description: 全量标签统计
     * @date: 2021/9/27
     * @Return void
     */
    public void statisticsDataLabel() { //todo 待优化
        List<DataRevealLabelCountVo> insertList = new ArrayList<>();
        Map<String, HyperLogLogPlus> dataLabelMap = new HashMap<>();
        List<DataRevealLabelCount> dataRevealLabelCounts = dataRevealLabelCountDao.find(new MetabaseQuery().where("delFlag", Predicate.IS, false), null);
        for (DataRevealLabelCount dataRevealLabelCount : dataRevealLabelCounts) {
            List<DataRevealLabelCount.DataLabelCount> dataLabelCountList = dataRevealLabelCount.getDataLabelCountList();
            for (DataRevealLabelCount.DataLabelCount dataLabelCount : dataLabelCountList) {
                String label = dataLabelCount.getDataLabel();
                List<String> distinctDataLabelValList = dataLabelCount.getDistinctDataLabelValList();
                if(DataUtil.isNotEmpty(distinctDataLabelValList)){
                    if (dataLabelMap.containsKey(label)) {
                        HyperLogLogPlus hyperLogLogPlus = dataLabelMap.get(label);
                        distinctDataLabelValList.forEach(e -> hyperLogLogPlus.offer(e));
                    } else {
                        HyperLogLogPlus hyperLogLogPlus = new HyperLogLogPlus(11, 16);
                        distinctDataLabelValList.forEach(e -> hyperLogLogPlus.offer(e));
                        dataLabelMap.put(label, hyperLogLogPlus);
                    }
                }
            }
        }
        dataLabelMap.forEach((k, v) -> {
            insertList.add(DataRevealLabelCountVo.builder().dataLabel(k).dataLabelValueCnt(v.cardinality()).build());
        });
        //全量替换
        saveDataRevealLabelCountVo(insertList);
    }

    private void saveDataRevealLabelCountVo(List<DataRevealLabelCountVo> insertList) {
        try {
            dataRevealLabelCountDao.dropCollection(dataRevealLabelCountTable);
            dataRevealLabelCountDao.saveDataRevealLabelCountVo(insertList, dataRevealLabelCountTable);
        } catch (Exception exception) {
            log.error("saveDataRevealLabelCountVo err,{}", exception);
        }
    }


    /**
     * <AUTHOR>
     * @description: 数据泄漏统计
     * @date: 2021/9/27
     * @Return java.util.List<com.quanzhi.auditapiv2.common.dal.dto.common.CommonDto>
     */
    public List<CommonDto> revealDataAnalyze() {

        List<CommonDto> resultList = new ArrayList<>();
        Map<String, Long> map = new LinkedHashMap<String, Long>();
        
        //旧风险
        List<DataRevealLabelCountVo> list = dataRevealLabelCountDao.findDataRevealLabelCountVo(dataRevealLabelCountTable);
        list.forEach(e -> {

            if(DataUtil.isNotEmpty(map.get(e.getDataLabel()))) {

                Long count = map.get(e.getDataLabel());
                map.put(e.getDataLabel(), count + e.getDataLabelValueCnt());
            }else {
                map.put(e.getDataLabel(), e.getDataLabelValueCnt());
            }
        });
        
        //新风险
        List<RiskInfo> riskInfoList = riskInfoService.selectDataRevealRisk(false);
        for (RiskInfo riskInfo : riskInfoList) {
            
            if(DataUtil.isNotEmpty(riskInfo.getDescription())) {

                //数据标签
                JSONArray jsonArray = riskInfo.getDescription().getJSONArray("dataLabelList");

                if (DataUtil.isNotEmpty(jsonArray)) {

                    Map<String, String> dataLabeMap = getDataLabeMap();
                    for (Object obj : jsonArray) {

                        Map<String, Object> jsonMap = (Map<String, Object>) obj;

                        String name = dataLabeMap.get((String) jsonMap.get("labelId"));
                        
                        if(DataUtil.isNotEmpty(map.get(name))) {

                            Long count = map.get(name);
                            map.put(name, count + (Long) jsonMap.get("count"));
                        }else {
                            map.put(name, (Long) jsonMap.get("count"));
                        }
                    }
                }
            }
        }

        for (String key : map.keySet()) {

            CommonDto commonDto = new CommonDto(key, map.get(key));
            resultList.add(commonDto);
        }
        
        return resultList;
    }

    /**
     * 获取全部数据标签
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2020-04-08 9:56
     */
    private Map<String, String> getDataLabeMap() {
        //数据标签
        Map<String, String> dataLabeMap = new HashMap<String, String>();
        Map<String, DataLabel> dataLabelMap = nacosDadaServiceBuilder.build().getDataLabelMap();
        dataLabelMap.forEach((k, v) -> {
            dataLabeMap.put(k, v.getName());
        });
        return dataLabeMap;
    }


}