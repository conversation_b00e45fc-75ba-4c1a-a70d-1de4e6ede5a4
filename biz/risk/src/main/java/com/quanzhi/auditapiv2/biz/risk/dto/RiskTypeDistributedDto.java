package com.quanzhi.auditapiv2.biz.risk.dto;

import com.quanzhi.auditapiv2.core.risk.entity.RiskType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/9 14:14
 * @Description:
 */
@Data
@Builder
@ApiModel("风险类型分布")
public class RiskTypeDistributedDto {
    @ApiModelProperty("ip关联风险类型分布信息")
    private List<RiskType> typeDistributed;
    @ApiModelProperty("ip关联风险总数")
    private Long totalCount;
}
