package com.quanzhi.auditapiv2.biz.risk.service;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.model.NetworkDomain;
import com.quanzhi.audit_core.common.model.NetworkSegment;
import com.quanzhi.audit_core.common.model.Position;
import com.quanzhi.audit_core.resource.fetcher.client.fetcher.IpNetworkDomainFetcher;
import com.quanzhi.audit_core.resource.fetcher.client.resolve.IpCityResolve;
import com.quanzhi.audit_core.resource.fetcher.client.resolve.convert.NetworkSegmentConverter;
import com.quanzhi.audit_core.resource.fetcher.client.resolve.model.NetworkSegmentIpInfo;
import com.quanzhi.audit_core.resource.fetcher.client.utils.IpRegionalUtils;
import com.quanzhi.auditapiv2.biz.risk.constant.RiskConstant;
import com.quanzhi.auditapiv2.biz.risk.dto.ThreatIpDto;
import com.quanzhi.auditapiv2.biz.risk.dto.export.ThreatIpExportDto;
import com.quanzhi.auditapiv2.biz.risk.dto.search.ThreatIpSearchDto;
import com.quanzhi.auditapiv2.biz.risk.util.OkHttpUtil;
import com.quanzhi.auditapiv2.common.dal.dao.IIpLabelDao;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.ThreatIpTop10Dto;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.ip.IpLabel;
import com.quanzhi.auditapiv2.common.dal.enums.NacosSingleKeyConfigEnum;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.service.integration.impl.LicenseServiceImpl;
import com.quanzhi.auditapiv2.common.util.dto.AggregationDto;
import com.quanzhi.auditapiv2.common.util.dto.GroupDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.common.util.utils.IpLocationUtil;
import com.quanzhi.auditapiv2.common.util.utils.push.WphFileGatewayDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicy;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatIp;
import com.quanzhi.auditapiv2.core.risk.repository.*;
import com.quanzhi.auditapiv2.core.risk.repository.aggRisk.AggRiskDao;
import com.quanzhi.auditapiv2.core.service.manager.web.INacosSingleKeyUpdateService;
import com.quanzhi.auditapiv2.core.service.manager.web.push.WphSendService;
import com.quanzhi.metabase.core.model.query.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: HaoJun
 * @Date: 2021/8/9 10:00 上午
 * @description: 威胁ip service
 */
@Service
@Slf4j
public class ThreatIpService {

    @Autowired
    private IThreatIpDao threatIpDao;

    @Autowired
    private AggRiskDao aggRiskDao;

    @Autowired
    private IIpLabelDao ipLabelDao;

    @Autowired
    private IRiskPolicyDao riskPolicyDao;

    @Autowired
    private IDataRevealLabelCountDao dataRevealLabelCountDao;

    @Autowired
    private INacosSingleKeyUpdateService nacosSingleKeyUpdateService;

    @Autowired
    private IRiskThreatPortraitDao riskThreatPortraitDaoImpl;

    @Autowired
    private LicenseServiceImpl licenseService;

    @Autowired
    private IRiskIpAccountInfoDao riskIpAccountInfoDao;

    @Autowired
    private WphSendService wphSendService;

    private OkHttpClient client = OkHttpUtil.getUnSafeClient();

    //http://quanzhi.synology.me:8716/api/onlineIpInfo/checkIp 外网
    //http://************/api/onlineIpInfo/checkIp 内网
    @NacosValue(value = "${online.ip.info.url:http://quanzhi.synology.me:8716/api/onlineIpInfo/checkIp}", autoRefreshed = true)
    private String onlineIpInfoUrl;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    private String threatIpTempTable = "threatIpTemp";

    @DynamicValue(dataId = "common.networksegment.json", groupId = "common", typeClz = NetworkSegment.class)
    private List<NetworkSegment> networkSegments;

    /**
     * 机器码
     */
    private String matchingCode;

    /**
     * 获取威胁IPTop10
     *
     * @return {@link List}<{@link CommonDto}>
     */
    public List<ThreatIp> getThreatIpTop10() {
        return threatIpDao.getThreatTop10();
    }

    /**
     * @param threatIpList
     * <AUTHOR>
     * @description: 保存威胁ip数据
     * @date: 2021/8/13
     * @Return void
     */
    public void saveThreatIpInfo(List<ThreatIp> threatIpList) {
        //创建临时表
        threatIpDao.createThreatIpTable();
        //插入临时表
        threatIpDao.batchSave(threatIpList, threatIpTempTable);
        //临时表替换为正式表
        threatIpDao.threatIpTemp2ThreatTemp(threatIpTempTable);
    }

    /**
     * 根据ip更新标签列表
     *
     * @param ip
     * @param ipLabels
     */
    public void updateThreatIpLabels(String ip, List<String> ipLabels) {
        threatIpDao.updateThreatIpLabels(ip, ipLabels);
    }

    /**
     * @param threatIpSearchDto
     * <AUTHOR>
     * @description: 威胁ip分页
     * @date: 2021/8/14
     * @Return com.quanzhi.auditapiv2.common.util.dto.ListOutputDto<com.quanzhi.auditapiv2.biz.risk.dto.ThreatIpDto>
     */
    public ListOutputDto<ThreatIpDto> getThreatIpList(ThreatIpSearchDto threatIpSearchDto) {
        MetabaseQuery metabaseQuery = new MetabaseQuery();
        if (DataUtil.isNotEmpty(threatIpSearchDto)) {
            fillingThreatIpQuery(metabaseQuery, threatIpSearchDto);
        }
        long totalCount = threatIpDao.count(metabaseQuery, null);
        if (DataUtil.isEmpty(threatIpSearchDto.getLimit())) {
            threatIpSearchDto.setLimit(10000);
        }
        if (DataUtil.isEmpty(threatIpSearchDto.getPage())) {
            threatIpSearchDto.setPage(1);
        }
        metabaseQuery.setSkip((threatIpSearchDto.getPage() - 1) * threatIpSearchDto.getLimit());
        metabaseQuery.setLimit(threatIpSearchDto.getLimit());
        if (DataUtil.isNotEmpty(threatIpSearchDto.getSortField()) && DataUtil.isNotEmpty(threatIpSearchDto.getSort())) {
            if (threatIpSearchDto.getSort() == ConstantUtil.Sort.ASC) {
                metabaseQuery.setSort(new Sort[]{Sort.by(threatIpSearchDto.getSortField(), SortOrder.ASC)});
            } else {
                metabaseQuery.setSort(new Sort[]{Sort.by(threatIpSearchDto.getSortField(), SortOrder.DESC)});
            }
        }
       /* ListOutputDto<ThreatIp> pages = threatIpDao.page(metabaseQuery, null);
        List<ThreatIp> rows = pages.getRows();*/
        List<ThreatIp> rows = threatIpDao.find(metabaseQuery, null);
        ListOutputDto<ThreatIpDto> resultList = new ListOutputDto<ThreatIpDto>();
        List<ThreatIpDto> collect = rows.stream().map(threatIp -> ThreatIpDto.ThreatIpDtoMapper.INSTANCE.convert(threatIp)).collect(Collectors.toList());
        resultList.setRows(collect);
        resultList.setTotalCount(totalCount);
        return resultList;
    }

    private void fillingThreatIpQuery(MetabaseQuery metabaseQuery, ThreatIpSearchDto threatIpSearchDto) {
        if (DataUtil.isNotEmpty(threatIpSearchDto.getIp())) {
            metabaseQuery.where("ip", Predicate.REGEX, threatIpSearchDto.getIp());
        }
        if (DataUtil.isNotEmpty(threatIpSearchDto.getIpLabel())) {
            metabaseQuery.where("ipLabels", Predicate.ALL, threatIpSearchDto.getIpLabel());
        }
        if (DataUtil.isNotEmpty(threatIpSearchDto.getNetworkSegment())) {
            metabaseQuery.where("networkSegment", Predicate.IN, threatIpSearchDto.getNetworkSegment());
        }
        if (DataUtil.isNotEmpty(threatIpSearchDto.getCountry())) {
            metabaseQuery.where("country", Predicate.IS, threatIpSearchDto.getCountry());
        }
        if (DataUtil.isNotEmpty(threatIpSearchDto.getThreatState())) {
            metabaseQuery.where("threatState", Predicate.IS, threatIpSearchDto.getThreatState());
        }
        if (DataUtil.isNotEmpty(threatIpSearchDto.getThreatLabels())) {
            metabaseQuery.where("threatLabels", Predicate.IN, threatIpSearchDto.getThreatLabels());
        }
        if (DataUtil.isNotEmpty(threatIpSearchDto.getAccounts())) {
            metabaseQuery.where("accounts", Predicate.IN, threatIpSearchDto.getAccounts());
        }
        if (DataUtil.isNotEmpty(threatIpSearchDto.getFirstTimeStart()) && DataUtil.isNotEmpty(threatIpSearchDto.getFirstTimeEnd())) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(Criteria.where("firstTime").gte(threatIpSearchDto.getFirstTimeStart()), Criteria.where("firstTime").lte(threatIpSearchDto.getFirstTimeEnd()));
            metabaseQuery.getCriteria().add(criteria);
        }
        if (DataUtil.isNotEmpty(threatIpSearchDto.getLastTimeStart()) && DataUtil.isNotEmpty(threatIpSearchDto.getLastTimeEnd())) {
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(Criteria.where("lastTime").gte(threatIpSearchDto.getLastTimeStart()), Criteria.where("lastTime").lte(threatIpSearchDto.getLastTimeEnd()));
            metabaseQuery.getCriteria().add(criteria);
        }
        //资产权限控制
        if (DataUtil.isNotEmpty(threatIpSearchDto.getIsAssetAuthorization()) && threatIpSearchDto.getIsAssetAuthorization()) {

            if (DataUtil.isNotEmpty(threatIpSearchDto.getAppUriSet()) || DataUtil.isNotEmpty(threatIpSearchDto.getDepartmentSet())) {

                Criteria criteria = new Criteria();
                Criteria criteria1 = null;
                Criteria criteria2 = null;

                if (DataUtil.isNotEmpty(threatIpSearchDto.getAppUriSet())) {
                    criteria1 = Criteria.where("uri").in(threatIpSearchDto.getAppUriSet());
                }

                if (DataUtil.isNotEmpty(threatIpSearchDto.getDepartmentSet())) {
                    criteria2 = Criteria.where("departments.department").in(threatIpSearchDto.getDepartmentSet());
                }

                if (DataUtil.isNotEmpty(criteria1) && DataUtil.isEmpty(criteria2)) {
                    criteria = criteria.orOperator(criteria1);
                } else if (DataUtil.isEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                    criteria = criteria.orOperator(criteria2);
                } else if (DataUtil.isNotEmpty(criteria1) && DataUtil.isNotEmpty(criteria2)) {
                    criteria = criteria.orOperator(criteria1, criteria2);
                }

                metabaseQuery.getCriteria().add(criteria);

            } else {
                metabaseQuery.where("_id", Predicate.IS, "");
            }
        }
    }

    /**
     * <AUTHOR>
     * @description: 威胁ip分组
     * @date: 2021/8/14
     */
    public List<CommonGroupDto> getThreatIpGroup(ThreatIpSearchDto threatIpSearchDto) {
        List<CommonGroupDto> commonGroupDtoList = new ArrayList<>();
        GroupDto groupDto = new GroupDto();
        groupDto.setGroupFields(new String[]{threatIpSearchDto.getGroupField()});
        groupDto.setUnwind(threatIpSearchDto.getGroupField());
        groupDto.setAggrType(GroupDto.GroupTypeEnum.COUNT.getName());
        List<AggregationDto> list = null;
        MetabaseQuery query = new MetabaseQuery();
        fillingThreatIpQuery(query, threatIpSearchDto);
        try {
            list = threatIpDao.getThreatIpGroup(query, groupDto, threatIpSearchDto.getSortField(), threatIpSearchDto.getSort(), threatIpSearchDto.getPage(), threatIpSearchDto.getLimit());
            list.forEach(e -> commonGroupDtoList.add(CommonGroupDto.builder().name(e.getId()).id(e.getId()).count(Long.valueOf(String.valueOf(e.getResultAlias()))).build()));
        } catch (Exception exception) {
            log.error("威胁ip分组出错:", exception);
        }

        //排序，保证结果顺序一致
        Map<Long, List<CommonGroupDto>> collect = commonGroupDtoList.stream().collect(Collectors.groupingBy(CommonGroupDto::getCount));
        Set<Long> sortKey = new TreeSet<>(Long::compareTo);
        sortKey.addAll(collect.keySet());
        commonGroupDtoList.clear();
        for (Long key : sortKey) {
            if (collect.get(key).size() > 1) {
                collect.get(key).sort(Comparator.comparing(CommonGroupDto::getName));
                commonGroupDtoList.addAll(collect.get(key));
            } else {
                commonGroupDtoList.add(collect.get(key).get(0));
            }
        }
        Collections.reverse(commonGroupDtoList);

        return commonGroupDtoList;
    }

    /**
     * @param threatIpExportDto
     * @param workDir
     * @param fileName
     * <AUTHOR>
     * @description: 导出威胁ip
     * @date: 2021/8/18
     * @Return void
     */
    public void exportThreatIp(ThreatIpExportDto threatIpExportDto, String workDir, String fileName) throws Exception {
        //获取中文列头
        List<String> titileList = threatIpExportDto.getList().stream().map(e -> e.getTitle()).collect(Collectors.toList());
        //获取英文字段
        List<String> fieldList = threatIpExportDto.getList().stream().map(e -> e.getField()).collect(Collectors.toList());

        //获取数据
        ThreatIpSearchDto threatIpSearchDto = threatIpExportDto.getThreatIpSearchDto();
        BeanUtils.copyProperties(threatIpExportDto, threatIpSearchDto);
        ListOutputDto<ThreatIpDto> threatIpList = getThreatIpList(threatIpSearchDto);
        List<ThreatIpDto> rows = threatIpList.getRows();

        CsvWriter writer = CsvUtil.getWriter(workDir + File.separator + fileName, CharsetUtil.CHARSET_GBK);

        //准备数据
        List<List<String>> rowList = new ArrayList<>();
        rowList.add(titileList);
        for (ThreatIpDto threatIpDto : rows) {
            List<String> dataList = new ArrayList<>();
            for (String fieldName : fieldList) {
                Object fieldValue = ReflectUtil.getFieldValue(threatIpDto, fieldName);
                if ("riskNum".equals(fieldName) || "confirmRiskNum".equals(fieldName)) {
                    Long num = (Long) fieldValue;
                    dataList.add(String.valueOf(num));
                } else if ("networkSegment".equals(fieldName)) {
                    List<String> networkSegment = (List<String>) fieldValue;
                    String networkSegmentStr = networkSegment.stream().collect(Collectors.joining(","));
                    dataList.add(networkSegmentStr);
                } else if ("firstTime".equals(fieldName) || "lastTime".equals(fieldName)) {
                    Long time = (Long) fieldValue;
                    dataList.add(DateUtil.format(time));
                } else if ("ipLabels".equals(fieldName) || "threatLabels".equals(fieldName) || "accounts".equals(fieldName)) {
                    if (fieldValue != null) {
                        List<String> labels = (List<String>) fieldValue;
                        if (labels.size() > 0) {
                            String labelStr = labels.stream().collect(Collectors.joining(","));
                            dataList.add(labelStr);
                        } else {
                            dataList.add("");
                        }
                    } else {
                        dataList.add("");
                    }
                } else {
                    if (fieldValue != null) {
                        dataList.add(String.valueOf(fieldValue));
                    } else {
                        dataList.add("");
                    }
                }
            }
            rowList.add(dataList);
        }
        writer.write(rowList);
        if (productType.equals(ProductTypeEnum.wph.name())) {
            WphFileGatewayDto wphFileGatewayDto = wphSendService.getWphFileGatewayDto(threatIpExportDto.getApplicant_id(), threatIpExportDto.getTags(), threatIpExportDto.getReasons(), threatIpExportDto.getUsage(), rowList.size());
            wphSendService.postSendFile(wphFileGatewayDto, workDir + File.separator + fileName);
        }
    }

    /**
     * 统计威胁IP数量
     *
     * @return
     */
    public List<ThreatIp> getAll() {
        return threatIpDao.getAll();
    }


    /**
     * 解析ip的威胁信息
     */
    public ThreatIp parseThreatIp(String ip, Boolean onlineIpOpen) {
        ThreatIp threatIp = new ThreatIp();
        parsingIpInfo(threatIp, ip);
        if (DataUtil.isEmpty(threatIp.getIp())) {
            return threatIp;
        }

        //首次触发时间
        MetabaseQuery firstTimeQuery = new MetabaseQuery();
        firstTimeQuery.where("entities.value", Predicate.IS, ip);
        firstTimeQuery.sort(Sort.by("firstTime", SortOrder.ASC));
        firstTimeQuery.fields(Arrays.asList("firstTime"));
        firstTimeQuery.limit(1);
        AggRiskInfo firstTimeRisk = aggRiskDao.findOne(firstTimeQuery, "aggRiskInfo");
        if (firstTimeRisk != null) {
            threatIp.setFirstTime(firstTimeRisk.getFirstTime());
        }

        //最近触发时间
        MetabaseQuery lastTimeQuery = new MetabaseQuery();
        lastTimeQuery.where("entities.value", Predicate.IS, ip);
        lastTimeQuery.sort(Sort.by("lastTime", SortOrder.DESC));
        lastTimeQuery.fields(Arrays.asList("lastTime"));
        lastTimeQuery.limit(1);
        AggRiskInfo lastTimeRisk = aggRiskDao.findOne(lastTimeQuery, "aggRiskInfo");
        if (lastTimeRisk != null) {
            threatIp.setLastTime(lastTimeRisk.getLastTime());
        }

        //确认异常数
        MetabaseQuery countQuery = new MetabaseQuery();
        countQuery.where("entities.value", Predicate.IS, ip);
        countQuery.where("state", Predicate.IS, RiskInfo.RiskStateEnum.HAS_HANDLE.getState());
        long confirmRiskNum = aggRiskDao.count(countQuery, "aggRiskInfo");
        threatIp.setConfirmRiskNum(confirmRiskNum);

        //获取该ip对应的异常名称
        Query query = Query.query(org.springframework.data.mongodb.core.query.Criteria.where("entities.value").is(ip));
        List<String> riskNameList = aggRiskDao.findDistinct(query, "name", "aggRiskInfo", String.class);

        threatIp.setIp(ip);
        threatIp.setRiskNum(riskThreatPortraitDaoImpl.countRiskInfoByIp(ip));
        threatIp.setIpLabels(riskNameList);

        //获取ip的关联账号信息
        List<String> accounts = riskIpAccountInfoDao.findAllAccountByIp(ip);
        threatIp.setAccounts(accounts);

        //获取威胁标签
        parseThreatLabel(ip, threatIp, onlineIpOpen);
        return threatIp;
    }

    private void parseThreatLabel(String ip, ThreatIp threatIp, Boolean onlineIpOpen) {
        if (DataUtil.isEmpty(onlineIpOpen)) {
            //检查在线IP情报库的开关是否被打开
            Map<String, Object> results = nacosSingleKeyUpdateService.getNacosSingleKeyValus(Collections.singletonList(NacosSingleKeyConfigEnum.ONLINE_DATABASE_ENABLE.getSingleKey()));
            onlineIpOpen = results != null && results.containsKey(NacosSingleKeyConfigEnum.ONLINE_DATABASE_ENABLE.getSingleKey()) && results.get(NacosSingleKeyConfigEnum.ONLINE_DATABASE_ENABLE.getSingleKey()) != null && (Boolean) results.get(NacosSingleKeyConfigEnum.ONLINE_DATABASE_ENABLE.getSingleKey());
        }
        if (onlineIpOpen) {
            //调用在线IP情报库服务接口，传入IP和机器码获取在线威胁IP信息
            String machineCode = getMachineCode();
            if (machineCode == null) {
                return;
            }
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("ip", ip);
                jsonObject.put("machineCode", machineCode);
                String params = jsonObject.toJSONString();
                RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=utf-8"), params);
                Request request = new Request.Builder().url(onlineIpInfoUrl).post(requestBody).build();
                Response response = client.newCall(request).execute();
                if (response.isSuccessful()) {
                    if (DataUtil.isNotEmpty(response.body())) {
                        if (DataUtil.isNotEmpty(response.body()) || DataUtil.isNotEmpty(response.body().string())) {
                            JSONObject threatInfo = JSONObject.parseObject(response.body().string());
                            if (threatInfo.containsKey("data")) {
                                String data = threatInfo.get("data").toString();
                                ThreatIp onlineThreatIp = JSON.parseObject(data, ThreatIp.class);
                                threatIp.setThreatState(onlineThreatIp.getThreatState());
                                threatIp.setThreatLabels(onlineThreatIp.getThreatLabels());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("parse threat label error:", e);
            }
        }
    }

    private String getMachineCode() {
        if (matchingCode != null) {
            return matchingCode;
        }
        try {
            matchingCode = licenseService.getUniqueCode();
            return matchingCode;
        } catch (Exception e) {
            log.error("get matchingCode error:", e);
            return null;
        }
    }

    /**
     * <AUTHOR>
     * @description: 获取最新的ip标签映射关系
     * @date: 2021/9/11
     * @Return java.util.Map<java.lang.String, java.lang.String>
     */
    private Map<String, String> getIpLabelsMapping() {
        Map<String, String> map = new HashMap<>();
        try {
            //获取全部异常规则
            List<RiskPolicy> riskPolicies = riskPolicyDao.selectAllRiskPolicyWithDel();
            //获取全部IP标签
            List<IpLabel> ipLabels = ipLabelDao.selectIpLabelWithDel();
            for (RiskPolicy riskPolicy : riskPolicies) {
                String threatIpId = riskPolicy.getThreatLabel();
                for (IpLabel ipLabel : ipLabels) {
                    if (ipLabel.getId().equals(threatIpId)) {
                        map.put(riskPolicy.getName(), ipLabel.getName());
                    }
                }
            }
            return map;
        } catch (Exception exception) {
            log.error("IP标签异常", exception);
            return RiskConstant.IP_LABELS_MAPPING;
        }
    }

    /**
     * @param threatIp
     * @param ip
     * <AUTHOR>
     * @description: 解析ip信息
     * @date: 2021/8/13
     * @Return void
     */
    private void parsingIpInfo(ThreatIp threatIp, String ip) {
//        Position position = IpCityResolve.resolveIpCity(ip, Collections.EMPTY_LIST);
        List<NetworkSegment> networkSegmentsList = com.quanzhi.audit_core.resource.fetcher.client.utils.DataUtil.deepCopy(networkSegments);

        List<NetworkSegmentIpInfo> networkSegmentConfigInfos = NetworkSegmentConverter.convert2(networkSegmentsList);
        Position position = new Position();
        Map<String, Object> ipNetworkDomainsAndArea = IpNetworkDomainFetcher.getNetworkDomainsAndArea(ip, networkSegmentConfigInfos);
        if (ipNetworkDomainsAndArea.containsKey("area")) {
            position = (Position) ipNetworkDomainsAndArea.get("area");
        }
        if (DataUtil.isNotEmpty(position)) {
            threatIp.setLocation(IpLocationUtil.getLocationStr(position));
            threatIp.setCountry(position.getCountry() != null ? position.getCountry() : null);
            threatIp.setProvince(position.getProvince() != null ? position.getProvince() : null);
            threatIp.setCity(position.getCity() != null ? position.getCity() : null);
        } else {
            threatIp.setLocation(IpLocationUtil.OTHER);
        }

        List<NetworkDomain> networkDomains = IpNetworkDomainFetcher.getNetworkDomains(ip, networkSegmentConfigInfos);
        List<String> domainIds = new ArrayList<>();
        for (NetworkDomain networkDomain : networkDomains) {
            domainIds.add(networkDomain.getId());
        }
        threatIp.setNetworkSegment(domainIds);
        threatIp.setIp(ip);
    }

}
