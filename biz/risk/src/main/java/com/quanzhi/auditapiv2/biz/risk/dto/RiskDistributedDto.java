package com.quanzhi.auditapiv2.biz.risk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @Auther: yangzixian
 * @Date: 2021/8/9 14:32
 * @Description:
 */
@Data
@Builder
@ApiModel("关联风险分布")
public class RiskDistributedDto {

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("总数")
    private Integer total;

    @ApiModelProperty("高危")
    private Long high;

    @ApiModelProperty("中危")
    private Long mid;

    @ApiModelProperty("低危")
    private Long low;

}
