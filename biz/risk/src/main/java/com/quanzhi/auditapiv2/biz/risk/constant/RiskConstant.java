package com.quanzhi.auditapiv2.biz.risk.constant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2021/8/13  上午
 * @description: 风险相关常量
 **/
public class RiskConstant {


    /**
     * 属于数据泄漏类的风险
     */
    public static List<String> REVEAL_DATA_RISK = Arrays.asList(new String[]{
            "API单次返回大量敏感数据", "IP执行参数遍历", "IP执行翻页遍历", "异常请求查询敏感数据", "IP访问频次异常"
    });


    /**
     * 威胁ip涉及的风险范围
     */
    public static List<String> THREAT_IP_INVOLVE_RISK = Arrays.asList(new String[]{
            "IP执行参数遍历", "IP执行翻页遍历", "异常请求查询敏感数据", "IP访问次数异常", "IP高频撞库", "SSRF攻击", "SQL注入", "路径试探", "账号暴力破解", "境外IP拉取大量数据", "Web攻击", "验证码暴破",
            "异常参数请求", "IP多账号", "短信炸弹", "渗透测试尝试", "SSRF攻击", "API单次返回大量敏感数据", "登录API存在弱密码登录账号", "异常参数获取敏感数据", "session多终端访问", "session异地访问",
            "账号异地登录"
    });

    /**
     * 数据泄漏类风险且不包括接口的风险范围
     */
    public static List<String> DATA_REVEAL_IP_RISK = Arrays.asList(new String[]{
            "IP执行参数遍历", "IP执行翻页遍历", "异常请求查询敏感数据", "IP访问次数异常", "境外IP拉取大量数据"
    });


    /**
     * ip标签与风险的映射关系
     */
    public static Map<String, String> IP_LABELS_MAPPING = new HashMap<String, String>() {
        {
            put("接口单次返回大量敏感数据", "单次返回过大");
            put("IP执行参数遍历", "参数遍历");
            put("IP执行翻页遍历", "翻页遍历");
            put("异常请求查询敏感数据", "异常请求");
            put("IP访问次数异常", "次数异常");
            put("IP高频撞库", "撞库");
            put("账号暴力破解", "暴破");
            put("账号弱密码登录", "弱密码");
            put("SSRF攻击", "SSRF");
            put("SQL注入", "SQL注入");
            put("路径试探", "路径试探");
            put("境外IP拉取大量数据", "境外IP拉取大量数据");
            put("异常参数获取敏感数据", "异常参数请求");
            put("IP使用多账号", "IP多账号");
            put("短信炸弹", "短信炸弹");
            put("渗透测试尝试", "渗透测试尝试");
            put("API单次返回大量敏感数据", "API大量敏感数据");
            put("登录API存在弱密码登录账号", "API弱密码");
            put("Web攻击", "Web攻击");
            put("验证码暴破", "验证码暴破");
            put("session多终端访问", "session多终端访问");
            put("session异地访问", "session异地访问");
            put("账号异地登录", "账号异地登录");
        }
    };


}