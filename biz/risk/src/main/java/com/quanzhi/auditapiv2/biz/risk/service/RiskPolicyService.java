package com.quanzhi.auditapiv2.biz.risk.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.quanzhi.auditapiv2.biz.risk.dto.RiskPolicyFeatureEnumDto;
import com.quanzhi.auditapiv2.biz.risk.dto.SimpleRiskPolicyDto;
import com.quanzhi.auditapiv2.common.dal.common.dao.ViewMenuDao;
import com.quanzhi.auditapiv2.common.dal.dto.common.CascadeDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.entity.RiskGroup;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicy;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicySearchDto;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskGroupDao;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskPolicyDao;
import com.quanzhi.auditapiv2.core.trace.util.StringUtils;
import com.quanzhi.operate.operateHandler.IOperateHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 《异常规则业务层》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Service
@Slf4j
public class RiskPolicyService {
    private Logger logger = LoggerFactory.getLogger(RiskPolicyService.class);

    @Autowired
    private IRiskPolicyDao riskPolicyDaoImpl;

    private final ViewMenuDao viewMenuDao;

    @NacosInjected
    private ConfigService configService;

    private final IRiskGroupDao riskGroupDao;

    private final IOperateHandlerService operateHandlerService;

    private ScriptEngine scriptEngine;

    private final Integer reqAndRspLabelLimit = 10;

    private final String RISK_POLICY_TEMPLATE_DATA_ID = "auditapiv2.riskTemplate.json";

    private final String RISK_POLICY_ENTITY_DATA_ID = "auditapiv2.risk.entity.json";

    private final String RISK_GROUP_ID = "auditapiv2";

    private static final long timeoutMs = 5000;

    private final String RISK_POLICY_FEATURE_ENUM_DATA_ID = "auditapiv2.riskPolicyFeature.json";

    private final String ipBaseLineId = "22";

    private final String accountBaseLineId = "23";

    public RiskPolicyService(
            ViewMenuDao viewMenuDao,
            IRiskGroupDao riskGroupDao,
            IOperateHandlerService operateHandlerService
    ) {
        this.viewMenuDao = viewMenuDao;
        this.riskGroupDao = riskGroupDao;
        this.operateHandlerService = operateHandlerService;
        ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
        scriptEngine = scriptEngineManager.getEngineByName("javascript");
    }


    /**
     * 查询异常规则列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public List<RiskPolicy> getRiskPolicyList() throws Exception {

        return riskPolicyDaoImpl.selectRiskPolicyList();
    }

    public ListOutputDto<RiskPolicy> getRiskPolicyList(RiskPolicySearchDto riskPolicySearchDto) throws Exception {
        ListOutputDto<RiskPolicy> datas = new ListOutputDto<>();
        List<RiskPolicy> riskPolicies = riskPolicyDaoImpl.selectRiskPolices(riskPolicySearchDto);
        Long totalCount = riskPolicyDaoImpl.totalCount(riskPolicySearchDto);
        datas.setRows(riskPolicies);
        datas.setTotalCount(totalCount);
        return datas;
    }

    public Boolean getByGroup(String group) {
        return riskPolicyDaoImpl.getByGroup(group).size() > 0;
    }

    public String updateRiskPolicyState(String policyId, Boolean enable) {
        riskPolicyDaoImpl.updateRiskPolicyState(policyId, enable);
        return "修改成功！";
    }

    public String deleteRiskPolicy(String policyId) {
        riskPolicyDaoImpl.deleteRiskPolicy(policyId);
        return "删除成功！";
    }

    public List<CascadeDto> getCascades() {
        List<RiskPolicy> list = riskPolicyDaoImpl.getAll();
        if (list == null) {
            return Collections.emptyList();
        }
        Map<String, List<RiskPolicy>> riskPolicyMap = list.stream()
                .filter(riskPolicy -> riskPolicy.getDelFlag() == null || !riskPolicy.getDelFlag())
                .collect(Collectors.groupingBy(RiskPolicy::getGroup));
        List<CascadeDto> cascadeDtos = new ArrayList<>();
        for (Map.Entry<String, List<RiskPolicy>> entry : riskPolicyMap.entrySet()) {
            CascadeDto cascadeDto = new CascadeDto();
            cascadeDto.setLabel(entry.getKey());
            cascadeDto.setValue(entry.getValue().get(0).getGroup());
            cascadeDto.setChildren(new ArrayList<>());
            for (RiskPolicy policy : entry.getValue()) {
                CascadeDto child = new CascadeDto();
                child.setLabel(policy.getName());
                child.setValue(policy.getId());
                cascadeDto.getChildren().add(child);
            }
            cascadeDtos.add(cascadeDto);
        }
        return cascadeDtos;
    }

    public List<RiskGroup> getAllGroups() {
        List<RiskGroup> riskGroups = riskGroupDao.getAll();
        return riskGroups;
    }

    public String addRiskGroup(RiskGroup group) throws Exception {
        riskGroupDao.save(group);
        viewMenuDao.initViewMenuState(Collections.singletonList("riskViewMenu"));
        return "添加成功！";
    }

    public Boolean checkRiskGroup(RiskGroup group) {
        long count = riskGroupDao.getCount(Criteria.where("groupName").is(group.getGroupName()));
        return count > 0;
    }

    public String deleteRiskGroup(RiskGroup group) {
        riskGroupDao.delete("riskGroup", Criteria.where("_id").is(group.getId()));
        return "删除成功！";
    }

    public List<String> getRiskGroups() {
        List<RiskPolicy> list = riskPolicyDaoImpl.selectRiskPolices(null);
        if (list == null) {
            return Collections.emptyList();
        }
        List<String> groups = list.stream().map(riskPolicy -> riskPolicy.getGroup()).collect(Collectors.toList());
        return groups;
    }

    public List<CommonGroupDto> groupRiskPolices(RiskPolicySearchDto riskPolicySearchDto) throws Exception {
        List<CommonGroupDto> commonGroupDtos = riskPolicyDaoImpl.groupRiskPolices(riskPolicySearchDto);
        if ("level".equals(riskPolicySearchDto.getGroupField())) {
            for (CommonGroupDto commonGroupDto : commonGroupDtos) {
                if ("1.0".equals(commonGroupDto.getId()) || "1".equals(commonGroupDto.getId())) {
                    commonGroupDto.setName("低等级");
                } else if ("2.0".equals(commonGroupDto.getId()) || "2".equals(commonGroupDto.getId())) {
                    commonGroupDto.setName("中等级");
                } else {
                    commonGroupDto.setName("高等级");
                }
            }
        }
        //排序，保证结果顺序一致
        Map<Long, List<CommonGroupDto>> collect = commonGroupDtos.stream()
                .collect(Collectors.groupingBy(CommonGroupDto::getCount));
        Set<Long> sortKey = new TreeSet<>(Long::compareTo);
        sortKey.addAll(collect.keySet());
        List<CommonGroupDto> result = new ArrayList<>();
        for (Long key : sortKey) {
            if (collect.get(key).size() > 1) {
                collect.get(key).sort(Comparator.comparing(CommonGroupDto::getName));
                result.addAll(collect.get(key));
            } else {
                result.add(collect.get(key).get(0));
            }
        }
        Collections.reverse(result);
        return result;
    }

    public Map<String, List<SimpleRiskPolicyDto>> getSimpleRiskPolicyList() throws Exception {
        List<RiskPolicy> policies = getRiskPolicyList();
        if (policies == null) {
            return Collections.emptyMap();
        }
        Map<String, List<RiskPolicy>> policyByGroup = policies.stream()
                .collect(Collectors.groupingBy(policy -> policy.getGroup()));
        Map<String, List<SimpleRiskPolicyDto>> result = new HashMap<>();
        Map<String, List<SimpleRiskPolicyDto>> sortResult = new LinkedHashMap<>();
        for (String key : policyByGroup.keySet()) {
            List<SimpleRiskPolicyDto> samples = policyByGroup.get(key).stream()
                    .map(riskPolicy -> SimpleRiskPolicyDto.builder()
                            .name(riskPolicy.getName())
                            .id(riskPolicy.getId())
                            .threatLabel(riskPolicy.getThreatLabel())
                            .build())
                    .collect(Collectors.toList());
            result.put(key, samples);
        }
        for (String key : result.keySet()) {
            if (key.equals("数据泄漏类")) {
                sortResult.put(key, result.get(key));
            }
        }
        for (String key : result.keySet()) {
            if (key.equals("账号安全类")) {
                sortResult.put(key, result.get(key));
            }
        }
        for (String key : result.keySet()) {
            if (key.equals("Web攻击类")) {
                sortResult.put(key, result.get(key));
            }
        }
        return sortResult;
    }

    /**
     * id查询异常规则详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public RiskPolicy getRiskPolicyById(String id) throws Exception {

        return riskPolicyDaoImpl.selectRiskPolicyById(id);
    }

    public void delAndSave(JSONObject riskPolicy) {
        riskPolicyDaoImpl.delete("riskPolicy", Criteria.where("_id").is(riskPolicy.getString("_id")));
        riskPolicyDaoImpl.saveJson(riskPolicy);
    }

    /**
     * 编辑
     *
     * @param riskPolicy 异常策略
     * @return {@link String}
     */
//    public String save(RiskPolicy riskPolicy) throws Exception {
//        riskPolicy.setUpdateTime(System.currentTimeMillis());
//        if (riskPolicy.getIsDefaultRisk() && !ipBaseLineId.equals(riskPolicy.getId()) && !accountBaseLineId.equals(riskPolicy.getId())) {
//            riskPolicyDaoImpl.update(riskPolicy.getId(),
//                    ResourceUpdates.create().set("desc", riskPolicy.getDesc())
//                            .set("enable", riskPolicy.getEnable())
//                            .set("updateTime", riskPolicy.getUpdateTime()),
//                    "riskPolicy");
//        } else {
//            checkPolicy(riskPolicy);
//
//            //前端保存没有这个字段就默认塞个false
//            if (riskPolicy.getDelFlag() == null) {
//                riskPolicy.setDelFlag(false);
//            }
//
//            //如果有主体规则，需要生成主体规则的hash值
//            generateEventRuleHash(riskPolicy);
//
//            // 排除策略中是带API的前端没有添加httpapi: 前缀，需要携带一下，不然无法过滤
//            checkUriPrefix(riskPolicy);
//
//            // 生成指标描述
//            generateLeftDesc(riskPolicy);
//
//            // 采样配置
//            generateSampleConfig(riskPolicy);
//        }
//
//        //先删除已有的同样策略
//        riskPolicyDaoImpl.delete("riskPolicy", Criteria.where("_id").is(riskPolicy.getId()));
//        //保存新的
//        riskPolicyDaoImpl.save(riskPolicy);
//        return riskPolicy.getId();
//    }
    public String saveV2(RiskPolicy riskPolicy) throws Exception {
        //重名检查
        checkPolicy(riskPolicy);
        riskPolicy.setUpdateTime(System.currentTimeMillis());
        riskPolicyDaoImpl.upsertRiskPolicy(riskPolicy);
        return riskPolicy.getId();
    }

    /**
     * 生成采样配置
     */
//    private static void generateSampleConfig(RiskPolicy riskPolicy) {
//        if (riskPolicy.getQuotaRule() != null) {
//            Rule quotaRule = riskPolicy.getQuotaRule();
//            Rule rule = JSONObject.parseObject(JSON.toJSONString(quotaRule), Rule.class);
//            for (Expression expression : rule.getExprList()) {
//                if (expression.getRight() != null
//                        && VariableType.CONSTANT == expression.getRight().getType()
//                        && expression.getRight().getDataType() != null
//                        && "Number".equals(expression.getRight().getDataType().name())) {
//                    expression.getRight().setValue((Integer) expression.getRight().getValue() / 2);
//                }
//            }
//            riskPolicy.getSampleConfig().setProperties(new HashMap<>());
//            riskPolicy.getSampleConfig().getProperties().put("rule", rule);
//            riskPolicy.getSampleConfig().setName("dsl");
//            riskPolicy.getSampleConfig().setLimit(50);
//            if (Policy.RiskTimeTypeEnum.MINUTE.name().equals(riskPolicy.getRiskTimeConfig().getType().name())
//                    || Policy.RiskTimeTypeEnum.FIFTEENMINUTE.name().equals(riskPolicy.getRiskTimeConfig().getType().name())
//                    || Policy.RiskTimeTypeEnum.HOUR.name().equals(riskPolicy.getRiskTimeConfig().getType().name())) {
//                riskPolicy.getSampleConfig()
//                        .setPhase(Policy.SampleConfig.PhaseEnum.AGG.name());
//            } else {
//                riskPolicy.getSampleConfig()
//                        .setPhase(Policy.SampleConfig.PhaseEnum.AFTER.name());
//            }
//        }
//    }

//    private static void checkUriPrefix(RiskPolicy riskPolicy) {
//        if (riskPolicy.getExceptionRule() == null || riskPolicy.getExceptionRule().getExprList() == null) {
//            return;
//        }
//        for (Expression expression : riskPolicy.getExceptionRule().getExprList()) {
//            if (expression.getLeft().getValue() != null && "$.apiUri".equals(expression.getLeft().getValue())) {
//                if (expression.getRight() != null && expression.getRight().getValue() != null
//                        && expression.getRight().getValue() instanceof List) {
//                    expression.getRight().setValue(((List) expression.getRight().getValue()).stream().map(api -> {
//                        if (!String.valueOf(api).startsWith("httpapi:")) {
//                            return "httpapi:" + api;
//                        }
//                        return api;
//                    }).collect(Collectors.toList()));
//                } else if (expression.getRight() != null && expression.getRight().getValue() != null
//                        && expression.getRight().getValue() instanceof String) {
//                    if (!String.valueOf(expression.getRight().getValue()).startsWith("httpapi:")) {
//                        expression.getRight().setValue("httpapi:" + expression.getRight().getValue());
//                    }
//                }
//            }
//        }
//    }
    private void checkPolicy(RiskPolicy riskPolicy) throws Exception {
        if (StringUtils.isEmpty(riskPolicy.getName())) {
            throw new Exception("异常名称不可为空！");
        }

        if (riskPolicy.getName().length() > 40) {
            throw new Exception("异常名称长度不可大于40！");
        }

        List<RiskPolicy> policies = riskPolicyDaoImpl.listAllPolicies();
        if (DataUtil.isEmpty(policies)) {
            return;
        }
        for (RiskPolicy policy : policies) {
            if (policy.getDelFlag()) {
                continue;
            }
            if (policy.getName().equals(riskPolicy.getName())) {
                if (riskPolicy.getId() == null) {
                    throw new Exception("异常名称不可重复");
                } else if (!policy.getId().equals(riskPolicy.getId())) {
                    throw new Exception("异常名称不可重复");
                }
            }
        }
    }

    /**
     * 生成指标描述
     */
//    private static void generateLeftDesc(RiskPolicy riskPolicy) {
//        if (riskPolicy.getQuotaRule() == null || riskPolicy.getQuotaRule().getExprList() == null) {
//            return;
//        }
//
//        for (Expression expression : riskPolicy.getQuotaRule().getExprList()) {
//
//            //单事件异常的left对象中的值可能需要重新拼jsonPath
//            if ("TIMESTAMP".equals(riskPolicy.getRiskTimeConfig().getType().name())) {
//
//                // 事件异常 请求标签去重数据量 返回标签去重数据量 这两个指标需要根据 leftDesc 中配置的标签放入 left对象的value中
//                if (expression.getLeftDesc() != null &&
//                        ("$.reqLabelContentDistinctCountByLabel.[LABEL]".equals(expression.getLeftDesc().getUdf())
//                                || "$.rspLabelContentDistinctCountByLabel.[LABEL]".equals(expression.getLeftDesc().getUdf()))) {
//
//                    String label = Optional.ofNullable(expression.getLeftDesc().getDesc())
//                            .map(leftOptions -> leftOptions.get(0))
//                            .map(leftOption -> leftOption.getValue().toString())
//                            .orElse("");
//
//                    expression.getLeft().setValue(expression.getLeftDesc().getUdf().replace("[LABEL]", label));
//                }
//            }
//
//            if (!"TIMESTAMP".equals(riskPolicy.getRiskTimeConfig().getType().name())
//                    && expression.getLeftDesc() != null
//                    && expression.getLeftDesc().getDesc() != null
//                    && (expression.getLeftDesc().getUdf() != null || expression.getLeftDesc().getUdfParam() != null)) {
//
//                if (expression.getLeftDesc().getDesc().size() > 1) {
//                    expression.getLeftDesc().getDesc().sort(new Comparator<Expression.LeftOption>() {
//                        @Override
//                        public int compare(Expression.LeftOption a, Expression.LeftOption b) {
//                            try {
//                                String value_1 = a.getKey();
//                                String value_2 = b.getKey();
//
//                                if (DataUtil.isEmpty(value_1)) {
//                                    return 1;
//                                }
//
//                                if (DataUtil.isEmpty(value_2)) {
//                                    return -1;
//                                }
//
//                                return value_1.compareTo(value_2);
//
//                            } catch (Exception e) {
//                                return 1;
//                            }
//                        }
//                    });
//                }
//
//                //对指标描述生成hash
//                generateLeftValue(expression);
//            }
//        }
//    }

//    private static void generateLeftValue(Expression expression) {
//        if (expression.getLeftDesc() == null || CollectionUtils.isEmpty(expression.getLeftDesc().getDesc())) {
//            return;
//        }
//
//        String keyed;
//        if (expression.getLeftDesc().getDesc().size() == 1) {
//            // 为单个描述简化 keyed
//            keyed = generateSingleLeftDesc(expression);
//
//        } else {
//            StringBuffer s = new StringBuffer();
//            expression.getLeftDesc().getDesc().forEach(leftOption -> {
//                s.append("_");
//                s.append(leftOption.getKey());
//                s.append("_");
//                s.append(leftOption.getOp());
//                s.append("_");
//                s.append(leftOption.getValue().toString());
//            });
//
//            keyed = MD5Util.md5(s.toString());
//            String leftVal = "$." + expression.getLeftDesc().getUdf() + "." + keyed;
//            expression.getLeft().setValue(leftVal);
//        }
//
//        // 右变量为基线时
//        if (expression.getRight() != null)
//            if (VariableType.NAMED == expression.getRight().getType()) {
//                if ("baseline".equals(expression.getRight().getVarName())) {
//                    // 和左变量的 UDF 相同
//                    expression.getRight().setValue("$." + expression.getLeftDesc().getUdf() + "." + keyed);
//                    expression.getRight().getConfigs().put("keyed", keyed);
//                }
//            }
//    }

    /**
     * 为单个指标描述生成简化的 keyed
     */
//    private static String generateSingleLeftDesc(Expression expression) {
//        String keyed;
//        Expression.LeftOption leftOption = expression.getLeftDesc().getDesc().get(0);
//        if (leftOption.getKey().equals("visitTime")) {
//            Map map = (Map) leftOption.getValue();
//            keyed = "time" + String.valueOf(map.get("startTime")).replace(":", "")
//                    + String.valueOf(map.get("endTime")).replace(":", "");
//        } else if (leftOption.getKey().equals("all")) {
//            keyed = "all";
//        } else if (leftOption.getKey().equals("visitDate")) {
//            List dayOfWeekList = (List) ((Map) leftOption.getValue()).get("dayOfWeekList");
//            StringBuilder t = new StringBuilder();
//            for (Object o : dayOfWeekList) {
//                t.append(o);
//            }
//            keyed = "week" + t;
//        } else {
//            StringBuffer s = new StringBuffer();
//            s.append("_");
//            s.append(leftOption.getKey());
//            s.append("_");
//            s.append(leftOption.getOp());
//            s.append("_");
//            s.append(leftOption.getValue().toString());
//            keyed = MD5Util.md5(s.toString());
//        }
//
//        expression.getLeft().setValue("$." + expression.getLeftDesc().getUdf() + "." + keyed);
//        return keyed;
//    }

//    private static void generateEventRuleHash(RiskPolicy riskPolicy) {
//        if (riskPolicy.getEventRule() == null) {
//            return;
//        }
//        if (riskPolicy.getEventRule().getExprList() == null) {
//            return;
//        }
//
//        //如果主体规则超过2个，需要先根据left中的value进行排序后再hash
//        if (riskPolicy.getEventRule().getExprList().size() > 1) {
//
//            riskPolicy.getEventRule().getExprList().sort((a, b) -> {
//                try {
//                    String value_1 = Optional.ofNullable(a.getLeft()).map(i -> i.getValue().toString()).orElse("");
//                    String value_2 = Optional.ofNullable(b.getLeft()).map(i -> i.getValue().toString()).orElse("");
//
//                    if (DataUtil.isEmpty(value_1)) {
//                        return 1;
//                    }
//
//                    if (DataUtil.isEmpty(value_2)) {
//                        return -1;
//                    }
//
//                    return value_1.compareTo(value_2);
//
//                } catch (Exception e) {
//                    return 1;
//                }
//            });
//        }
//
//
//        //对主体描述生成hash
//        StringBuffer s = new StringBuffer();
//
//        for (Expression expression : riskPolicy.getEventRule().getExprList()) {
//            s.append("_");
//            s.append(expression.getLeft().getValue().toString());
//            s.append("_");
//            s.append(expression.getOperator());
//            s.append("_");
//            s.append(expression.getRight() != null ? expression.getRight().getValue().toString() : "");
//        }
//
//        String entityRuleHash = MD5Util.md5(s.toString());
//        riskPolicy.setEventRuleHash(entityRuleHash);
//
//    }
//    private void checkLabelLimit(Set<String> req_rsp_label_set, List<RiskPolicy> policys) throws Exception {
//        if (req_rsp_label_set.isEmpty()) {
//            return;
//        }
//
//        policys.forEach(i -> collectDataLabelSet(req_rsp_label_set, i));
//
//        if (req_rsp_label_set.size() > reqAndRspLabelLimit) {
//            throw new Exception("标签去重数据量异常中最多允许启用" + reqAndRspLabelLimit + "条！");
//        }
//    }
//
//    private static void collectDataLabelSet(Set<String> req_rsp_label_set, RiskPolicy i) {
//        if (i.getQuotaRule() == null || CollectionUtils.isEmpty(i.getQuotaRule().getExprList())) {
//            return;
//        }
//        i.getQuotaRule().getExprList().forEach(j -> {
//            if (j.getLeftDesc() == null || CollectionUtils.isEmpty(j.getLeftDesc().getDesc())) {
//                return;
//            }
//            j.getLeftDesc().getDesc().forEach(k -> {
//                if (k.getKey().equals("reqDataLabel") || k.getKey().equals("rspDataLabel")) {
//                    req_rsp_label_set.add(k.getKey() + "_" + k.getValue());
//                }
//            });
//        });
//    }

    /**
     * 获取策略模版
     *
     * @return
     */
    public JSONObject getRiskPolicyTemplate() {

        try {

            String content = configService.getConfig(RISK_POLICY_TEMPLATE_DATA_ID, RISK_GROUP_ID, timeoutMs);

            return JSON.parseObject(content);

        } catch (Exception e) {

            log.error("getRiskPolicyTemplate error", e);

            return null;
        }

    }

    /**
     * 获取主体模版
     *
     * @return
     */
    public JSONObject getRiskEntityConfig() {

        try {

            String content = configService.getConfig(RISK_POLICY_ENTITY_DATA_ID, RISK_GROUP_ID, timeoutMs);

            return JSON.parseObject(content);

        } catch (Exception e) {

            log.error("getRiskEntityConfig error", e);

            return null;
        }

    }

    /**
     * 获取策略描述中的枚举值
     *
     * @return
     */
    public List<RiskPolicyFeatureEnumDto> getRiskFeatureEnums() {

        try {

            String content = configService.getConfig(RISK_POLICY_FEATURE_ENUM_DATA_ID, RISK_GROUP_ID, timeoutMs);

            List<RiskPolicyFeatureEnumDto> riskPolicyFeatureEnumDtos = JSONArray.parseArray(content, RiskPolicyFeatureEnumDto.class);

            for (RiskPolicyFeatureEnumDto riskPolicyFeatureEnumDto : riskPolicyFeatureEnumDtos) {

                if (riskPolicyFeatureEnumDto.getNacosConfigs() != null
                        && riskPolicyFeatureEnumDto.getNacosConfigs().size() > 0
                ) {
                    if (riskPolicyFeatureEnumDto.getNacosConfigs().size() == 1) {
                        RiskPolicyFeatureEnumDto.NacosConfig nacosConfig = riskPolicyFeatureEnumDto.getNacosConfigs()
                                .get(0);
                        if (nacosConfig.getDataId() != null
                                && nacosConfig.getGroupId() != null) {
                            String val = configService.getConfig(nacosConfig.getDataId(), nacosConfig.getGroupId(), timeoutMs);

                            if (nacosConfig.getUdf() != null) {

                                String target = this.transformOriginalValByUdf(val, nacosConfig.getUdf());
                                riskPolicyFeatureEnumDto.setValue(JSONArray.parseArray(target));

                            } else {

                                riskPolicyFeatureEnumDto.setValue(JSONArray.parseArray(val));
                            }
                        }
                    } else {
                        JSONArray finalArray = new JSONArray();
                        for (RiskPolicyFeatureEnumDto.NacosConfig nacosConfig : riskPolicyFeatureEnumDto.getNacosConfigs()) {
                            if (nacosConfig.getDataId() != null
                                    && nacosConfig.getGroupId() != null) {
                                String val = configService.getConfig(nacosConfig.getDataId(), nacosConfig.getGroupId(), timeoutMs);

                                if (nacosConfig.getUdf() != null) {

                                    String target = this.transformOriginalValByUdf(val, nacosConfig.getUdf());
                                    JSONArray array = JSONArray.parseArray(target);
                                    finalArray.addAll(array);
                                } else {

                                    JSONArray array = JSONArray.parseArray(val);
                                    finalArray.addAll(array);
                                }
                            }
                        }
                        riskPolicyFeatureEnumDto.setValue(finalArray);
                    }
                }

                if (riskPolicyFeatureEnumDto.getActionConfigFrontTransform() != null) {

                    Map<String, Object> result = operateHandlerService.handleActions(riskPolicyFeatureEnumDto.getActionConfigFrontTransform(), null);

                    if (result.get("groupList") != null) {

                        riskPolicyFeatureEnumDto.setValue(result.get("groupList"));
                    }
                }
            }

            return riskPolicyFeatureEnumDtos;

        } catch (Exception e) {

            log.error("getRiskFeatureEnums error", e);

            return null;
        }
    }

    private synchronized String transformOriginalValByUdf(String originalStr, String js) {

        try {

            scriptEngine.put("originalStr", originalStr);
            String result = (String) scriptEngine.eval(js);

            return result;

        } catch (ScriptException e) {
            // logger.warn(e.getMessage());

            return null;
        }
    }

    public List<RiskPolicy> getRiskPolicyByIds(List<String> ids) {
        return riskPolicyDaoImpl.selectRiskPolicyByIds(ids);
    }
}