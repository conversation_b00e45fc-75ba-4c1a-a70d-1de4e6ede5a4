package com.quanzhi.auditapiv2.biz.risk.service;


import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicyAllowList;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskPolicyAllowListDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;


/**
 * 《风险白名单业务层》
 *
 * <AUTHOR> [<EMAIL>]
 * @Project:
 * @Module ID:
 * @Comments:
 * @JDK version used:      <JDK1.8>
 * @since 2021-08-10-上午9:56:10
 */
@Service
public class RiskPolicyAllowListService {
    private Logger logger = LoggerFactory.getLogger(RiskPolicyAllowListService.class);

    @Autowired
    private IRiskPolicyAllowListDao riskPolicyAllowListDaoImpl;

    /**
     * id查询险白名单详情
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public RiskPolicyAllowList getRiskPolicyAllowListById(String id) throws Exception {

        return riskPolicyAllowListDaoImpl.selectRiskPolicyAllowListById(id);
    }

    /**
     * 查询风险白名单列表
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public RiskPolicyAllowList getRiskPolicyAllowList(String policyId, String type, String value) throws Exception {

        return riskPolicyAllowListDaoImpl.selectRiskPolicyAllowList(policyId, type, value);
    }

    /**
     * 批量新增风险白名单
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public List<RiskPolicyAllowList> addRiskPolicyAllowList(List<RiskPolicyAllowList> list) throws Exception {

        for (RiskPolicyAllowList riskPolicyAllowList : list) {

            riskPolicyAllowListDaoImpl.insertRiskPolicyAllowList(riskPolicyAllowList);
        }

        return list;
    }

    /**
     * 批量编辑风险白名单
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public List<RiskPolicyAllowList> editRiskPolicyAllowList(List<RiskPolicyAllowList> list) throws Exception {

        for (RiskPolicyAllowList riskPolicyAllowList : list) {

            riskPolicyAllowListDaoImpl.updateRiskPolicyAllowList(riskPolicyAllowList);
        }

        return list;
    }

    /**
     * 按风险策略id删除
     *
     * @param id id
     * @throws Exception 异常
     */
    public void deleteByPolicyId(String id) throws Exception {
        riskPolicyAllowListDaoImpl.deleteByRiskPolicyId(id);
    }

    public void updateByPolicyId(String id, List<RiskPolicyAllowList> riskPolicyAllowLists) throws Exception {
        //删除
        this.deleteByPolicyId(id);
        //添加
        this.addRiskPolicyAllowList(riskPolicyAllowLists);
    }

    public void updatePolicyId(String policyId) {
        riskPolicyAllowListDaoImpl.updatePolicyId(policyId);
    }

    /**
     * 按风险策略id获取
     *
     * @param id id
     * @return {@link List}<{@link RiskPolicyAllowList}>
     * @throws Exception 异常
     */
    public List<RiskPolicyAllowList> getByPolicyId(String id) throws Exception {
        return riskPolicyAllowListDaoImpl.getByRiskPolicyId(id);
    }

    /**
     * 批量删除风险白名单
     *
     * @param
     * @return
     * @Comments: <对此方法的描述，可以引用系统设计中的描述>
     * <AUTHOR> [<EMAIL>]
     * @since 2021-08-10 9:56
     */
    public void delRiskPolicyAllowList(List<String> ids) throws Exception {

        riskPolicyAllowListDaoImpl.deleteRiskPolicyAllowList(ids);
    }

    public List<RiskPolicyAllowList> getByPolicyIds(List<String> ids) {
        return riskPolicyAllowListDaoImpl.getByRiskPolicyIds(ids);
    }
}