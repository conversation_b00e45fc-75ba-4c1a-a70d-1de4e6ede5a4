package com.quanzhi.auditapiv2.biz.risk.dto.export;

import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.AggRiskOperatorDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2021/8/20 上午
 * @description:
 **/
@Data
@ToString(callSuper = true)
public class AggRiskExportDto extends CommonExportDto {

    private AggRiskOperatorDto aggRiskOperatorDto;
    private List<String> ids;
    private List<String> exportIds;

}