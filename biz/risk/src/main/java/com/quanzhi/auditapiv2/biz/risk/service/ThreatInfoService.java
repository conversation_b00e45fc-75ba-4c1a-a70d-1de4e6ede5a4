package com.quanzhi.auditapiv2.biz.risk.service;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ReflectUtil;
import com.quanzhi.audit_core.common.enums.EntityTypeEnum;
import com.quanzhi.audit_core.common.model.AccountInfo;
import com.quanzhi.auditapiv2.biz.risk.dto.export.ThreatInfoExportDto;
import com.quanzhi.auditapiv2.common.dal.dao.AccountInfoDao;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonDto;
import com.quanzhi.auditapiv2.common.dal.dto.common.CommonGroupDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.ThreatIpTop10Dto;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.common.util.utils.push.WphFileGatewayDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.ThreatInfoSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatInfo;
import com.quanzhi.auditapiv2.core.risk.repository.IRiskInfoDao;
import com.quanzhi.auditapiv2.core.risk.repository.IThreatInfoDao;
import com.quanzhi.auditapiv2.core.risk.repository.aggRisk.AggRiskDao;
import com.quanzhi.auditapiv2.core.service.manager.web.push.WphSendService;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import com.quanzhi.metabase.core.model.query.Sort;
import com.quanzhi.metabase.core.model.query.SortOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yangzixian
 * @date: 1/3/2023 17:43
 * @description:
 */
@Service
@Slf4j
public class ThreatInfoService {

    private final IThreatInfoDao threatInfoDao;

    private final AccountInfoDao accountInfoDao;

    private final AggRiskDao aggRiskDao;

    private final WphSendService wphSendService;

    public ThreatInfoService(IThreatInfoDao threatInfoDao, AccountInfoDao accountInfoDao, AggRiskDao aggRiskDao, WphSendService wphSendService) {
        this.threatInfoDao = threatInfoDao;
        this.accountInfoDao = accountInfoDao;
        this.aggRiskDao = aggRiskDao;
        this.wphSendService = wphSendService;
    }

    public ThreatInfo upsertThreatInfo(ThreatInfo threatInfo) {
        threatInfoDao.upsertThreatInfo(threatInfo.getThreatEntity(), threatInfo);
        return threatInfo;
    }

    public Long getCount() {
        return threatInfoDao.getCount();
    }

    /**
     * 获取威胁信息清单
     *
     * @param threatInfoSearchDto
     * @return
     */
    public ListOutputDto<ThreatInfo> listThreatInfos(ThreatInfoSearchDto threatInfoSearchDto) {
        ListOutputDto<ThreatInfo> datas = new ListOutputDto<>();
        //数据列表
        List<ThreatInfo> threatInfos = threatInfoDao.listThreatInfos(threatInfoSearchDto);
        //总数
        Long totalCount = threatInfoDao.countThreatInfos(threatInfoSearchDto);
        datas.setRows(threatInfos);
        datas.setTotalCount(totalCount);
        return datas;
    }

    public List<ThreatInfo> getAll() {
        return threatInfoDao.getAllByCriteria(Criteria.where("delFlag").is(false));
    }

    public List<CommonGroupDto> getThreatInfoGroup(ThreatInfoSearchDto threatInfoSearchDto) {
        List<CommonGroupDto> commonGroupDtoList = threatInfoDao.groupThreatInfo(threatInfoSearchDto);
        //排序，保证结果顺序一致
        Map<Long, List<CommonGroupDto>> collect = commonGroupDtoList.stream().collect(Collectors.groupingBy(CommonGroupDto::getCount));
        Set<Long> sortKey = new TreeSet<>(Long::compareTo);
        sortKey.addAll(collect.keySet());
        List<CommonGroupDto> result = new ArrayList<>(commonGroupDtoList.size());
        for (Long key : sortKey) {
            if (collect.get(key).size() > 1) {
                collect.get(key).sort(Comparator.comparing(CommonGroupDto::getName));
                result.addAll(collect.get(key));
            } else {
                result.add(collect.get(key).get(0));
            }
        }
        Collections.reverse(result);
        return result;
    }

    /**
     * 导出威胁信息
     *
     * @param threatInfoExportDto
     * @param workDir
     * @param fileName
     */
    public void exportThreatInfo(ThreatInfoExportDto threatInfoExportDto, String workDir, String fileName) {
        //获取中文列头
        List<String> titleList = threatInfoExportDto.getList().stream().map(e -> e.getTitle()).collect(Collectors.toList());
        //获取英文字段
        List<String> fieldList = threatInfoExportDto.getList().stream().map(e -> e.getField()).collect(Collectors.toList());

        //获取数据
        ThreatInfoSearchDto threatInfoSearchDto = threatInfoExportDto.getThreatInfoSearchDto();
        List<ThreatInfo> rows = new ArrayList<>();
        if (DataUtil.isNotEmpty(threatInfoExportDto.getIds())) {
            rows = threatInfoDao.listThreatInfos(threatInfoExportDto.getIds(), threatInfoSearchDto);
        } else {
            if (DataUtil.isNotEmpty(threatInfoSearchDto)) {
                ListOutputDto<ThreatInfo> threatInfoList = listThreatInfos(threatInfoSearchDto);
                rows = threatInfoList.getRows();
            } else {
                rows = threatInfoDao.getAllByCriteria(Criteria.where("delFlag").is(false));
            }
        }

        CsvWriter writer = CsvUtil.getWriter(workDir + File.separator + fileName, CharsetUtil.CHARSET_GBK);

        //准备数据
        List<List<String>> rowList = new ArrayList<>();
        rowList.add(titleList);
        for (ThreatInfo threatInfo : rows) {
            List<String> dataList = new ArrayList<>();
            for (String fieldName : fieldList) {
                Object fieldValue = ReflectUtil.getFieldValue(threatInfo, fieldName);
                if ("riskNum".equals(fieldName) || "confirmRiskNum".equals(fieldName)) {
                    if (fieldValue != null) {
                        Long num = (Long) fieldValue;
                        dataList.add(String.valueOf(num));
                    } else {
                        dataList.add("");
                    }
                } else if ("networkSegment".equals(fieldName)) {
                    if (fieldValue != null) {
                        List<String> networkSegment = (List<String>) fieldValue;
                        String networkSegmentStr = networkSegment.stream().collect(Collectors.joining(","));
                        dataList.add(networkSegmentStr);
                    } else {
                        dataList.add("");
                    }
                } else if ("threatType".equals(fieldName)) {
                    String threatType = (String) fieldValue;
                    if ("ACCOUNT".equals(threatType)) {
                        dataList.add("账号");
                    } else {
                        dataList.add(threatType);
                    }
                } else if ("blockFlag".equals(fieldName)) {
                    if (fieldValue != null) {
                        Boolean blockFlag = (Boolean) fieldValue;
                        if (blockFlag) {
                            dataList.add("开启");
                        } else {
                            dataList.add("关闭");
                        }
                    } else {
                        dataList.add("关闭");
                    }
                } else if ("firstTime".equals(fieldName) || "lastTime".equals(fieldName)) {
                    if (fieldValue != null) {
                        Long time = (Long) fieldValue;
                        dataList.add(DateUtil.format(time));
                    } else {
                        dataList.add("");
                    }
                } else if ("riskNames".equals(fieldName) || "threatLabels".equals(fieldName)) {
                    if (fieldValue != null) {
                        List<String> labels = (List<String>) fieldValue;
                        if (labels.size() > 0) {
                            String labelStr = labels.stream().collect(Collectors.joining(","));
                            dataList.add(labelStr);
                        } else {
                            dataList.add("");
                        }
                    } else {
                        dataList.add("");
                    }
                } else {
                    if (fieldValue != null) {
                        dataList.add(String.valueOf(fieldValue));
                    } else {
                        dataList.add("");
                    }
                }
            }
            rowList.add(dataList);
        }
        writer.write(rowList);
    }

    /**
     * 唯品会推送
     */
    public void exportThreatInfo(ThreatInfoExportDto threatInfoExportDto, String workDir, String fileName, String applicant_id, String tags, String reasons, String usage) throws Exception {
        //获取中文列头
        List<String> titleList = threatInfoExportDto.getList().stream().map(e -> e.getTitle()).collect(Collectors.toList());
        //获取英文字段
        List<String> fieldList = threatInfoExportDto.getList().stream().map(e -> e.getField()).collect(Collectors.toList());

        //获取数据
        ThreatInfoSearchDto threatInfoSearchDto = threatInfoExportDto.getThreatInfoSearchDto();
        List<ThreatInfo> rows = new ArrayList<>();
        if (DataUtil.isNotEmpty(threatInfoExportDto.getIds())) {
            rows = threatInfoDao.listThreatInfos(threatInfoExportDto.getIds(), threatInfoSearchDto);
        } else {
            if (DataUtil.isNotEmpty(threatInfoSearchDto)) {
                ListOutputDto<ThreatInfo> threatInfoList = listThreatInfos(threatInfoSearchDto);
                rows = threatInfoList.getRows();
            } else {
                rows = threatInfoDao.getAllByCriteria(Criteria.where("delFlag").is(false));
            }
        }
        String filePath = workDir + File.separator + fileName;

        CsvWriter writer = CsvUtil.getWriter(filePath, CharsetUtil.CHARSET_GBK);

        //准备数据
        List<List<String>> rowList = new ArrayList<>();
        rowList.add(titleList);
        for (ThreatInfo threatInfo : rows) {
            List<String> dataList = new ArrayList<>();
            for (String fieldName : fieldList) {
                Object fieldValue = ReflectUtil.getFieldValue(threatInfo, fieldName);
                if ("riskNum".equals(fieldName) || "confirmRiskNum".equals(fieldName)) {
                    if (fieldValue != null) {
                        Long num = (Long) fieldValue;
                        dataList.add(String.valueOf(num));
                    } else {
                        dataList.add("");
                    }
                } else if ("networkSegment".equals(fieldName)) {
                    if (fieldValue != null) {
                        List<String> networkSegment = (List<String>) fieldValue;
                        String networkSegmentStr = networkSegment.stream().collect(Collectors.joining(","));
                        dataList.add(networkSegmentStr);
                    } else {
                        dataList.add("");
                    }
                } else if ("threatType".equals(fieldName)) {
                    String threatType = (String) fieldValue;
                    if ("ACCOUNT".equals(threatType)) {
                        dataList.add("账号");
                    } else {
                        dataList.add(threatType);
                    }
                } else if ("blockFlag".equals(fieldName)) {
                    if (fieldValue != null) {
                        Boolean blockFlag = (Boolean) fieldValue;
                        if (blockFlag) {
                            dataList.add("开启");
                        } else {
                            dataList.add("关闭");
                        }
                    } else {
                        dataList.add("关闭");
                    }
                } else if ("firstTime".equals(fieldName) || "lastTime".equals(fieldName)) {
                    if (fieldValue != null) {
                        Long time = (Long) fieldValue;
                        dataList.add(DateUtil.format(time));
                    } else {
                        dataList.add("");
                    }
                } else if ("riskNames".equals(fieldName) || "threatLabels".equals(fieldName)) {
                    if (fieldValue != null) {
                        List<String> labels = (List<String>) fieldValue;
                        if (labels.size() > 0) {
                            String labelStr = labels.stream().collect(Collectors.joining(","));
                            dataList.add(labelStr);
                        } else {
                            dataList.add("");
                        }
                    } else {
                        dataList.add("");
                    }
                } else {
                    if (fieldValue != null) {
                        dataList.add(String.valueOf(fieldValue));
                    } else {
                        dataList.add("");
                    }
                }
            }
            rowList.add(dataList);
        }
        writer.write(rowList);

        WphFileGatewayDto wphFileGatewayDto = wphSendService.getWphFileGatewayDto(applicant_id, tags, reasons, usage, rowList.size());
        wphSendService.postSendFile(wphFileGatewayDto, filePath);

    }

    /**
     * 解析账号的威胁信息
     */
    public ThreatInfo parseThreatAccount(String account) {
        AccountInfo threatAccount = accountInfoDao.getAccountInfoByAccount(account);//todo appUri
        if (threatAccount != null) {
            //API3.0 将威胁账号信息转换为威胁信息存入数据库
            ThreatInfo threatInfo = ThreatInfo.ThreatInfoMapper.INSTANCE.convert(threatAccount);
            threatInfo.setThreatType("ACCOUNT");
            //获取该账号对应的异常名称
            Query query = Query.query(org.springframework.data.mongodb.core.query.Criteria.where("entities.value").is(account).and("entities.type").is(EntityTypeEnum.ACCOUNT.name()));
            List<String> riskNameList = aggRiskDao.findDistinct(query, "name", "aggRiskInfo", String.class);

            threatInfo.setRiskNames(riskNameList);

            //首次触发时间
            MetabaseQuery firstTimeQuery = new MetabaseQuery();
            firstTimeQuery.where("entities.value", Predicate.IS, account);
            firstTimeQuery.where("entities.type", Predicate.IS, EntityTypeEnum.ACCOUNT.name());
            firstTimeQuery.sort(Sort.by("firstTime", SortOrder.ASC));
            firstTimeQuery.fields(Arrays.asList("firstTime"));
            firstTimeQuery.limit(1);
            AggRiskInfo firstTimeRisk = aggRiskDao.findOne(firstTimeQuery, "aggRiskInfo");
            if (firstTimeRisk != null) {
                threatInfo.setFirstTime(firstTimeRisk.getFirstTime());
            }

            //最近触发时间
            MetabaseQuery lastTimeQuery = new MetabaseQuery();
            lastTimeQuery.where("entities.value", Predicate.IS, account);
            lastTimeQuery.where("entities.type", Predicate.IS, EntityTypeEnum.ACCOUNT.name());
            lastTimeQuery.sort(Sort.by("lastTime", SortOrder.DESC));
            lastTimeQuery.fields(Arrays.asList("lastTime"));
            lastTimeQuery.limit(1);
            AggRiskInfo lastTimeRisk = aggRiskDao.findOne(lastTimeQuery, "aggRiskInfo");
            if (lastTimeRisk != null) {
                threatInfo.setLastTime(lastTimeRisk.getLastTime());
            }

            //确认异常数
            MetabaseQuery countQuery = new MetabaseQuery();
            countQuery.where("entities.value", Predicate.IS, account);
            countQuery.where("entities.type", Predicate.IS, EntityTypeEnum.ACCOUNT.name());
            long riskNum = aggRiskDao.count(countQuery, "aggRiskInfo");
            threatInfo.setRiskNum(riskNum);
            countQuery.where("state", Predicate.IS, RiskInfo.RiskStateEnum.HAS_HANDLE.getState());
            long confirmRiskNum = aggRiskDao.count(countQuery, "aggRiskInfo");
            threatInfo.setConfirmRiskNum(confirmRiskNum);

            return threatInfo;
        } else {
            return null;
        }
    }

    public List<String> getThreatLabelEnum() {
        List<ThreatInfo> threatInfos = threatInfoDao.getAllByCriteria(Criteria.where("delFlag").is(false));
        List<String> threatLabelEnums = new ArrayList<>();
        if (DataUtil.isNotEmpty(threatInfos)) {
            threatLabelEnums = threatInfos.stream().filter(o -> o.getThreatLabels() != null).flatMap(o -> o.getThreatLabels().stream()).distinct().collect(Collectors.toList());
        }
        return threatLabelEnums;
    }

    /**
     * 获取威胁IPTop10
     *
     * @return {@link List}<{@link CommonDto}>
     */
    public List<ThreatIpTop10Dto> getThreatIpTop10() {
        List<ThreatInfo> threatIpTop10 = threatInfoDao.getThreatIpTop10();
        List<ThreatIpTop10Dto> result = new ArrayList<>();
        for (ThreatInfo threatInfo : threatIpTop10) {
            ThreatIpTop10Dto ip = ThreatIpTop10Dto.builder().ip(threatInfo.getThreatEntity()).riskNum(threatInfo.getRiskNum()).build();
            result.add(ip);
        }
        return result;
    }

}
