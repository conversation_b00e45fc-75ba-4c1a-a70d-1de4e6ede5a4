package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.model.NetworkSegment;
import com.quanzhi.audit_core.common.model.Position;
import com.quanzhi.auditapiv2.common.dal.dto.NetworkSegmentDto;
import com.quanzhi.auditapiv2.common.util.utils.ServiceException;
import com.quanzhi.auditapiv2.core.service.manager.web.INetworkSegmentService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.NetworkSegmentServiceImpl;
import com.quanzhi.auditapiv2.core.trace.util.DataUtil;
import com.quanzhi.auditapiv2.core.trace.util.StringUtils;
import com.quanzhi.awdb_core.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class NetworkSegmentUpdate implements UpgradeService {

    private final INetworkSegmentService networkSegmentService;

    public NetworkSegmentUpdate(NetworkSegmentServiceImpl networkSegmentService) {
        this.networkSegmentService = networkSegmentService;
    }

    @Override
    public int getVersion() {
        return 20231130;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        //获取当前所有网段配置
        List<NetworkSegment> segmentList = networkSegmentService.getAll();
        for (NetworkSegment networkSegment : segmentList) {
            //二级网段名称为空的配置，将一级网段名称设置为二级网段，一级网段使用局域网填充
            if (DataUtil.isNotEmpty(networkSegment.getDomain()) && StringUtils.isEmpty(networkSegment.getDomain().getSecondLevel())) {
                String newName = networkSegment.getDomain().getFirstLevel().replaceAll("-", "");
                networkSegment.getDomain().setSecondLevel(newName);
                networkSegment.getDomain().setFirstLevel("局域网");
                //网段检查
                if (DataUtil.isNotEmpty(networkSegment.getNetworkSegmentList())) {
                    List<NetworkSegment.NetworkSegmentIp> networkSegmentList = new ArrayList<>();
                    for (NetworkSegment.NetworkSegmentIp networkSegmentIp : networkSegment.getNetworkSegmentList()) {
                        String ips = "";
                        if (DataUtil.isNotEmpty(networkSegmentIp.getStartIp()) && DataUtil.isNotEmpty(networkSegmentIp.getEndIp())) {
                            ips = networkSegmentIp.getStartIp() + "-" + networkSegmentIp.getEndIp();
                        } else if (DataUtil.isNotEmpty(networkSegmentIp.getStartIp())) {
                            ips = networkSegmentIp.getStartIp();
                        } else if (DataUtil.isNotEmpty(networkSegmentIp.getEndIp())) {
                            ips = networkSegmentIp.getEndIp();
                        }
                        if (checkNetwork(ips)) {
                            networkSegmentList.add(networkSegmentIp);
                        }
                    }
                    networkSegment.setNetworkSegmentList(networkSegmentList);
                }
                if (DataUtil.isNotEmpty(networkSegment.getNetworkSegmentList())) {
                    networkSegmentService.delById(networkSegment.getId());
                    //升级成功的网段，重设id
                    networkSegment.setId(networkSegment.getDomain().getFirstLevel() + "-" + networkSegment.getDomain().getSecondLevel());
                    networkSegmentService.save(networkSegment);
                } else {
                    networkSegmentService.delById(networkSegment.getId());
                }
            }
        }
        NetworkSegmentDto networkOut = networkSegmentService.getListById("互联网-境外");
        if (DataUtil.isEmpty(networkOut.getId())) {
            NetworkSegment networkSegmentOut = new NetworkSegment();
            networkSegmentOut.setId("互联网-境外");
            networkSegmentOut.setNetworkSegmentList(new ArrayList<>());
            NetworkSegment.Domain domainOut = new NetworkSegment.Domain();
            domainOut.setFirstLevel("互联网");
            domainOut.setSecondLevel("境外");
            networkSegmentOut.setDomain(domainOut);
            networkSegmentOut.setPosition(new Position());
            networkSegmentOut.setType(1);
            networkSegmentService.save(networkSegmentOut);
        }
        NetworkSegmentDto networkIn = networkSegmentService.getListById("互联网-境内");
        if (DataUtil.isEmpty(networkIn.getId())) {
            NetworkSegment networkSegmentIn = new NetworkSegment();
            networkSegmentIn.setId("互联网-境内");
            networkSegmentIn.setNetworkSegmentList(new ArrayList<>());
            NetworkSegment.Domain domainIn = new NetworkSegment.Domain();
            domainIn.setFirstLevel("互联网");
            domainIn.setSecondLevel("境内");
            networkSegmentIn.setDomain(domainIn);
            networkSegmentIn.setPosition(new Position());
            networkSegmentIn.setType(1);
            networkSegmentService.save(networkSegmentIn);
        }
        NetworkSegmentDto networkLocal = networkSegmentService.getListById("局域网-其他");
        if (DataUtil.isEmpty(networkLocal.getId())) {
            NetworkSegment networkSegmentLocal = new NetworkSegment();
            networkSegmentLocal.setId("局域网-其他");
            networkSegmentLocal.setNetworkSegmentList(new ArrayList<>());
            NetworkSegment.Domain domainIn = new NetworkSegment.Domain();
            domainIn.setFirstLevel("局域网");
            domainIn.setSecondLevel("其他");
            networkSegmentLocal.setDomain(domainIn);
            networkSegmentLocal.setPosition(new Position());
            networkSegmentLocal.setType(1);
            networkSegmentService.save(networkSegmentLocal);
        }
    }

    private boolean checkNetwork(String ips) {
        boolean flag1 = false, flag2 = false, flag3 = false, flag4 = false;
        //校验导入或手动填写的网段配置
        //1、使用通配符进行IP范围的配置，例如：192.168.0.*；
        if (ips.contains("*")) {
            if (ips.contains("-") || ips.contains("/")) {
                return false;
            }
            ips = ips.replaceAll("\\*", "0");
            if (!ips.contains(",")) {
                //1.1、192.168.0.*；
                if (!checkIp(ips)) {
                    return false;
                }
            } else {
                //1.2、192.168.0.*,192.168.1.*,……；
                String[] split_ips = ips.split(",");
                for (String splitIp : split_ips) {
                    if (!checkIp(splitIp)) {
                        return false;
                    }
                }
            }
            flag1 = true;
        }

        //2、使用IP网段进行IP范围的配置，例如：***********-*************；
        if (ips.contains("-")) {
            if (ips.contains("*") || ips.contains("/")) {
                return false;
            }
            String[] split_ips = ips.split("-");
            for (String splitIp : split_ips) {
                if (!splitIp.contains(",")) {
                    //2.1、***********-*************；
                    if (!checkIp(splitIp)) {
                        return false;
                    }
                } else {
                    //2.2、***********-*************,***********-*************,……；
                    String[] split_ips2 = splitIp.split(",");
                    for (String splitIp2 : split_ips2) {
                        if (!checkIp(splitIp2)) {
                            return false;
                        }
                    }
                }
            }
            flag2 = true;
        }

        //3、使用子网掩码进行IP范围的配置，例如：***********/28；
        if (ips.contains("/")) {
            if (ips.contains("*") || ips.contains("-")) {
                return false;
            }
            if (!ips.contains(",")) {
                //3.1、***********/28；
                String ip = ips.split("/")[0];
                if (!checkIp(ip)) {
                    return false;
                }
            } else {
                //3.2、***********/28,***********/28,……；
                String[] split_ips = ips.split(",");
                for (String splitIp : split_ips) {
                    String ip = ips.split("/")[0];
                    if (!checkIp(ip)) {
                        return false;
                    }
                }
            }
            flag3 = true;
        }

        //4、不实用任何通配符的基础形式
        if (!ips.contains("*") && !ips.contains("-") && !ips.contains("/")) {
            if (!checkIp(ips)) {
                return false;
            }
            flag4 = true;
        }

        //不符合以上四种格式的都算错
        if (!flag1 && !flag2 && !flag3 && !flag4) {
            return false;
        }
        return true;
    }

    private boolean checkIp(String ip) {
        try {
            if (ip.contains("*")) {
                ip = ip.replaceAll("\\*", "0");
            }
            if (ip.contains("/")) {
                ip = ip.substring(0, ip.indexOf("/"));
            }

            return IpUtil.isIPV6_IPV4(ip);
        } catch (Exception e) {
            log.error("ip校验失败：{}", ip, e);
        }

        return false;
    }

}
