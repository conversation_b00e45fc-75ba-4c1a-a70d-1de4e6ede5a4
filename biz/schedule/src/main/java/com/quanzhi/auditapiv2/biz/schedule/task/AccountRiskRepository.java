package com.quanzhi.auditapiv2.biz.schedule.task;

import com.quanzhi.auditapiv2.core.risk.repository.IRiskInfoDao;
import com.quanzhi.metabase.core.model.dto.RiskLevelMatchDto;
import org.springframework.stereotype.Service;

@Service
public class AccountRiskRepository extends AbstactIpAndAccountTask {
    private final IRiskInfoDao riskInfoDao;

    public AccountRiskRepository(IRiskInfoDao riskInfoDao) {
        this.riskInfoDao = riskInfoDao;
    }

    public RiskLevelMatchDto.Risk getRiskInfoBy(String account, String date, boolean refresh) {
        if (date == null || refresh) {
            return getRiskInfo(account, "ACCOUNT");
        }
        if (riskInfoDao.exist(account, date)) {
            return getRiskInfo(account, "ACCOUNT");
        }
        return null;
    }
}
