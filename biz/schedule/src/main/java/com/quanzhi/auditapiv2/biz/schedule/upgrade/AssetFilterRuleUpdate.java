package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceFilterService;
import com.quanzhi.metabase.core.model.filter.AssetFilterRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create at 2024/1/2 17:47
 * @description: 3.0的资产过滤规则升到3.1
 **/
@Component
@Slf4j
public class AssetFilterRuleUpdate implements UpgradeService {


    private final IResourceFilterService resourceService;
    private final MongoTemplate mongoTemplate;

    public AssetFilterRuleUpdate(IResourceFilterService resourceService, MongoTemplate mongoTemplate) {
        this.resourceService = resourceService;
        this.mongoTemplate = mongoTemplate;
    }


    @Override
    public int getVersion() {
        return 20240408;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        Sort sort=Sort.by(Sort.Order.asc("createTime"));

        log.info("start AssetFilterRuleUpdate");
        //应用规则
        AssetFilterRule appRule = mongoTemplate.findOne(Query.query(Criteria.where("type").is("APP").and("configType").is("FILTER").and("resource").regex("amap.com")).with(sort).limit(1), AssetFilterRule.class);
        if (appRule != null&&!appRule.getDelFlag()&&!appRule.getId().contains("FILTER")) {
            appRule.setDelFlag(true);
            appRule.setOperator("upgrade");
            resourceService.saveAssetFilterRule(appRule);
        }
        //API规则
        AssetFilterRule apiRule = mongoTemplate.findOne(Query.query(Criteria.where("type").is("API").and("configType").is("FILTER").and("resource").regex("etc/passwd")).with(sort).limit(1), AssetFilterRule.class);
        if (apiRule != null&&!apiRule.getDelFlag()&&!apiRule.getId().contains("FILTER")) {
            apiRule.setDelFlag(true);
            apiRule.setOperator("upgrade");
            resourceService.saveAssetFilterRule(apiRule);
        }
        //请求后缀
        AssetFilterRule reqSuffixRule = mongoTemplate.findOne(Query.query(Criteria.where("type").is("REQ_SUFFIX").and("configType").is("FILTER").and("resource").regex("woff,ico,mp4")).with(sort).limit(1), AssetFilterRule.class);
        if (reqSuffixRule != null&&!reqSuffixRule.getDelFlag()&&!reqSuffixRule.getId().contains("FILTER")) {
            reqSuffixRule.setDelFlag(true);
            reqSuffixRule.setOperator("upgrade");
            resourceService.saveAssetFilterRule(reqSuffixRule);
        }
        //请求UA
        AssetFilterRule reqUaRule = mongoTemplate.findOne(Query.query(Criteria.where("type").is("REQ_UA").and("configType").is("FILTER").and("resource").regex("scanbot,webcrusier,akari")).with(sort).limit(1), AssetFilterRule.class);
        if (reqUaRule != null&&!reqUaRule.getDelFlag()&&!reqUaRule.getId().contains("FILTER")) {
            reqUaRule.setDelFlag(true);
            reqUaRule.setOperator("upgrade");
            resourceService.saveAssetFilterRule(reqUaRule);
        }
        //返回状态码
        AssetFilterRule rspCodeRule = mongoTemplate.findOne(Query.query(Criteria.where("type").is("RSP_CODE").and("configType").is("FILTER").and("resource").regex("101")).with(sort).limit(1), AssetFilterRule.class);
        if (reqUaRule != null&&!rspCodeRule.getDelFlag()&&!rspCodeRule.getId().contains("FILTER")) {
            rspCodeRule.setDelFlag(true);
            rspCodeRule.setOperator("upgrade");
            resourceService.saveAssetFilterRule(rspCodeRule);
        }
       //返回内容
        AssetFilterRule rspContentRule = mongoTemplate.findOne(Query.query(Criteria.where("type").is("RSP_CONTENT").and("configType").is("FILTER").and("resource").regex("HTTP Status 404")).with(sort).limit(1), AssetFilterRule.class);
        if (rspContentRule != null&&!rspContentRule.getDelFlag()&&!rspContentRule.getId().contains("FILTER")) {
            rspContentRule.setDelFlag(true);
            rspContentRule.setOperator("upgrade");
            resourceService.saveAssetFilterRule(rspContentRule);
        }
        log.info("finish AssetFilterRuleUpdate");

    }
}