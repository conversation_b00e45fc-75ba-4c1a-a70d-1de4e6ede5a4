package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import com.quanzhi.auditapiv2.core.service.node.source.watermark.TunnelSourceReadProgress;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class TunnelTaskUpdate implements UpgradeService {
    private final MongoTemplate mongoTemplate;
    private static final Map<String, String> UPDATE_FIELDS = new HashMap<>();

    static {
        UPDATE_FIELDS.put("riskInfo", "lastTime");
        UPDATE_FIELDS.put("riskSample", "createTime");
        UPDATE_FIELDS.put("filterHttpEvent", "createTime");
    }
    @Override
    public int getVersion() {
        return 20241208;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        List<TunnelSourceReadProgress> progresses = mongoTemplate.find(new Query(), TunnelSourceReadProgress.class);
        for (TunnelSourceReadProgress progress : progresses){
            if (StringUtils.isNullOrEmpty(progress.getField())) {
                String field = UPDATE_FIELDS.get(progress.getCollection());
                if (field == null){
                    field = "updateTime";
                }
                progress.setField(field);
                mongoTemplate.save(progress);
            }
        }
    }
}
