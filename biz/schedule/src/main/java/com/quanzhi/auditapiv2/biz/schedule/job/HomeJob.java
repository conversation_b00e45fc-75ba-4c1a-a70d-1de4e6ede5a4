package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.AggRiskService;
import com.quanzhi.auditapiv2.common.dal.dao.home.HomeDao;
import com.quanzhi.auditapiv2.common.dal.dao.impl.common.DataPermissionUtil;
import com.quanzhi.auditapiv2.common.dal.dto.api.ApiStatistics;
import com.quanzhi.auditapiv2.common.dal.dto.api.DataLabelTopDto;
import com.quanzhi.auditapiv2.common.dal.dto.group.DataGroupChangeEvent;
import com.quanzhi.auditapiv2.common.dal.dto.risk.SystemRiskIndexDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.SensitiveInfoDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.StatisticsDto;
import com.quanzhi.auditapiv2.common.dal.dto.weakness.Top10WeaknessDto;
import com.quanzhi.auditapiv2.common.dal.entity.weakness.Top10Weakness;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.ApiWeaknessServiceImpl;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.HttpApiService;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: yangzixian
 * @date: 2022/8/9 10:22
 * @description:
 */
@Component
@Slf4j
public class HomeJob {

    @NacosValue(value = "${schedule.count.num:500000}", autoRefreshed = true)
    private Long scheduleCountNum;
    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    private final HttpApiService httpApiService;
    private final ApiWeaknessServiceImpl apiWeaknessService;
    private final AggRiskService aggRiskService;
    private final HomeDao homeDao;
    private final IHttpAppService httpAppService;
    private final DataPermissionUtil dataPermissionUtil;

    public HomeJob(HttpApiService httpApiService, ApiWeaknessServiceImpl apiWeaknessService, AggRiskService aggRiskService, HomeDao homeDao, IHttpAppService httpAppService, DataPermissionUtil dataPermissionUtil) {
        this.httpApiService = httpApiService;
        this.apiWeaknessService = apiWeaknessService;
        this.aggRiskService = aggRiskService;
        this.homeDao = homeDao;
        this.httpAppService = httpAppService;
        this.dataPermissionUtil = dataPermissionUtil;
    }

    @LockedScheduler(cron = "0 0 * * * ?", executor = "homeJob", name = "概览定时任务", description = "计算概览统计信息、top10弱点信息、首页敏感数据",
            interval = 1000 * 60 * 60 * 24, intervalConditionSpEL = "#root.check()")
    public void execute() throws Exception {
        doJob();
    }

    public boolean check() {
        return httpApiService.getCount(new MetabaseQuery()) > scheduleCountNum
                || httpAppService.getCount(new MetabaseQuery()) > scheduleCountNum
                || apiWeaknessService.getCount(new MetabaseQuery()) > scheduleCountNum
                || aggRiskService.totalCount() > scheduleCountNum;
    }

    @EventListener(DataGroupChangeEvent.class)
    public void onDataGroupChangeEvent(DataGroupChangeEvent dataGroupChangeEvent){
        try {
            doJob();
        } catch (Exception e) {
           log.error("DataGroupChangeEvent error:",e);
        }
    }

    public void doJob() throws Exception {

        Map<String, List<String>> currentDataGroupConditions = dataPermissionUtil.getCurrentDataGroupConditions();

        Map<String, ApiStatistics> apiStatisticsMap = new HashMap<>();
        Map<String, Top10WeaknessDto> top10WeaknessDtoMap = new HashMap<>();
        Map<String, List<DataLabelTopDto>> dataLabelTopDtoListMap = new HashMap<>();

        for (String groupId : currentDataGroupConditions.keySet()) {

            //appUris
            List<String> appUris = currentDataGroupConditions.get(groupId);

            //概览统计信息
            ApiStatistics apiStatistics = httpApiService.getApiStatistics(appUris);
            apiStatistics.setType("apiStatistics");
            apiStatisticsMap.put(groupId, apiStatistics);

            //top10弱点信息统计
            List<Top10Weakness> top10Weakness = apiWeaknessService.getTop10Weakness(appUris);
            Top10WeaknessDto top10WeaknessDto = new Top10WeaknessDto();
            top10WeaknessDto.setTop10Weaknesses(top10Weakness);
            top10WeaknessDto.setType("top10Weakness");
            top10WeaknessDtoMap.put(groupId, top10WeaknessDto);

            //敏感数据识别
            List<DataLabelTopDto> dataLabelTop10List = null;
            if (productType.equals(ProductTypeEnum.ltsk_v2.name())) {
//                dataLabelTop10List = new ArrayList<>(12);
//                //近一天
//                Long startTime = DateUtil.getTimeStampBeforeDays(1);
//                DataLabelTopDto last1DayInterReq = httpApiService.getDataLabelTop10(startTime, "internetApi", "req");
//                last1DayInterReq.setType("dataLabelTop10");
//                last1DayInterReq.setViewType("last1DayInterReq");
//
//                DataLabelTopDto last1DayInterRsp = httpApiService.getDataLabelTop10(startTime, "internetApi", "rsp");
//                last1DayInterRsp.setType("dataLabelTop10");
//                last1DayInterRsp.setViewType("last1DayInterRsp");
//
//                DataLabelTopDto last1DayFulReq = httpApiService.getDataLabelTop10(startTime, "fullApi", "req");
//                last1DayFulReq.setType("dataLabelTop10");
//                last1DayFulReq.setViewType("last1DayFulReq");
//
//                DataLabelTopDto last1DayFulRsp = httpApiService.getDataLabelTop10(startTime, "fullApi", "rsp");
//                last1DayFulRsp.setType("dataLabelTop10");
//                last1DayFulRsp.setViewType("last1DayFulRsp");
//                //近7天
//                startTime = DateUtil.getTimeStampBeforeDays(7);
//                DataLabelTopDto last7DayInterReq = httpApiService.getDataLabelTop10(startTime, "internetApi", "req");
//                last7DayInterReq.setType("dataLabelTop10");
//                last7DayInterReq.setViewType("last7DayInterReq");
//
//                DataLabelTopDto last7DayInterRsp = httpApiService.getDataLabelTop10(startTime, "internetApi", "rsp");
//                last7DayInterRsp.setType("dataLabelTop10");
//                last7DayInterRsp.setViewType("last7DayInterRsp");
//
//                DataLabelTopDto last7DayFulReq = httpApiService.getDataLabelTop10(startTime, "fullApi", "req");
//                last7DayFulReq.setType("dataLabelTop10");
//                last7DayFulReq.setViewType("last7DayFulReq");
//
//                DataLabelTopDto last7DayFulRsp = httpApiService.getDataLabelTop10(startTime, "fullApi", "rsp");
//                last7DayFulRsp.setType("dataLabelTop10");
//                last7DayFulRsp.setViewType("last7DayFulRsp");
//                //近30天
//                startTime = DateUtil.getTimeStampBeforeDays(30);
//                DataLabelTopDto last30DayInterReq = httpApiService.getDataLabelTop10(startTime, "internetApi", "req");
//                last30DayInterReq.setType("dataLabelTop10");
//                last30DayInterReq.setViewType("last30DayInterReq");
//
//                DataLabelTopDto last30DayInterRsp = httpApiService.getDataLabelTop10(startTime, "internetApi", "rsp");
//                last30DayInterRsp.setType("dataLabelTop10");
//                last30DayInterRsp.setViewType("last30DayInterRsp");
//
//                DataLabelTopDto last30DayFulReq = httpApiService.getDataLabelTop10(startTime, "fullApi", "req");
//                last30DayFulReq.setType("dataLabelTop10");
//                last30DayFulReq.setViewType("last30DayFulReq");
//
//                DataLabelTopDto last30DayFulRsp = httpApiService.getDataLabelTop10(startTime, "fullApi", "rsp");
//                last30DayFulRsp.setType("dataLabelTop10");
//                last30DayFulRsp.setViewType("last30DayFulRsp");
//
//                dataLabelTop10List.add(last1DayInterReq);
//                dataLabelTop10List.add(last1DayInterRsp);
//                dataLabelTop10List.add(last1DayFulReq);
//                dataLabelTop10List.add(last1DayFulRsp);
//
//                dataLabelTop10List.add(last7DayInterReq);
//                dataLabelTop10List.add(last7DayInterRsp);
//                dataLabelTop10List.add(last7DayFulReq);
//                dataLabelTop10List.add(last7DayFulRsp);
//
//                dataLabelTop10List.add(last30DayInterReq);
//                dataLabelTop10List.add(last30DayInterRsp);
//                dataLabelTop10List.add(last30DayFulReq);
//                dataLabelTop10List.add(last30DayFulRsp);
            } else {
                dataLabelTop10List = new ArrayList<>(4);
                DataLabelTopDto intReq = httpApiService.getDataLabelTop10("internetApi", "req", appUris);
                intReq.setType("dataLabelTop10");
                intReq.setViewType("intReq");
                DataLabelTopDto intRsp = httpApiService.getDataLabelTop10("internetApi", "rsp", appUris);
                intRsp.setType("dataLabelTop10");
                intRsp.setViewType("intRsp");
                DataLabelTopDto fullReq = httpApiService.getDataLabelTop10("fullApi", "req", appUris);
                fullReq.setType("dataLabelTop10");
                fullReq.setViewType("fullReq");
                DataLabelTopDto fullRsp = httpApiService.getDataLabelTop10("fullApi", "rsp", appUris);
                fullRsp.setType("dataLabelTop10");
                fullRsp.setViewType("fullRsp");
                dataLabelTop10List.add(intReq);
                dataLabelTop10List.add(intRsp);
                dataLabelTop10List.add(fullReq);
                dataLabelTop10List.add(fullRsp);
                dataLabelTopDtoListMap.put(groupId, dataLabelTop10List);
            }
        }

        //入库
        homeDao.upsertMulti(apiStatisticsMap, top10WeaknessDtoMap, dataLabelTopDtoListMap);

    }

}
