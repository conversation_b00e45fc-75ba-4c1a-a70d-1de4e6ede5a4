package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.schdule.domain.repository.ScheduleRepository;
import com.quanzhi.audit.mix.schdule.infrastructure.spring.SpringScheduledTaskRegistrar;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create at 2023/9/07 2:12 下午
 * @description: 定时任务信息升级
 **/
@Component
@Slf4j
public class ScheduleUpdate implements UpgradeService {

    private final SpringScheduledTaskRegistrar scheduledTaskRegistrar;

    private final ScheduleRepository scheduleRepository;

    public ScheduleUpdate(SpringScheduledTaskRegistrar scheduledTaskRegistrar, ScheduleRepository scheduleRepository) {
        this.scheduledTaskRegistrar = scheduledTaskRegistrar;
        this.scheduleRepository = scheduleRepository;
    }

    @Override
    public int getVersion() {
        return 20230931;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        log.info("update schedule,{}", DateUtil.currentDateTime());

        scheduleRepository.removeAll("auditapiv2");
        scheduledTaskRegistrar.afterSingletonsInstantiated();

        log.info("update success,{}", DateUtil.currentDateTime());
    }

}