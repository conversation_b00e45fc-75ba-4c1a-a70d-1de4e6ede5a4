package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysUser;
import com.quanzhi.auditapiv2.common.dal.dto.GWDSSysUserDto;
import com.quanzhi.auditapiv2.common.dal.enums.EnvTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.SM4Util;
import com.quanzhi.auditapiv2.common.util.utils.SSLUtils;
import com.quanzhi.auditapiv2.core.service.SysRoleService;
import com.quanzhi.auditapiv2.core.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.mapstruct.Condition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@ConditionalOnProperty(value = "env.type", havingValue="3")
public class GWDSSysUserScheduleJob  {
    private Logger logger = LoggerFactory.getLogger(GWDSSysUserScheduleJob.class);

    //系统角色
    @Autowired
    private SysRoleService sysRoleService;

    //系统用户
    @Autowired
    private SysUserService sysUserService;

    //请求地址
    @NacosValue(value = "${gwds.user.pull.url:https://esrc.esgcc.com.cn/api/user/getApiUserList}", autoRefreshed = true)
    private String url;

    //用户密码
    @NacosValue(value = "${gwds.user.pull.password:162075d9f13e45f20497b928f3777e623d08060682d7488c}", autoRefreshed = true)
    private String password;

    //环境类型
    @NacosValue(value = "${env.type:1}", autoRefreshed = true)
    private Integer envType;

    /**
     * 执行定时任务
     * @throws Exception
     */
    @LockedScheduler(cron = "0 0/30 * * * ? ", executor = "gwdsSysUserScheduleJob", description = "用户同步")
    public void execute() throws Exception {
        logger.warn("用户同步");
        doJob();
    }

    /**
     * 处理任务
     */
    public void doJob() throws Exception {

        try {
            
            if(EnvTypeEnum.GWDS.type().intValue() != envType.intValue()) {
                
                return;
            }

            CloseableHttpResponse response = null;

            //连接http接口
            CloseableHttpClient httpClient = SSLUtils.createHttpClientTLS();
            HttpGet httpGet = new HttpGet(url);
            //设置header信息
            //指定报文头【Content-type】、【User-Agent】
            httpGet.setHeader("content-type", "application/x-www-form-urlencoded; charset=utf-8");
            httpGet.setHeader("user-agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            httpGet.setHeader("Referer", "https://esrc.esgcc.com.cn");
            //获取响应信息
            response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            String json = EntityUtils.toString(entity);

            logger.warn("用户同步数据：" + json);

            List<GWDSSysUserDto> list = JSON.parseArray(json, GWDSSysUserDto.class);
            for (GWDSSysUserDto gwdsSysUserDto : list) {

                try {
                    if(DataUtil.isNotEmpty(gwdsSysUserDto.getUserName()) && DataUtil.isNotEmpty(gwdsSysUserDto.getUserType())) {

                        SysUser sysUser = new SysUser();
                        //用户名称
                        sysUser.setUsername(SM4Util.decrypt(gwdsSysUserDto.getUserName()));
                        //用户密码
                        sysUser.setPassword(password);
                        //用户类型
                        sysUser.setType(Integer.valueOf(gwdsSysUserDto.getUserType()));

                        sysUserService.saveSysUserByJob(sysUser);
                    }
                } catch (Exception e) {
                    logger.error("用户同步-数据格式错误", e);
                }
            }

        } catch (Exception e) {
            logger.error("用户同步", e);
        }
    }
}
