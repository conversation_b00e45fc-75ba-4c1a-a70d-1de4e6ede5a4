package com.quanzhi.auditapiv2.biz.schedule.task;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.auditapiv2.common.dal.dao.impl.GatewayAgentManagerDaoImpl;
import com.quanzhi.auditapiv2.common.dal.entity.GatewayAgentManagerStatus;
import com.quanzhi.auditapiv2.common.util.enums.GatewayAgentStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 网关探针健康检测任务
 * @date 2023/7/31 10:57
 */

@Component
@Slf4j
public class GatewayAgentHealthCheckTask {

    private static Integer gatewayDefaultPort = 5001;

    @Autowired
    private GatewayAgentManagerDaoImpl gatewayAgentManagerDao;

    public GatewayAgentHealthCheckTask(GatewayAgentManagerDaoImpl gatewayAgentManagerDao) {
        this.gatewayAgentManagerDao = gatewayAgentManagerDao;
    }

    public void checkAgentHealthJob(){
        List<GatewayAgentManagerStatus> all = gatewayAgentManagerDao.getAll();
        List<String> gatewayIpList = gatewayAgentManagerDao.getAllGatewayIp();
        Map<String, Boolean> gatewayAgentAliveCheck = getGatewayAgentAliveCheck(gatewayIpList);
        for (GatewayAgentManagerStatus gatewayAgentManagerStatus : all) {
            //如果安装失败,禁用,安装中,重装中则跳过
            if (gatewayAgentManagerStatus.getStatus() == GatewayAgentStatusEnum.FAILED.getState()
                    || gatewayAgentManagerStatus.getStatus() == GatewayAgentStatusEnum.INSTALLING.getState()
                    || gatewayAgentManagerStatus.getStatus() == GatewayAgentStatusEnum.REINSTALLING.getState()
                    || gatewayAgentManagerStatus.getStatus() == GatewayAgentStatusEnum.DISABLED.getState()
                    || gatewayAgentManagerStatus.getStatus() == GatewayAgentStatusEnum.UNINSTALLING.getState()) {
                continue;
            }

            String gatewayIp = gatewayAgentManagerStatus.getGatewayIp();
            String agentIp = gatewayAgentManagerStatus.getAgentIp();

            if (gatewayAgentAliveCheck.getOrDefault(gatewayIp + "-" + agentIp, false)) {
                gatewayAgentManagerStatus.setStatus(GatewayAgentStatusEnum.RUNNING.getState());
            } else {
                gatewayAgentManagerStatus.setStatus(GatewayAgentStatusEnum.ERROR.getState());
            }
            gatewayAgentManagerDao.update(gatewayAgentManagerStatus);
        }
        try {
            Thread.sleep(60000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private Map<String, Boolean> getGatewayAgentAliveCheck(List<String> gatewayList) {
        Map<String, Boolean> map = new HashMap<>();
        for (String gatewayIp : gatewayList) {
            String url = "http://" + gatewayIp + ":" + gatewayDefaultPort + "/agent/client/status";
            try {
                String result = httpGet(url);
                JSONObject jsonResult = JSONObject.parseObject(result);
                if (jsonResult.getInteger("error_code") == 0) {
                    JSONObject jsonStatus = jsonResult.getJSONObject("status");
                    for (String agentIp : jsonStatus.keySet()) {
                        map.put(gatewayIp + "-" + agentIp, true);
                    }
                } else {
                    log.error("[gateway agent config] failed to getGatewayAgentAliveCheck:{}", gatewayIp);
                }
            } catch (Exception e) {
                log.error("[gateway agent config]  connect failed:{}", url);
            }
        }
        return map;
    }

    private String httpGet(String url) {
        HttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpGet = new HttpGet(url);
        httpGet.addHeader("Content-Type", "application/json");
        HttpResponse httpResponse = null;
        try {
            httpResponse = httpClient.execute(httpGet);
            HttpEntity responseEntity = httpResponse.getEntity();
            String result = IOUtils.toString(responseEntity.getContent());
            //System.out.println("result:" + result);
            return result;
        } catch (IOException e) {
            log.error("failed: Connection refused {}", url);
            //e.printStackTrace();
            return null;
        }
    }
}
