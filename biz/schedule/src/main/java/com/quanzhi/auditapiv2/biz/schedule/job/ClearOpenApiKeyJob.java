//package com.quanzhi.auditapiv2.biz.schedule.job;
//
//import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
//import com.quanzhi.auditapiv2.common.dal.entity.OpenApiKey;
//import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
//import com.quanzhi.auditapiv2.core.service.manager.web.impl.OpenApiKeyServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StopWatch;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * create at 2021/8/12 5:40 下午
// * @description: 威胁Ip表信息统计
// **/
//@Component
//@Slf4j
//public class ClearOpenApiKeyJob {
//
//    private final OpenApiKeyServiceImpl openApiKeyService;
//
//    public ClearOpenApiKeyJob(OpenApiKeyServiceImpl openApiKeyService) {
//        this.openApiKeyService = openApiKeyService;
//    }
//
//    @LockedScheduler(cron = "0 0/2 * * * ?", executor = "clearOpenApiKeyJob", name = "清理过期开放接口密钥", description = "清理过期开放接口密钥"
//    )
//    public void execute() throws Exception {
//        doJob();
//    }
//
//    public void doJob() throws Exception {
//        log.info("start cleaning up expired openAPI keys");
//        StopWatch watch = new StopWatch();
//        watch.start("clearOpenApiKeyJob-task");
//
//        List<OpenApiKey> allOpenApiKey = openApiKeyService.getAllOpenApiKey();
//        for (OpenApiKey openApiKey : allOpenApiKey) {
//            if (DataUtil.isNotEmpty(openApiKey.getDeadline()) && openApiKey.getDeadline() != 0) {
//                Long deadline = openApiKey.getDeadline();
//                if (deadline < System.currentTimeMillis()) {
//                    openApiKeyService.removeOpenApiKey(openApiKey.getId());
//                }
//            }
//        }
//
//        watch.stop();
//        log.info("Cleaning up expired openAPI keys end,completed in {} second", watch.getTotalTimeSeconds());
//    }
//
//}