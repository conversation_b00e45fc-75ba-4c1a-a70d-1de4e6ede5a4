package com.quanzhi.auditapiv2.biz.schedule.task.structure;

import com.quanzhi.auditapiv2.common.dal.entity.UrlStructureLeafInfo;

import java.util.List;

public interface AppStructureProcess {

    /**
     * 当节点下叶子节点过多时的处理
     * @param urlStructureLeafInfo
     */
    @Deprecated
    Object process(UrlStructureLeafInfo urlStructureLeafInfo);

    Object process(List<String> apiUrls);

    /**
     * 对一个应用下多个节点下的叶子节点过多时进行统一的处理
     * @param app
     * @param nodeProcessResults
     */
    void processApp(String app, List<Object> nodeProcessResults);

    String getType();
}
