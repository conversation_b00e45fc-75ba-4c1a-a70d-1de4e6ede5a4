package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.plugin.domain.Plugin;
import com.quanzhi.audit.mix.plugin.service.PluginService;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.api.service.WeaknessPushService;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/27 10:16 上午
 */
@Component
@Slf4j
@ConditionalOnProperty(havingValue = "true", name = "push.enable")
public class WeaknessPushJob {

    private final IApiWeaknessService weaknessService;

    private final WeaknessPushService weaknessPushService;

    private final PluginService pluginService;

    @NacosValue("${push.enable:false}")
    private boolean pushEnable;

    public WeaknessPushJob(IApiWeaknessService weaknessService,
                           WeaknessPushService weaknessPushService,
                           PluginService pluginService) {
        this.weaknessService = weaknessService;
        this.weaknessPushService = weaknessPushService;
        this.pluginService = pluginService;
    }

    @LockedScheduler(cron = "0 0/15 * * * ?", executor = "weaknessPushJob", description = "弱点推送任务")
    public void execute() {
        if (!pushEnable){
            return;
        }
        WeaknessPushService weaknessPushService = this.weaknessPushService;
        List<Plugin> plugins = pluginService.findAll("WEAKNESS_PUSH_SERVICE");
        if (!CollectionUtils.isEmpty(plugins)) {
            weaknessPushService = (WeaknessPushService) plugins.get(0).getPlugin();
        }
        // 每15分钟上传一次弱点
        long start = System.currentTimeMillis() - 15 * 60 * 1000;
        List<ApiWeakness> apiWeaknessList = null;
        try {
            apiWeaknessList = weaknessService.select(start);
            if (CollectionUtils.isEmpty(apiWeaknessList)) {
                return;
            }
            weaknessPushService.push(apiWeaknessList);
            weaknessPushService.onSuccess("ZWX", apiWeaknessList);
        } catch (Throwable e) {
            log.error("push error", e);
            weaknessPushService.onFail("ZWX", apiWeaknessList, e);
            throw e;
        }
    }
}
