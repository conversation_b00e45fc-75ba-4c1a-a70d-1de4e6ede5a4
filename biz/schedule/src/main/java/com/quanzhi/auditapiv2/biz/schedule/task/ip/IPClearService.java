package com.quanzhi.auditapiv2.biz.schedule.task.ip;

import com.mongodb.client.result.DeleteResult;
import com.quanzhi.audit_core.common.model.ApiCleanJobParam;
import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document.IPMonthDocument;
import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document.IpInfoDocument;
import com.quanzhi.metabase.common.utils.DateUtil;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexDefinition;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.IndexResolver;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Iterator;

@Service
@RequiredArgsConstructor
@Slf4j
public class IPClearService {

    public static final String IP_INFO = "ipInfo";
    public static final String IP_INFO_STAT_M = "ipMonthInfo";
    public static final String IP_INFO_STAT_D = "ipDateInfo";
    private final MongoTemplate mongoTemplate;
    private final MongoMappingContext mongoMappingContext;

    public void clear(ApiCleanJobParam param) {
        String formatDate = DateUtil.format(param.getStartTimestamp(), DateUtil.DATE_PATTERN.YYYYMMDD);
        String endDate = DateUtil.format(param.getEndTimestamp(), DateUtil.DATE_PATTERN.YYYYMMDD);
        String today = DateUtil.currentDate();
        // 全部清理，我们使用更快的方式进行清理
        if (today.equals(endDate)) {
            long count = mongoTemplate.count(new Query(), IP_INFO);
            mongoTemplate.dropCollection(IP_INFO);
            mongoTemplate.dropCollection(IP_INFO_STAT_M);
            mongoTemplate.dropCollection(IP_INFO_STAT_D);
            log.info("ipInfo delete all: {}", count);
            // 重新创建索引
            createIndex(IpInfoDocument.class);
            createIndex(IPMonthDocument.class);
        } else {
            Query query = new Query();
            query.addCriteria(Criteria.where("firstDate").gte(formatDate).lte(endDate));
            DeleteResult remove = mongoTemplate.remove(query, "ipInfo");
            log.info("ipInfo delete size: {}", remove.getDeletedCount());
        }
    }

    private void createIndex(Class<?> clz) {
        IndexOperations indexOps = mongoTemplate.indexOps(clz);
        IndexResolver resolver = new MongoPersistentEntityIndexResolver(mongoMappingContext);
        for (IndexDefinition next : resolver.resolveIndexFor(clz)) {
            try {
                indexOps.ensureIndex(next);
            } catch (Exception e) {
                log.error("ensure {} index {} error", clz.getName(), next.getIndexKeys(), e);
            }
        }
    }
}
