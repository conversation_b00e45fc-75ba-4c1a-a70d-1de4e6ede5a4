package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2023/12/11 19:31
 * @description: 删除smartFilterEvent表
 **/
@Component
@Slf4j
public class SmartFilterEventUpdate implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    public SmartFilterEventUpdate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }


    @Override
    public int getVersion() {
        return 20240530;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        mongoTemplate.dropCollection("smartFilterEvent");
        log.info("smartFilterEvent drop finish");
    }
}