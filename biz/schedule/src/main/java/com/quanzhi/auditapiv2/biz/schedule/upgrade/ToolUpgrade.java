package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.common.util.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.io.File;

import static com.quanzhi.auditapiv2.core.service.facade.impl.ToolFacadeImpl.TOOL_PATH;

/**
 * <AUTHOR>
 * @class ToolUpgrade
 * @created 2023/12/4 14:46
 * @desc
 **/
@Slf4j
@Component
public class ToolUpgrade implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    public ToolUpgrade(MongoTemplate mongoTemplate) {this.mongoTemplate = mongoTemplate;}

    @Override
    public int getVersion() {
        return 20231204;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        removeToolFile();
        removeToolResult();
    }

    private void removeToolResult() {
        mongoTemplate.remove(Query.query(Criteria.where("name").is("POC汇报模板数据统计")),"scanTool");
    }

    private void removeToolFile() {
        String fileName = TOOL_PATH + File.separator + "POC汇报模板数据统计.zip";
        try {
            FileUtils.deleteFile(fileName);
        } catch (Exception e) {
            log.error("remove file {} error", fileName, e);
        }
    }
}
