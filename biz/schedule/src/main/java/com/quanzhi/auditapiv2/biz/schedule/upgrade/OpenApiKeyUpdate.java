package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.dal.entity.OpenApiKey;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class OpenApiKeyUpdate implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    public OpenApiKeyUpdate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public int getVersion() {
        return 20240904;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        log.info("update OpenApiKey,{}", DateUtil.currentDateTime());

        //查找所有openApiKey
        List<OpenApiKey> openApiKey = mongoTemplate.find(new Query(), OpenApiKey.class, "openApiKey");
        for (OpenApiKey apiKey : openApiKey) {
            if (DataUtil.isEmpty(apiKey.getUseKey())) {
                Update update = new Update();
                update.set("useKey", apiKey.getId());
                mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(apiKey.getId())), update, "openApiKey");
            }
        }

        log.info("update success");
    }

}