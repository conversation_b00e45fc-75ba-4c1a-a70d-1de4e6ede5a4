package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.auditapiv2.common.dal.entity.DataLabelClassify;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @class DataLabelUpgradeV330
 * @created 2025/2/28 16:06
 * @desc 数据标签和模板升级
 * @since 3.3.0
 **/
@Slf4j
@Component
public class DataLabelUpgradeV330 implements UpgradeService {

    @NacosInjected
    private ConfigService configService;

    @Override
    public int getVersion() {
        return 0;
    }

    @Override
    public long getVersionV2() {
        return 33020250321L;
    }

    @Override
    public void upgrade() throws Exception {
        String dataLabelJsonString = configService.getConfig("common.datalabel.json", "common", 5000);
        String dataLabelClassifyJsonString = configService.getConfig("common.dataLabelClassify.json", "common", 5000);
        List<DataLabelClassify> dataLabelClassifies = JSON.parseArray(dataLabelClassifyJsonString, DataLabelClassify.class);
        List<DataLabel> dataLabels = JSON.parseArray(dataLabelJsonString, DataLabel.class);
        Map<String, DataLabel> labelMap = new HashMap<>();
        for (DataLabel dataLabel : dataLabels) {
            labelMap.put(dataLabel.getId(), dataLabel);
            if (StringUtils.isNotEmpty(dataLabel.getMatcher())) {
                labelMap.put(dataLabel.getMatcher(), dataLabel);
            }
        }

        for (DataLabelClassify dataLabelClassify : dataLabelClassifies) {
            generateClassifyId(dataLabelClassify, labelMap, dataLabelClassify.isEnabled());
        }

        Map<String, DataLabel> result = new HashMap<>();
        for (DataLabel value : labelMap.values()) {
            result.put(value.getId(), value);
        }
        configService.publishConfig("common.dataLabelClassify.json", "common", JSON.toJSONString(dataLabelClassifies));
        configService.publishConfig("common.datalabel.json", "common", JSON.toJSONString(new ArrayList<>(result.values())));
    }

    public void generateClassifyId(
            DataLabelClassify dataLabelClassify,
            Map<String, DataLabel> labelMap,
            boolean enable
    ) {
        Map<String, List<String>> parentChildMap = new HashMap<>();
        List<DataLabelClassify.Classify> data = dataLabelClassify.getData();
        for (DataLabelClassify.Classify classify : data) {
            generateClassifyId(classify, parentChildMap, "ROOT", "", labelMap, enable);
        }

    }

    /**
     * 递归生成子节点 ID
     *
     * @param classify
     * @param parentChildMap
     * @param parent
     * @param parentId
     */
    public void generateClassifyId(
            DataLabelClassify.Classify classify,
            Map<String, List<String>> parentChildMap,
            String parent,
            String parentId,
            Map<String, DataLabel> labelMap,
            boolean enable
    ) {
        List<String> childList = parentChildMap.get(parentId);
        if (childList == null) {
            childList = new ArrayList<>();
        }
        boolean fd = false;
        for (int i = 0; i < childList.size(); i++) {
            if (classify.getClassify().equals(childList.get(i))) {
                classify.setClassifyId(generateClassifyId(parentId, i));
                classify.setLevel(i + 1);
                fd = true;
                break;
            }
        }
        if (!fd) {
            childList.add(classify.getClassify());
            classify.setClassifyId(generateClassifyId(parentId, childList.size()));
            classify.setLevel(childList.size());
        }
        parentChildMap.put(parentId, childList);

        if (CollectionUtils.isNotEmpty(classify.getChildren())) {
            List<DataLabelClassify.Classify> children = classify.getChildren();
            for (DataLabelClassify.Classify child : children) {
                generateClassifyId(child, parentChildMap, classify.getClassify(), classify.getClassifyId(), labelMap, enable);
            }
        } else {
            if (enable) {
                List<DataLabelClassify.ClassifyCell> data = classify.getData();
                for (DataLabelClassify.ClassifyCell cell : data) {
                    for (String labelId : cell.getLabelIds()) {
                        DataLabel dataLabel = labelMap.get(labelId);
                        if (dataLabel != null) {
                            dataLabel.setFirstClass(classify.getClassifyId() + " " + classify.getClassify());
                        }
                    }
                }
            }
        }
    }

    public String generateClassifyId(String parent, int index) {
        if ("".equals(parent)) {
            return "" + index;
        }
        return parent + "-" + index;
    }
}
