package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit.mix.schdule.domain.entity.TriggerStatus;
import com.quanzhi.audit.mix.schdule.domain.utils.ParameterHelper;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.audit_core.common.utils.StringUtils;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.RiskV2Service;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysUser;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.AggRiskOperatorDto;
import com.quanzhi.auditapiv2.common.dal.entity.Notify;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.core.model.event.RiskChangedEvent;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.repository.aggRisk.AggRiskDao;
import com.quanzhi.auditapiv2.core.service.NacosDataServiceBuilder;
import com.quanzhi.auditapiv2.core.service.SysUserService;
import com.quanzhi.auditapiv2.core.service.manager.web.INotifyService;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceChangeEventHandleService;
import com.quanzhi.metabase.core.model.ResourceEntity;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.json.Converter;
import org.bson.json.JsonWriterSettings;
import org.bson.json.StrictJsonWriter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yangzixian
 * @date: 2022/8/9 10:22
 * @description:
 */
@Component
@Slf4j
public class AggRiskInfoJob {

    private final RiskV2Service riskV2Service;

    private final AggRiskDao aggRiskDao;

    private final ApplicationEventPublisher eventPublisher;

    private final IResourceChangeEventHandleService resourceChangeEventHandleService;

    private final SysUserService sysUserServiceImpl;

    private final INotifyService notifyServiceImpl;

    private final MongoTemplate mongoTemplate;

    private final NacosDataServiceBuilder nacosDataServiceBuilder;

    public AggRiskInfoJob(RiskV2Service riskV2Service, AggRiskDao aggRiskDao, ApplicationEventPublisher eventPublisher, IResourceChangeEventHandleService resourceChangeEventHandleService, SysUserService sysUserServiceImpl, INotifyService notifyServiceImpl, MongoTemplate mongoTemplate, NacosDataServiceBuilder nacosDataServiceBuilder) {
        this.riskV2Service = riskV2Service;
        this.aggRiskDao = aggRiskDao;
        this.eventPublisher = eventPublisher;
        this.resourceChangeEventHandleService = resourceChangeEventHandleService;
        this.sysUserServiceImpl = sysUserServiceImpl;
        this.notifyServiceImpl = notifyServiceImpl;
        this.mongoTemplate = mongoTemplate;
        this.nacosDataServiceBuilder = nacosDataServiceBuilder;
    }

    @LockedScheduler(cron = "0 */20 * * * ?", executor = "aggRiskInfoJob", name = "风险定时任务", description = "计算风险信息", triggerStatus = TriggerStatus.OPEN, version = 2)
    public void execute() throws Exception {
        doJob();
    }

    public void doJob() throws Exception {

        String param = ParameterHelper.get();
        Long count = aggRiskDao.totalCount(new AggRiskOperatorDto());
        Query query = new Query();
        if (count > 0 && !"all".equals(param)) {
            // 获取今天的日期
            LocalDate today = LocalDate.now();
            // 减去2天
            LocalDate twoDaysAgo = today.minusDays(2);
            // 创建ZonedDateTime对象，使用系统默认时区
            ZonedDateTime zonedTwoDaysAgo = twoDaysAgo.atStartOfDay(ZoneId.systemDefault());
            // 获取时间戳
            long timeStamp = zonedTwoDaysAgo.toInstant().toEpochMilli();

            query.addCriteria(Criteria.where("updateTime").gte(timeStamp));
        }

        Map<String, DataLabel> dataLabelMap = nacosDataServiceBuilder.getDataLable(true).build().getDataLabelMap();

        //获取异常
        try (MongoCursor<Document> cursor =
                     mongoTemplate.getCollection("riskInfo").
                             find(query.getQueryObject()).sort(query.getSortObject()).noCursorTimeout(true).batchSize(1000).cursor()) {
            JsonWriterSettings settings = JsonWriterSettings.builder().int64Converter(new Converter<Long>() {
                public void convert(Long value, StrictJsonWriter writer) {
                    writer.writeNumber(value.toString());
                }
            }).build();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            while (cursor.hasNext()) {
                RiskInfo riskInfoV2 = JSONObject.parseObject(cursor.next().toJson(settings), RiskInfo.class);
                //计算风险id
                String aggRiskId = riskInfoV2.getAggRiskId();
                if (DataUtil.isEmpty(aggRiskId)) {
                    aggRiskId = generatorRiskId(riskInfoV2);
                    if (StringUtils.isEmpty(aggRiskId)) {
                        continue;
                    }
                    riskInfoV2.setAggRiskId(aggRiskId);
                    //没有这个字段的风险填充一下
                    riskV2Service.updateAggRiskId(riskInfoV2.getId(), riskInfoV2.getAggRiskId());
                }
                AggRiskInfo aggRiskInfo = aggRiskDao.findById(aggRiskId);
                if (DataUtil.isNotEmpty(aggRiskInfo)) {
                    //日期
                    String aggDate = aggRiskInfo.getDate();
                    String riskDate = riskInfoV2.getDate();
                    Date date1 = sdf.parse(aggDate);
                    Date date2 = sdf.parse(riskDate);
                    if (date1.after(date2)) {
                        aggRiskInfo.setDate(riskDate);
                        aggRiskInfo.setOperationId(riskInfoV2.getOperationId().replaceAll("R", "E"));
                    }
                    //等级
                    Integer aggLevel = aggRiskInfo.getLevel();
                    Integer riskLevel = riskInfoV2.getLevel();
                    if (aggLevel < riskLevel) {
                        aggRiskInfo.setLevel(riskLevel);
                        aggRiskInfo.setLevelName(AggRiskInfo.RiskLevelEnum.getRiskLevelEnum(riskLevel).getName());
                    }
                    if (riskInfoV2.getFirstTime() < aggRiskInfo.getFirstTime()) {
                        aggRiskInfo.setFirstTime(riskInfoV2.getFirstTime());
                    }
                    if (riskInfoV2.getLastTime() > aggRiskInfo.getLastTime()) {
                        aggRiskInfo.setLastTime(riskInfoV2.getLastTime());
                    }
                    //描述
                    String startDate = sdf.format(new Date(aggRiskInfo.getFirstTime()));
                    String endDate = sdf.format(new Date(aggRiskInfo.getLastTime()));
                    Long riskNum = riskV2Service.totalCount(aggRiskId);
                    aggRiskInfo.setRiskNum(riskNum);
                    String desc = getDesc(riskInfoV2.getEntities(), startDate + "至" + endDate, riskNum);
                    aggRiskInfo.setDesc(desc);
                    //状态
                    if (riskInfoV2.getState() > aggRiskInfo.getState()) {
                        aggRiskInfo.setState(riskInfoV2.getState());
                        aggRiskInfo.setStateName(AggRiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoV2.getState()).getName());
                    }
                    //多节点
                    if (riskInfoV2.getNodes() != null && !riskInfoV2.getNodes().isEmpty()) {
                        ResourceEntity.Node riskNode = riskInfoV2.getNodes().get(0);
                        boolean flag = true;
                        for (ResourceEntity.Node node : aggRiskInfo.getNodes()) {
                            if (node.getNid().equals(riskNode.getNid())) {
                                flag = false;
                                break;
                            }
                        }
                        if (flag) {
                            if (aggRiskInfo.getNodes() == null) {
                                aggRiskInfo.setNodes(new ArrayList<>());
                            }
                            aggRiskInfo.getNodes().add(riskNode);
                        }
                    }
                    aggRiskInfo.setDepartments(riskInfoV2.getDepartments());
                    if (riskInfoV2.getEntities().size() > 1
                            || riskInfoV2.getEntities().get(0).getType().equals("API")
                            || riskInfoV2.getEntities().get(0).getType().equals("APP")) {
                        aggRiskInfo.setAppName(riskInfoV2.getAppName());
                    }
                    //更新
                    aggRiskDao.upsertAggRiskInfo(aggRiskInfo);
                } else {
                    aggRiskInfo = new AggRiskInfo();
                    if (riskInfoV2.getEntities().size() > 1
                            || riskInfoV2.getEntities().get(0).getType().equals("API")
                            || riskInfoV2.getEntities().get(0).getType().equals("APP")) {
                        aggRiskInfo.setApiUri(riskInfoV2.getApiUri());
                        aggRiskInfo.setApiUrl(riskInfoV2.getApiUrl());
                        aggRiskInfo.setAppUri(riskInfoV2.getAppUri());
                        aggRiskInfo.setAppName(riskInfoV2.getAppName());
                        aggRiskInfo.setHost(riskInfoV2.getHost());
                    }
                    aggRiskInfo.setIp(riskInfoV2.getIp());
                    aggRiskInfo.setAccount(riskInfoV2.getAccount());
                    aggRiskInfo.setDepartments(riskInfoV2.getDepartments());
                    if (riskInfoV2.getPolicySnapshot() != null) {
                        aggRiskInfo.setEntityType(riskInfoV2.getPolicySnapshot().getType());
                        aggRiskInfo.setPolicyId(riskInfoV2.getPolicySnapshot().getId());
                    } else {
                        continue;
                    }
                    aggRiskInfo.setDate(riskInfoV2.getDate());
                    aggRiskInfo.setPolicySnapshot(riskInfoV2.getPolicySnapshot());
                    aggRiskInfo.setLevel(riskInfoV2.getLevel());
                    aggRiskInfo.setSuggest(riskInfoV2.getSuggest());
                    aggRiskInfo.setLevelName(AggRiskInfo.RiskLevelEnum.getRiskLevelEnum(riskInfoV2.getLevel()).getName());
                    aggRiskInfo.setFirstTime(riskInfoV2.getFirstTime());
                    aggRiskInfo.setLastTime(riskInfoV2.getLastTime());
                    aggRiskInfo.setName(riskInfoV2.getPolicySnapshot().getName());
                    aggRiskInfo.setState(riskInfoV2.getState());
                    aggRiskInfo.setStateName(AggRiskInfo.RiskStateEnum.getRiskStateEnum(riskInfoV2.getState()).getName());
                    aggRiskInfo.getEntities().addAll(riskInfoV2.getEntities());
                    aggRiskInfo.setOperationId(riskInfoV2.getOperationId().replaceAll("R", "E"));
                    String startDate = sdf.format(new Date(aggRiskInfo.getFirstTime()));
                    String endDate = sdf.format(new Date(aggRiskInfo.getLastTime()));
                    String desc = getDesc(riskInfoV2.getEntities(), startDate + "至" + endDate, 1L);
                    aggRiskInfo.setDesc(desc);
                    aggRiskInfo.setType(riskInfoV2.getPolicySnapshot().getGroup());
                    aggRiskInfo.setId(UUID.randomUUID().toString().replaceAll("-", ""));
                    aggRiskInfo.setRiskNum(1L);
                    aggRiskInfo.setNodes(riskInfoV2.getNodes());
                    aggRiskInfo.setId(aggRiskId);
                    aggRiskDao.upsertAggRiskInfo(aggRiskInfo);
                    //推送
                    resourceChangeEventHandleService.handle(aggRiskInfo);
                    //系统通知
                    Notify notify = getNotify(aggRiskInfo, dataLabelMap);
                    SysUser sysUser = sysUserServiceImpl.getByUserName("webadmin");
                    notifyServiceImpl.sendServiceNotify(notify, sysUser.getId());
                }
                eventPublisher.publishEvent(new RiskChangedEvent(aggRiskInfo));
            }
        }
    }

    public String getDesc(List<RiskInfo.Entity> entities, String date, Long count) {
        if (DataUtil.isEmpty(entities) || DataUtil.isEmpty(date) || DataUtil.isEmpty(count)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        List<com.quanzhi.audit_core.common.risk.RiskInfo.Entity> noApi_App = entities.stream().filter(entity -> !entity.getType().equals("API") && !entity.getType().equals("APP")).collect(Collectors.toList());
        for (RiskInfo.Entity entity : noApi_App) {
            sb.append(entity.getName()).append("<h>").append(entity.getValue()).append("</h>，");
        }
        List<com.quanzhi.audit_core.common.risk.RiskInfo.Entity> api_App = entities.stream().filter(entity -> entity.getType().equals("API") || entity.getType().equals("APP")).collect(Collectors.toList());
        for (RiskInfo.Entity entity : api_App) {
            sb.append("在").append(entity.getName()).append("<h>").append(entity.getValue().replaceAll("httpapp:", "").replaceAll("httpapi:", "")).append("</h>上，");
        }
        sb.append("于<h>").append(date).append("</h>期间共触发异常<h>").append(count).append("</h>次。");
        return sb.toString();
    }

    public String generatorRiskId(RiskInfo riskInfo) {
        // 提取主体
        if (com.quanzhi.re.core.utils.DataUtil.isEmpty(riskInfo)) {
            return "";
        }
        List<RiskInfo.Entity> entities = riskInfo.getEntities();
        StringBuilder aggRiskIdBuilder = new StringBuilder();
        // 主体聚合
        for (RiskInfo.Entity entity : entities) {
            aggRiskIdBuilder.append(entity.getType()).append(entity.getValue());
        }
        // 策略聚合
        if (riskInfo.getPolicySnapshot() != null) {
            aggRiskIdBuilder.append(riskInfo.getPolicySnapshot().getId());
        } else {
            return "";
        }
        return StringUtils.md5(aggRiskIdBuilder.toString());
    }

    public Notify getNotify(AggRiskInfo aggRiskInfo, Map<String, DataLabel> dataLabelMap) {
        List<RiskInfo.Entity> entities = aggRiskInfo.getEntities();
        if (CollectionUtils.isEmpty(entities)) {
            throw new IllegalStateException("riskInfo.getEntities is empty.");
        }
        List<String> ev = new ArrayList<>();
        for (com.quanzhi.audit_core.common.risk.RiskInfo.Entity entity : entities) {
            String replace = entity.getValue().replace("httpapi:", "").replace("httpapp:", "");
            if (dataLabelMap.containsKey(replace)) {
                ev.add(dataLabelMap.get(replace).getName());
            } else {
                ev.add(replace);
            }
            ev.add(replace);
        }
        String entity = String.join(",", ev);
        if (StringUtils.isEmpty(entity)) {
            throw new IllegalStateException("riskInfo entity is empty.");
        }
        String riskName = aggRiskInfo.getPolicySnapshot().getName();
        if (StringUtils.isEmpty(riskName)) {
            throw new IllegalStateException("riskInfo riskName is empty.");
        }
        String title = "风险主体-" + entity + "触发了" + riskName + "风险";
        Notify notify = new Notify();
        //标题
        notify.setTitle(title);
        //内容
        notify.setContent(title);
        //唯一编码
        notify.setCode(aggRiskInfo.getPolicySnapshot().getId());
        //触发时间
        notify.setTriggerTime(aggRiskInfo.getFirstTime());
        return notify;
    }

}
