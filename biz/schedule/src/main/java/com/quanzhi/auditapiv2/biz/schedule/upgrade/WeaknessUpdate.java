package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2022/8/10 2:12 下午
 * @description: 弱点规则的一些字段升级
 **/
@Component
@Slf4j
@Order(2)
public class WeaknessUpdate implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    public WeaknessUpdate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public int getVersion() {
        return 20231130;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        log.info("update weakness,{}", DateUtil.currentDateTime());

        String json = "[{\"code\":\"roleinfoupdatable\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象属性级别鉴权\",\"description\":\"接口请求中存在角色权限字段，用户可以修改该字段来更新自身角色权限，从而导致越权。\",\"updateTime\":*************,\"type\":\"broken_object_property_level_authorization\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"接口应当通过用户凭证做角色判断，而不是请求中包含的角色权限字段。\",\"enable\":true,\"name\":\"权限更新不合理\",\"definition\":\"\",\"id\":\"roleinfoupdatable\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"RoleInfoUpdatable\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"权限更新不合理\",\"pluginName\":\"权限更新不合理\"}],\"group\":\"\"},{\"code\":\"sensitiveinjwt\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OTHER\",\"utilizedWay\":\"\",\"typeName\":\"Web安全缺陷\",\"description\":\"JWT中存储了用户个人敏感信息，若JWT泄露则攻击者可轻易获取JWT中的敏感数据。\",\"updateTime\":*************,\"type\":\"other\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"JWT应当仅作为权限校验字段进行使用，避免存储用户个人敏感数据，降低数据泄露风险。\",\"enable\":true,\"name\":\"JWT存在敏感数据\",\"definition\":\"\",\"id\":\"sensitiveinjwt\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"SensitiveInJwt\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"JWT存在敏感数据\",\"pluginName\":\"JWT存在敏感数据\"}],\"group\":\"\"},{\"code\":\"dboperatorapi\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OTHER\",\"utilizedWay\":\"\",\"typeName\":\"Web安全缺陷\",\"description\":\"在接口的请求参数中发现可以执行的数据库查询语句，攻击者可以通过修改参数，绕过系统的限制查询系统中的重要数据，甚至执行恶意语句，造成系统数据被破坏。\",\"updateTime\":*************,\"type\":\"other\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"根据业务需求进行改造，限制接口不得直接传递数据库查询语句参数，根据业务需求只开放部分的参数，最终的查询语句应由后端控制，同时要注意对参数中非法字符做过滤。\",\"enable\":true,\"name\":\"数据库查询\",\"definition\":\"\",\"id\":\"dboperatorapi\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"DbOperatorApi\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"数据库查询\",\"pluginName\":\"数据库查询\"}],\"group\":\"\"},{\"code\":\"jwtnonealgorithm\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"JWT使用了无签名算法，当alg字段为空时，后端将不执行签名验证，攻击者可构造出JWT凭证进行身份信息伪造。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"Web服务端需要禁用无签名算法，避免攻击者恶意构造JWT凭证；对于仍支持无签名算法的应用接口，需要对接口访问进行监控，及时发现接口异常请求的风险。\",\"enable\":true,\"name\":\"JWT无签名算法\",\"definition\":\"\",\"id\":\"jwtnonealgorithm\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"JwtNoneAlgorithm\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"JWT无签名算法\",\"pluginName\":\"JWT无签名算法\"}],\"group\":\"\"},{\"code\":\"sql_injection\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OTHER\",\"utilizedWay\":\"\",\"typeName\":\"Web安全缺陷\",\"description\":\"接口存在SQL注入漏洞，攻击者可能利用SQL注入漏洞获取数据库内的数据。\",\"updateTime\":*************,\"type\":\"other\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"添加对参数中非法字符做过滤，或使用预编译等方式限制参数直接拼接到sql语句中。\",\"enable\":true,\"name\":\"SQL注入\",\"definition\":\"\",\"id\":\"sql_injection\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"SqlInjectionWeakness\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"数据库查询\",\"pluginName\":\"数据库查询\"}],\"group\":\"\"},{\"code\":\"ossdomaintakeover\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"安全配置错误\",\"description\":\"云存储桶已删除，但对应的DNS CNAME记录未被删除，自定义域名与云存储桶域名间仍存在映射关系，此时攻击者可注册同名云存储桶进行域名劫持。\",\"updateTime\":*************,\"type\":\"security_misconfiguration\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"删除云存储桶资源时，需及时删除DNS相关记录，避免出现域名劫持；对于未及时处理的资产，需要进行访问监控，及时发现潜在安全风险。\",\"enable\":true,\"name\":\"存储桶域名劫持\",\"definition\":\"\",\"id\":\"ossdomaintakeover\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"OssDomainTakeover\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"存储桶域名劫持\",\"pluginName\":\"存储桶域名劫持\"}],\"group\":\"\"},{\"code\":\"fakeantidata\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象属性级别鉴权\",\"description\":\"在接口事件的返回中发现，对同一个敏感数据存在已脱敏和未脱敏数据同时返回给前端的行为,可以通过查看接口详细的请求和返回找出未脱敏的原始敏感数据，未真正达到数据脱敏的目的。\",\"updateTime\":*************,\"type\":\"broken_object_property_level_authorization\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"对于需要脱敏的数据，需要确定在后端返回的数据即已经完成脱敏，不能只在前端做脱敏处理；对于未及时改造的接口，需要对接口访问情况进行监控，关注数据暴露情况，及时发现异常获取数据的风险。\",\"enable\":true,\"name\":\"脱敏策略不一致\",\"definition\":\"\",\"id\":\"fakeantidata\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"FakeAntiData\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"脱敏策略不一致\",\"pluginName\":\"脱敏策略不一致\"}],\"group\":\"\"},{\"code\":\"jwtweaksecret\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"JWT凭证秘钥太简单或没有使用秘钥，攻击者可以通过构造JWT凭证拿到账号的权限。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"Web服务端需要使用强密钥作为JWT密钥，避免被攻击者构造出JWT凭证；对于未及时修改JWT密钥的应用接口，需要对接口访问进行监控，及时发现接口异常请求的风险。\",\"enable\":true,\"name\":\"JWT弱秘钥\",\"definition\":\"\",\"id\":\"jwtweaksecret\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"JwtWeakSecret\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"JWT弱秘钥\",\"pluginName\":\"JWT弱秘钥\"}],\"group\":\"\"},{\"code\":\"weaknessservice\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"资产管理失当\",\"description\":\"gitlab,nexus等开发环境的应用在公网可访问，可能导致代码泄漏或重要的信息泄漏。\",\"updateTime\":*************,\"type\":\"improper_inventory_management\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"开发相关的服务需要限制在测试、开发环境中访问。\",\"enable\":true,\"name\":\"脆弱应用在公网暴露\",\"definition\":\"\",\"id\":\"weaknessservice\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"WeaknessService\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"脆弱应用在公网暴露\",\"pluginName\":\"脆弱应用在公网暴露\"}],\"group\":\"\"},{\"code\":\"alldataexposure\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象属性级别鉴权\",\"description\":\"接口可以直接访问，不用构造参数，可一次性获取大量敏感数据。\",\"updateTime\":*************,\"type\":\"broken_object_property_level_authorization\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"在接口设计时，可以根据业务需求对部分敏感数据进行脱敏，并限制接口单次返回的数据量；对于未及时改造的接口，需要对接口访问情况进行监控，关注数据暴露情况，及时发现异常获取数据的风险。\",\"enable\":true,\"name\":\"单次返回所有数据\",\"definition\":\"\",\"id\":\"alldataexposure\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"AllDataExposure\",\"paramFields\":{\"totalLabelCountLimit\":{\"allowEmpty\":false,\"defaultVal\":50,\"label\":\"总数据量阈值\",\"type\":\"NUMBER\",\"desc\":\"返回数据量阈值大小\"},\"singleLabelCountLimit\":{\"allowEmpty\":false,\"defaultVal\":21,\"label\":\"单一标签数据量阈值\",\"type\":\"NUMBER\",\"desc\":\"返回数据量阈值大小\"},\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"imei\",\"email\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"}},\"params\":\"{\\\"totalLabelCountLimit\\\":50,\\\"singleLabelCountLimit\\\":21,\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"imei\\\",\\\"email\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"单次返回所有数据\",\"pluginName\":\"单次返回所有数据\"}],\"group\":\"\"},{\"code\":\"weakpassword\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"在登录接口的请求中存在弱密码，弱密码可能通过账号暴力破解的方式被破解。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"需要对用户的密码做规则限制，避免使用易于被他人猜测出的密码，对于已经存在的弱密码，需要及时进行修改。对于未及时修改的密码，需要对登录接口访问进行监控，及时发现帐号被暴力破解登录或帐号登录异常的风险。\",\"enable\":true,\"name\":\"登录弱密码\",\"definition\":\"\",\"id\":\"weakpassword\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"WeakPassword\",\"paramFields\":{\"userDefinedWeakPasswordSet\":{\"allowEmpty\":true,\"defaultVal\":[],\"label\":\"自定义弱密码\",\"type\":\"STRING_ARRAY\",\"desc\":\"自定义弱密码\"},\"loginErrorKeywords\":{\"allowEmpty\":true,\"defaultVal\":[\"验证码错误\",\"密码错误\",\"密码不对\",\"重试\",\"不存在\",\"不正确\",\"失败\",\"失效\",\"超时\",\"有误\",\"password error\",\"not exist\",\"not found\",\"404\",\"Error Page\",\"Invalid user\",\"没有权限\"],\"label\":\"登录失败关键字\",\"type\":\"STRING_ARRAY\",\"desc\":\"如果返回信息中存在关键字，则被判断为登录失败\"},\"userDefinedWeakPasswordPrefixList\":{\"allowEmpty\":true,\"defaultVal\":[],\"label\":\"自定义弱密码前缀\",\"type\":\"STRING_ARRAY\",\"desc\":\"自定义弱密码前缀，如果出现前缀+弱密码，也会被判断为弱密码\"},\"userDefinedWeakPasswordSuffixList\":{\"allowEmpty\":true,\"defaultVal\":[],\"label\":\"自定义弱密码后缀\",\"type\":\"STRING_ARRAY\",\"desc\":\"自定义弱密码后缀，如果出现弱密码+后缀，也会被判断为弱密码\"}},\"params\":\"{\\\"userDefinedWeakPasswordSet\\\":[],\\\"loginErrorKeywords\\\":[\\\"验证码错误\\\",\\\"密码错误\\\",\\\"密码不对\\\",\\\"重试\\\",\\\"不存在\\\",\\\"不正确\\\",\\\"失败\\\",\\\"失效\\\",\\\"超时\\\",\\\"有误\\\",\\\"password error\\\",\\\"not exist\\\",\\\"not found\\\",\\\"404\\\",\\\"Error Page\\\",\\\"Invalid user\\\",\\\"没有权限\\\"],\\\"userDefinedWeakPasswordPrefixList\\\":[],\\\"userDefinedWeakPasswordSuffixList\\\":[]}\",\"passwordTag\":\"\",\"pluginDesc\":\"登录弱密码\",\"pluginName\":\"登录弱密码\"}],\"group\":\"\"},{\"code\":\"authkeyinreqparam\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的功能级别鉴权\",\"description\":\"请求参数中存在权限判断相关字段，攻击者可能通过修改权限字段值来提升自己的权限。\",\"updateTime\":*************,\"type\":\"bfla\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"建议在系统设计时，避免将权限信息暴露在请求参数中。\",\"enable\":true,\"name\":\"请求权限参数可控\",\"definition\":\"\",\"id\":\"authkeyinreqparam\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"AuthKeyInReqParam\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"请求权限参数可控\",\"pluginName\":\"请求权限参数可控\"}],\"group\":\"\"},{\"code\":\"sensitiveinstaticfile\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"资产管理失当\",\"description\":\"攻击者可以直接访问接口下载带有敏感信息的文件，从而获取敏感数据。\",\"updateTime\":*************,\"type\":\"improper_inventory_management\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"涉敏文件下载接口需要校验访问者的身份，避免未通过身份校验者访问这类接口；对于未及时改造的接口，需要对接口访问进行监控，及时发现通过该接口异常下载文件的风险。\",\"enable\":true,\"name\":\"敏感文件泄露\",\"definition\":\"\",\"id\":\"sensitiveinstaticfile\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"SensitiveInStaticFile\",\"paramFields\":{\"valueCountLimit\":{\"allowEmpty\":false,\"defaultVal\":10,\"label\":\"数据量\",\"type\":\"NUMBER\",\"desc\":\"总敏感数据量超过这个值时，触发弱点\"},\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"}},\"params\":\"{\\\"valueCountLimit\\\":10,\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"静态文件中包含有敏感信息\",\"pluginName\":\"敏感文件泄露\"}],\"group\":\"\"},{\"code\":\"debuginfomationleak\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"安全配置错误\",\"description\":\"在接口的返回数据中发现调试信息。调试信息可能泄露应用内部的敏感状态或配置详情，从而导致应用或平台数据存在泄漏风险。\",\"updateTime\":*************,\"type\":\"security_misconfiguration\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"根据业务需求进行改造，限制接口不得暴露调试信息。\",\"enable\":true,\"name\":\"调试信息泄露\",\"definition\":\"\",\"id\":\"debuginfomationleak\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"DebugInfomationLeak\",\"paramFields\":{\"debugKeywords\":{\"allowEmpty\":true,\"defaultVal\":[],\"label\":\"敏感调试信息关键字\",\"type\":\"STRING_ARRAY\",\"desc\":\"检查响应内容中不应该出现的敏感调试信息关键字\"}},\"params\":\"{\\\"debugKeywords\\\":[]}\",\"passwordTag\":\"\",\"pluginDesc\":\"调试信息泄露\",\"pluginName\":\"调试信息泄露\"}],\"group\":\"\"},{\"code\":\"jwtexptoolarge\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"JWT过期时间设置越长意味着有效JWT的时间就越长，相应的攻击成功率也会越高，如攻击者利用本应该失效的JWT进行会话劫持攻击。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"为保证系统的安全性，建议将JWT的过期时间设置为较短的时间，避免因JWT过期时间设置过长，而增加攻击者利用JWT的机会。\",\"enable\":true,\"name\":\"JWT过期时间设置过长\",\"definition\":\"\",\"id\":\"jwtexptoolarge\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"JwtExpTooLarge\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"},\"defaultExp\":{\"allowEmpty\":false,\"defaultVal\":604800,\"label\":\"JWT过期时间阈值（秒）\",\"type\":\"NUMBER\",\"desc\":\"设置过期时间阈值大小（秒）\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"],\\\"defaultExp\\\":604800}\",\"passwordTag\":\"\",\"pluginDesc\":\"JWT过期时间设置过长\",\"pluginName\":\"JWT过期时间设置过长\"}],\"group\":\"\"},{\"code\":\"unreasonableauth\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"发现登录接口或其他需要授权的接口使用URL参数传递密码，可能导致URL中的密码信息在请求日志中泄漏，或在浏览器页面被他人通过窥视泄漏。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"需要提交密码的接口，密码不应该使用URL参数传输，应该在请求的body中传输，避免密码在浏览器页面或在日志中泄漏。\",\"enable\":true,\"name\":\"登录认证不合理\",\"definition\":\"\",\"id\":\"unreasonableauth\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"UnreasonableAuth\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"登录认证不合理\",\"pluginName\":\"登录认证不合理\"}],\"group\":\"\"},{\"code\":\"noauthaccess\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象级别鉴权\",\"description\":\"可查询敏感数据或进行敏感操作的接口可以在未鉴权的情况下访问，攻击者可以不登录就访问接口从而获取大量敏感数据或执行敏感操作。\",\"updateTime\":*************,\"type\":\"bola\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"可查询敏感数据或进行敏感操作的接口需要对访问者的身份进行校验，避免未通过身份校验者访问这类接口；对于未及时改造的接口，需要对接口访问进行监控，及时发现通过该接口异常操作的风险。\",\"enable\":true,\"name\":\"未鉴权\",\"definition\":\"\",\"id\":\"noauthaccess\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"NoauthAccess\",\"paramFields\":{\"errorPasswords\":{\"allowEmpty\":true,\"defaultVal\":[\"\\\"+password+\\\"\",\"+pass+\",\"Password\",\"password\",\"密码\"],\"label\":\"密码值白名单\",\"type\":\"STRING_ARRAY\",\"desc\":\"对响应中出现的密码进行剔除\"},\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"},\"authKeywordsUserDefine\":{\"allowEmpty\":true,\"defaultVal\":[],\"label\":\"鉴权信息字段关键字\",\"type\":\"STRING_ARRAY\",\"desc\":\"请求头或参数中的鉴权信息字段名\"}},\"params\":\"{\\\"errorPasswords\\\":[\\\"\\\\\\\"+password+\\\\\\\"\\\",\\\"+pass+\\\",\\\"Password\\\",\\\"password\\\",\\\"密码\\\"],\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"],\\\"authKeywordsUserDefine\\\":[]}\",\"passwordTag\":\"\",\"pluginDesc\":\"未鉴权\",\"pluginName\":\"未鉴权\"}],\"group\":\"\"},{\"code\":\"ssrf_weakness\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"服务端请求伪造\",\"description\":\"SSRF漏洞允许攻击者在服务端执行恶意请求，攻击者可能会利用SSRF漏洞访问内部敏感文件、绕过防火墙、发起端口扫描或攻击内部系统等。\",\"updateTime\":*************,\"type\":\"server_side_request_forgery\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"验证和限制用户输入或使用白名单。\",\"enable\":true,\"name\":\"服务端请求伪造\",\"definition\":\"\",\"id\":\"ssrf_weakness\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"SsrfWeakness\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"服务端请求伪造\",\"pluginName\":\"服务端请求伪造\"}],\"group\":\"\"},{\"code\":\"irregularanti\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象属性级别鉴权\",\"description\":\"在接口事件的返回中发现，敏感数据脱敏不规范，脱敏位数不足。\",\"updateTime\":*************,\"type\":\"broken_object_property_level_authorization\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"对于需要脱敏的数据，应该按照规范脱敏；对于未及时改造的接口，需要对接口访问情况进行监控，关注数据暴露情况，及时发现异常获取数据的风险。\",\"enable\":true,\"name\":\"脱敏不合规\",\"definition\":\"\",\"id\":\"irregularanti\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"IrregularAnti\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"脱敏不合规\",\"pluginName\":\"脱敏不合规\"}],\"group\":\"\"},{\"code\":\"abnormalpath\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象级别鉴权\",\"description\":\"接口路径中包含异常请求字符，可能存在鉴权绕过访问行为。\",\"updateTime\":*************,\"type\":\"bola\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"若系统使用了存在历史漏洞版本的权限框架如Apache Shiro，需要更新框架至安全版本或对漏洞进行补丁修复；若是自实现的权限校验，需要对用户输入的请求路径进行异常字符的过滤，避免攻击者通过异常请求绕过权限校验。\",\"enable\":true,\"name\":\"请求路径异常\",\"definition\":\"\",\"id\":\"abnormalpath\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"AbnormalPath\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"请求路径异常\",\"pluginName\":\"请求路径异常\"}],\"group\":\"\"},{\"code\":\"logintipnotsafe\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"攻击者根据返回的提示信息可能枚举出系统中存在的登录用户名，再对其密码进行暴力破解或者根据收集到的用户名进行其他更高级的攻击。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"建议对网站登录页面的判断回显信息修改为一致：用户名或密码不正确。\",\"enable\":false,\"name\":\"登录错误提示不合理\",\"definition\":\"\",\"id\":\"logintipnotsafe\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"LoginTipNotSafe\",\"paramFields\":{\"loginErrorTips\":{\"allowEmpty\":true,\"defaultVal\":[\"帐号不存在\",\"账号不存在\",\"未找到帐号\",\"未找到账号\",\"帐号未注册\",\"账号未注册\",\"密码不正确\",\"密码错误\",\"用户不存在\"],\"label\":\"登录错误提示语\",\"type\":\"STRING_ARRAY\",\"desc\":\"登录提示不合理的登录错误提示语\"}},\"params\":\"{\\\"loginErrorTips\\\":[\\\"帐号不存在\\\",\\\"账号不存在\\\",\\\"未找到帐号\\\",\\\"未找到账号\\\",\\\"帐号未注册\\\",\\\"账号未注册\\\",\\\"密码不正确\\\",\\\"密码错误\\\",\\\"用户不存在\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"登录错误提示不合理\",\"pluginName\":\"登录错误提示不合理\"}],\"group\":\"\"},{\"code\":\"passwordinrsp\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"在接口返回内容中发现密码信息，攻击者可在传输过程中进行监听或拦截，从而获取密码信息。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"需要限制接口返回密码信息，降低密码被他人获取的可能性；对未改造接口，需要对接口访问进行监控，及时发现恶意获取敏感数据的风险。\",\"enable\":true,\"name\":\"密码透出\",\"definition\":\"\",\"id\":\"passwordinrsp\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"PasswordInRsp\",\"paramFields\":{\"errorPasswords\":{\"allowEmpty\":true,\"defaultVal\":[\"\\\"+password+\\\"\",\"Password\",\"password\",\"密码\",\"text\"],\"label\":\"密码值白名单\",\"type\":\"STRING_ARRAY\",\"desc\":\"返回密码信息的白名单\"}},\"params\":\"{\\\"errorPasswords\\\":[\\\"\\\\\\\"+password+\\\\\\\"\\\",\\\"Password\\\",\\\"password\\\",\\\"密码\\\",\\\"text\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"密码透出\",\"pluginName\":\"密码透出\"}],\"group\":\"\"},{\"code\":\"updatepwdhasnooldpwd\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"更新密码接口未校验旧密码，可能存在一定的安全风险，如账号盗用导致密码被篡改。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"对于更新密码接口，需要检查是否有对旧密码做校验，对未做校验的接口及时修复。\",\"enable\":true,\"name\":\"更新密码接口设计不合理\",\"definition\":\"\",\"id\":\"updatepwdhasnooldpwd\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"UpdatePwdHasNoOldPwd\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"更新密码接口设计不合理\",\"pluginName\":\"更新密码接口设计不合理\"}],\"group\":\"\"},{\"code\":\"loginnameenumerable\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"攻击者可以通过接口查询来确定系统中存在的有效用户名，从而进行密码猜测或其他攻击，例如暴力破解、社会工程学攻击等。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"对于必须透出用户名是否存在的场景下，需要对接口进行人机认证或添加验证码校验，以提升系统安全性。\",\"enable\":true,\"name\":\"账号名可枚举\",\"definition\":\"\",\"id\":\"loginnameenumerable\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"LoginNameEnumerable\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"账号名可枚举\",\"pluginName\":\"账号名可枚举\"}],\"group\":\"\"},{\"code\":\"anyfiledownload\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OTHER\",\"utilizedWay\":\"\",\"typeName\":\"Web安全缺陷\",\"description\":\"接口中发现直接传递文件路径的参数或可穿越目录的恶意参数，恶意攻击者可能通过修改参数访问服务器中的任意文件，导致服务器中的重要数据文件泄漏。\",\"updateTime\":*************,\"type\":\"other\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"对于访问文件的参数，应禁止参数中传递完整的文件路径，同时需要注意对参数中的非法字符进行过滤，如../，file://等。\",\"enable\":true,\"name\":\"任意文件读取\",\"definition\":\"\",\"id\":\"anyfiledownload\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"AnyFileDownload\",\"paramFields\":{\"filePathArgKeywords\":{\"allowEmpty\":false,\"defaultVal\":[\"file\",\"path\"],\"label\":\"路径参数字段\",\"type\":\"STRING_ARRAY\",\"desc\":\"通过路径参数字段获取路径进行判断\"}},\"params\":\"{\\\"filePathArgKeywords\\\":[\\\"file\\\",\\\"path\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"任意文件读取\",\"pluginName\":\"任意文件读取\"}],\"group\":\"\"},{\"code\":\"backupleak\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"资产管理失当\",\"description\":\"应用下backup.zip、backup.rar等备份文件可以被下载，其中可能包含代码或敏感数据备份，导致信息或敏感数据泄露。\",\"updateTime\":*************,\"type\":\"improper_inventory_management\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"在应用服务器的目录下删除相应备份文件，备份文件等敏感文件应该放到非应用服务器目录下。\",\"enable\":true,\"name\":\"备份文件泄露\",\"definition\":\"\",\"id\":\"backupleak\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"BackupLeak\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"备份文件泄露\",\"pluginName\":\"备份文件泄露\"}],\"group\":\"\"},{\"code\":\"weaktokenauth\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象级别鉴权\",\"description\":\"接口使用了手机号、身份证号等个人敏感信息作为鉴权凭证，导致鉴权凭证易被猜测和伪造，攻击者可修改鉴权凭证为其他用户对应的手机号、身份证号等字段值绕过权限校验，从而获取敏感数据。\",\"updateTime\":*************,\"type\":\"bola\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"建议接口停止使用用户个人敏感信息作为鉴权凭证，应使用更为安全的方式鉴权，如使用JWT令牌进行鉴权；对于未及时改造的接口，需要对接口访问进行监控，及时发现通过修改鉴权凭证恶意拉取数据的行为。\",\"enable\":true,\"name\":\"鉴权凭证脆弱\",\"definition\":\"\",\"id\":\"weaktokenauth\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"WeakTokenAuth\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"},\"authKeywordsUserDefine\":{\"allowEmpty\":true,\"defaultVal\":[],\"label\":\"鉴权信息字段关键字\",\"type\":\"STRING_ARRAY\",\"desc\":\"请求头或参数中的鉴权信息字段名\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"],\\\"authKeywordsUserDefine\\\":[]}\",\"passwordTag\":\"\",\"pluginDesc\":\"鉴权凭证脆弱\",\"pluginName\":\"鉴权凭证脆弱\"}],\"group\":\"\"},{\"code\":\"any_fileupload\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OTHER\",\"utilizedWay\":\"\",\"typeName\":\"Web安全缺陷\",\"description\":\"可通过接口上传特殊后缀名的文件，如果上传jsp、php等可能导致代码执行漏洞，上传html、js等文件进行xss攻击或钓鱼。\",\"updateTime\":*************,\"type\":\"other\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"限制上传文件的后缀，建议使用后缀名的白名单，限制非白名单中的文件后缀名。\",\"enable\":true,\"name\":\"允许上传恶意文件\",\"definition\":\"\",\"id\":\"any_fileupload\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"AnyFileUpload\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"允许上传恶意文件\",\"pluginName\":\"允许上传恶意文件\"}],\"group\":\"\"},{\"code\":\"anysmssend\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"未受限的敏感业务流访问\",\"description\":\"在发送短信的接口中，在请求参数中发现手机号码和短信内容，恶意攻击者可能利用接口给指定手机发送恶意短信。\",\"updateTime\":*************,\"type\":\"unrestricted_access_to_sensitive_business_flows\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"短信发送接口应该由后端固定模板给前端调用，不能由前端直接确定发送的短信内容。\",\"enable\":true,\"name\":\"任意短信发送\",\"definition\":\"\",\"id\":\"anysmssend\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"AnySmsSend\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"任意短信发送\",\"pluginName\":\"任意短信发送\"}],\"group\":\"\"},{\"code\":\"sessioninurl\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"URL中发现Token或SessionID等鉴权信息，URL中的信息会出现在日志中，也可能通过referer发送给第三方，同时URL在浏览器页面上也可能被用户看到，导致信息被他人获取。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"不通过URL传递敏感数据，应该使用请求body传递敏感信息。\",\"enable\":true,\"name\":\"鉴权信息在URL中\",\"definition\":\"\",\"id\":\"sessioninurl\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"SessionInUrl\",\"paramFields\":{\"tokenKeywords\":{\"allowEmpty\":false,\"defaultVal\":[\"token\",\"sessionid\"],\"label\":\"鉴权信息关键字\",\"type\":\"STRING_ARRAY\",\"desc\":\"检查url中不应该出现的鉴权信息字段\"}},\"params\":\"{\\\"tokenKeywords\\\":[\\\"token\\\",\\\"sessionid\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"鉴权信息在URL中\",\"pluginName\":\"鉴权信息在URL中\"}],\"group\":\"\"},{\"code\":\"passwordincookie\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OTHER\",\"utilizedWay\":\"\",\"typeName\":\"Web安全缺陷\",\"description\":\"接口设计中，将用户密码保存在cookie中，可能被中间人攻击拦截获取密码信息，或在浏览器被他人借用的情况下泄漏密码信息。\",\"updateTime\":*************,\"type\":\"other\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"避免cookie中出现重要敏感信息，相关敏感信息应该由服务器端保存在session或数据库中。\",\"enable\":true,\"name\":\"在cookie中保存密码\",\"definition\":\"\",\"id\":\"passwordincookie\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"PasswordInCookie\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"在cookie中保存密码\",\"pluginName\":\"在cookie中保存密码\"}],\"group\":\"\"},{\"code\":\"webshell\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OTHER\",\"utilizedWay\":\"\",\"typeName\":\"Web安全缺陷\",\"description\":\"WebShell是黑客经常使用的一种恶意脚本，其目的是获得服务器的执行操作权限，比如执行系统命令、窃取用户数据、删除web页面、修改主页等。\",\"updateTime\":*************,\"type\":\"other\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"WebShell只是漏洞利用后的实施工具，想要修复此类弱点，需要知道WebShell所在站点具体存在哪些漏洞点（一般是文件上传漏洞导致）并修复，防止WebShell被上传利用。\",\"enable\":true,\"name\":\"WebShell\",\"definition\":\"\",\"id\":\"webshell\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"WebShell\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"webshell\",\"pluginName\":\"webshell\"}],\"group\":\"\"},{\"code\":\"directorytraverse\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"安全配置错误\",\"description\":\"apache等服务配置时未禁用目录浏览，可能导致项目文件泄漏，如日志文件、项目配置、备份文件等。\",\"updateTime\":*************,\"type\":\"security_misconfiguration\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"服务器配置禁用目录浏览。\",\"enable\":true,\"name\":\"未禁用目录浏览\",\"definition\":\"\",\"id\":\"directorytraverse\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"DirectoryTraverse\",\"paramFields\":{\"directoryKeywords\":{\"allowEmpty\":false,\"defaultVal\":[\"index of /\",\"directory listing for /\",\"directory: /\",\"index of /\",\"directory listing for /\",\"<head><title>index of\",\"<table summary=\\\"directory listing\\\"\",\"last modified</a>\"],\"label\":\"匹配关键字\",\"type\":\"STRING_ARRAY\",\"desc\":\"检查响应内容中不应该出现的列目录关键字\"}},\"params\":\"{\\\"directoryKeywords\\\":[\\\"index of /\\\",\\\"directory listing for /\\\",\\\"directory: /\\\",\\\"index of /\\\",\\\"directory listing for /\\\",\\\"<head><title>index of\\\",\\\"<table summary=\\\\\\\"directory listing\\\\\\\"\\\",\\\"last modified</a>\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"未禁用目录浏览\",\"pluginName\":\"未禁用目录浏览\"}],\"group\":\"\"},{\"code\":\"osslistobjnoauth\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"安全配置错误\",\"description\":\"存储桶设置了ListObject权限公开，如果存储桶中存储了敏感数据，例如用户个人信息、机密文件等，那么公开ListObject权限可能会导致敏感信息未经授权访问，从而造成严重的信息泄露。\",\"updateTime\":*************,\"type\":\"security_misconfiguration\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"建议限制存储桶的访问权限，只允许授权用户访问存储桶中的数据；同时对接口访问情况进行监控，及时发现存储桶访问权限公开问题并及时修复。\",\"enable\":true,\"name\":\"存储桶ListObject权限公开\",\"definition\":\"\",\"id\":\"osslistobjnoauth\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"OssListObjNoAuth\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"存储桶ListObject权限公开\",\"pluginName\":\"存储桶ListObject权限公开\"}],\"group\":\"\"},{\"code\":\"apiinfoexposure\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"资产管理失当\",\"description\":\"接口文档暴露，增加了接口资产暴露面，攻击者可以根据接口文档对接口进行更深入的测试和利用。\",\"updateTime\":*************,\"type\":\"improper_inventory_management\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"应当限制接口文档的访问，只能在开发或测试环境中可以访问。\",\"enable\":true,\"name\":\"接口信息泄露\",\"definition\":\"\",\"id\":\"apiinfoexposure\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"ApiInfoExposure\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"接口信息泄露\",\"pluginName\":\"接口信息泄露\"}],\"group\":\"\"},{\"code\":\"plainpasswordrsp\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"在接口的返回内容中发现明文密码，并且密码没有进行hash就直接以明文保存在了数据库中，服务器不应该以任何形式保存明文密码信息。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"需要限制接口返回密码信息，降低密码被他人获取的可能性；密码应加盐hash后保存，避免数据泄漏后泄漏真实密码；对未改造接口，需要对接口访问进行监控，及时发现恶意获取敏感数据的风险。\",\"enable\":true,\"name\":\"明文密码透出\",\"definition\":\"\",\"id\":\"plainpasswordrsp\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"PlainPasswordRsp\",\"paramFields\":{\"errorPasswords\":{\"allowEmpty\":true,\"defaultVal\":[\"\\\"+password+\\\"\",\"Password\",\"password\",\"密码\",\"text\"],\"label\":\"密码值白名单\",\"type\":\"STRING_ARRAY\",\"desc\":\"返回密码信息的白名单\"}},\"params\":\"{\\\"errorPasswords\\\":[\\\"\\\\\\\"+password+\\\\\\\"\\\",\\\"Password\\\",\\\"password\\\",\\\"密码\\\",\\\"text\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"明文密码透出\",\"pluginName\":\"明文密码透出\"}],\"group\":\"\"},{\"code\":\"configinfoinstaticfile\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"资产管理失当\",\"description\":\"配置文件可以被下载，可能造成数据库连接信息、API密钥等敏感信息泄露。\",\"updateTime\":*************,\"type\":\"improper_inventory_management\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"在服务器中删除相应文件，或在应用服务器配置中限制指定目录的访问。\",\"enable\":true,\"name\":\"配置文件泄露\",\"definition\":\"\",\"id\":\"configinfoinstaticfile\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"ConfigInfoInStaticFile\",\"paramFields\":{\"filterKeywords\":{\"allowEmpty\":true,\"defaultVal\":[],\"label\":\"根据关键字过滤\",\"type\":\"STRING_ARRAY\",\"desc\":\"响应体/文件中包含关键字则不会记录为弱点\"}},\"params\":\"{\\\"filterKeywords\\\":[]}\",\"passwordTag\":\"\",\"pluginDesc\":\"配置文件泄露\",\"pluginName\":\"配置文件泄露\"}],\"group\":\"\"},{\"code\":\"sensitiveinurl\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象属性级别鉴权\",\"description\":\"URL中发现关键身份证号码或手机号等敏感信息，url会出现在日志中，也可能通过referer发送给第三方，同时URL在浏览器页面上也可能被用户看到，导致信息被窥视。\",\"updateTime\":*************,\"type\":\"broken_object_property_level_authorization\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"不通过URL传递敏感数据，应该使用请求body传递敏感信息。\",\"enable\":true,\"name\":\"敏感信息在URL中\",\"definition\":\"\",\"id\":\"sensitiveinurl\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"SensitiveInUrl\",\"paramFields\":{\"sensitiveLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"email\",\"bankCard\",\"idCard\",\"password\",\"personName\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"检查url中不应该出现的敏感数据标签\"}},\"params\":\"{\\\"sensitiveLabels\\\":[\\\"mobile\\\",\\\"email\\\",\\\"bankCard\\\",\\\"idCard\\\",\\\"password\\\",\\\"personName\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"敏感信息在URL中\",\"pluginName\":\"敏感信息在URL中\"}],\"group\":\"\"},{\"code\":\"passwordinupdateuserinfoapi\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象属性级别鉴权\",\"description\":\"更新用户接口中除去一般的用户信息还包含密码字段，存在潜在的安全风险，如账号借用导致密码修改、结合CSRF漏洞导致密码篡改等。\",\"updateTime\":*************,\"type\":\"broken_object_property_level_authorization\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"更新用户接口不应该具备更新密码功能，如需更新用户密码应当设计额外的更新密码接口并存在校验机制，避免密码被恶意篡改。\",\"enable\":true,\"name\":\"更新用户接口设计不规范\",\"definition\":\"\",\"id\":\"passwordinupdateuserinfoapi\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"PasswordInUpdateUserInfoAPI\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"更新用户接口设计不规范\",\"pluginName\":\"更新用户接口设计不规范\"}],\"group\":\"\"},{\"code\":\"plainpasswordreq\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"在接口请求中发现明文密码传输，攻击者可在传输过程中进行监听或拦截，从而获取用户真实密码。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"需要对密码做加密传输或使用https协议传输，降低流量被监听时泄漏密码的可能性。\",\"enable\":true,\"name\":\"明文密码传输\",\"definition\":\"\",\"id\":\"plainpasswordreq\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"PlainPasswordReq\",\"paramFields\":{\"loginErrorKeywords\":{\"allowEmpty\":true,\"defaultVal\":[\"验证码错误\",\"密码错误\",\"重试\",\"不存在\",\"不正确\",\"失败\",\"password error\",\"not exist\"],\"label\":\"登录失败关键字\",\"type\":\"STRING_ARRAY\",\"desc\":\"如果返回信息中存在关键字，则被判断为登录失败\"}},\"params\":\"{\\\"loginErrorKeywords\\\":[\\\"验证码错误\\\",\\\"密码错误\\\",\\\"重试\\\",\\\"不存在\\\",\\\"不正确\\\",\\\"失败\\\",\\\"password error\\\",\\\"not exist\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"明文密码传输\",\"pluginName\":\"明文密码传输\"}],\"group\":\"\"},{\"code\":\"captchainrsp\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"验证码值在响应中返回了，攻击者能够直接通过接口响应获取验证码值，从而伪造身份或绕过验证。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"应避免验证码值在接口中返回。\",\"enable\":true,\"name\":\"接口存在验证码返回\",\"definition\":\"\",\"id\":\"captchainrsp\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"CaptchaInRsp\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"接口存在验证码返回\",\"pluginName\":\"接口存在验证码返回\"}],\"group\":\"\"},{\"code\":\"unnecessarydataexposure\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象属性级别鉴权\",\"description\":\"可疑的查询请求，造成非必要透出的敏感数据暴露。\",\"updateTime\":*************,\"type\":\"broken_object_property_level_authorization\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"在接口设计时，应遵循数据最小使用原则，尽量不透出非必要的敏感数据。\",\"enable\":true,\"name\":\"非必要的数据暴露\",\"definition\":\"\",\"id\":\"unnecessarydataexposure\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"UnnecessaryDataExposure\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"},\"userNameKeywords\":{\"allowEmpty\":true,\"defaultVal\":[\"account\",\"loginuser\",\"loginname\",\"username\",\"userid\",\"user\",\"usr\",\"phone\",\"mobile\",\"logincode\",\"name\"],\"label\":\"账号名\",\"type\":\"STRING_ARRAY\",\"desc\":\"登录账号名\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"],\\\"userNameKeywords\\\":[\\\"account\\\",\\\"loginuser\\\",\\\"loginname\\\",\\\"username\\\",\\\"userid\\\",\\\"user\\\",\\\"usr\\\",\\\"phone\\\",\\\"mobile\\\",\\\"logincode\\\",\\\"name\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"非必要的数据暴露\",\"pluginName\":\"非必要的数据暴露\"}],\"group\":\"\"},{\"code\":\"filedownloadenumerable\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象级别鉴权\",\"description\":\"在接口入参中发现可遍历的参数，攻击者可按照参数的遍历特征通过脚本批量下载文件。\",\"updateTime\":*************,\"type\":\"bola\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"检查接口是否做了横向权限校验，提高可遍历参数的复杂性，避免使用短数字、姓名等易于猜测的参数；对接口访问进行监控，及时发现通过接口批量遍历下载文件导致的数据泄露风险。\",\"enable\":true,\"name\":\"可遍历下载文件\",\"definition\":\"\",\"id\":\"filedownloadenumerable\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"FileDownloadEnumerable\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"可遍历下载文件\",\"pluginName\":\"可遍历下载文件\"}],\"group\":\"\"},{\"code\":\"vcodeauthlackofratelimiting\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"未受限的资源消耗\",\"description\":\"验证码认证接口缺乏速率限制，攻击者可以使用自动化脚本进行恶意尝试，从而绕过验证码认证进行攻击。\",\"updateTime\":*************,\"type\":\"unrestricted_resource_consumption\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"建议限制同一IP地址或用户在一定时间内尝试使用验证码认证的次数。\",\"enable\":true,\"name\":\"验证码认证接口缺乏速率限制\",\"definition\":\"\",\"id\":\"vcodeauthlackofratelimiting\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"VCodeAuthLackOfRateLimiting\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"验证码认证接口缺乏速率限制\",\"pluginName\":\"验证码认证接口缺乏速率限制\"}],\"group\":\"\"},{\"code\":\"sensitiveinhttp\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"不安全的API使用\",\"description\":\"API使用了HTTP协议进行通信，传输的敏感数据易被中间人拦截和窃取，并缺乏数据完整性保护。\",\"updateTime\":*************,\"type\":\"unsafe_consumption_of_apis\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"确保传输敏感数据的API交互都在安全的通信通道上进行(TLS)。\",\"enable\":true,\"name\":\"HTTP协议传输敏感数据\",\"definition\":\"\",\"id\":\"sensitiveinhttp\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"SensitiveInHttp\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"},\"isInternetOpen\":{\"allowEmpty\":false,\"defaultVal\":\"true\",\"label\":\"仅检查部署域是内网访问域是互联网的API(true/false)\",\"type\":\"STRING\",\"desc\":\"是否只检查部署域是内网访问域是互联网的API\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"],\\\"isInternetOpen\\\":\\\"true\\\"}\",\"passwordTag\":\"\",\"pluginDesc\":\"HTTP协议传输敏感数据\",\"pluginName\":\"HTTP协议传输敏感数据\"}],\"group\":\"\"},{\"code\":\"sourcecodeleak\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"资产管理失当\",\"description\":\"源代码代码仓库中的文件泄露，如.git/目录、.svn目录等，攻击者可能利用这些文件还原程序源代码。\",\"updateTime\":*************,\"type\":\"improper_inventory_management\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"在服务器中删除相应文件，或在应用服务器配置中限制指定目录的访问。\",\"enable\":true,\"name\":\"源代码泄露\",\"definition\":\"\",\"id\":\"sourcecodeleak\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"SourceCodeLeak\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"源代码泄露\",\"pluginName\":\"源代码泄露\"}],\"group\":\"\"},{\"code\":\"shellapi\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OTHER\",\"utilizedWay\":\"\",\"typeName\":\"Web安全缺陷\",\"description\":\"在接口的请求参数中发现可以执行的系统命令，攻击者可以通过修改参数，绕过系统的限制执行系统命令，可能导致系统中重要数据泄漏、攻击者获取服务器控制权限或造成系统服务不可用。\",\"updateTime\":*************,\"type\":\"other\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"根据业务需求进行改造，限制接口不得传递命令参数，应根据业务需求，限定只开放部分的参数，最终执行的系统命令应由后端控制，同时要注意对参数中非法字符的过滤，避免黑客构造恶意参数导致系统执行非法的系统命令。\",\"enable\":true,\"name\":\"命令执行\",\"definition\":\"\",\"id\":\"shellapi\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"ShellApi\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"命令执行\",\"pluginName\":\"命令执行\"}],\"group\":\"\"},{\"code\":\"loginlackofratelimiting\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"未受限的资源消耗\",\"description\":\"登录接口缺乏速率限制，攻击者可以使用暴力破解等方式进行恶意登录，从而访问系统中的敏感信息或进行其他不当操作。\",\"updateTime\":*************,\"type\":\"unrestricted_resource_consumption\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"建议限制同一IP地址或用户在一定时间内尝试登录的次数。\",\"enable\":true,\"name\":\"登录接口缺乏速率限制\",\"definition\":\"\",\"id\":\"loginlackofratelimiting\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"LoginLackOfRateLimiting\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"登录接口缺乏速率限制\",\"pluginName\":\"登录接口缺乏速率限制\"}],\"group\":\"\"},{\"code\":\"expiredjwtinuse\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"本应过期的JWT仍通过权限验证。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":true,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"在JWT验证时，需要验证JWT的有效性和过期时间，避免过期的JWT仍能通过验证。\",\"enable\":true,\"name\":\"过期的JWT未失效\",\"definition\":\"\",\"id\":\"expiredjwtinuse\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"ExpiredJwtInUse\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"过期的JWT未失效\",\"pluginName\":\"过期的JWT未失效\"}],\"group\":\"\"},{\"code\":\"horizontalaccess\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象级别鉴权\",\"description\":\"在接口入参中发现可遍历的参数，攻击者可按照参数的遍历特征通过脚本进行批量的数据拉取。\",\"updateTime\":*************,\"type\":\"bola\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"检查接口是否做了横向权限校验，提高可遍历参数的复杂性，避免使用短数字、姓名等易于猜测的参数；对接口访问进行监控，及时发现通过接口批量遍历数据的风险。\",\"enable\":true,\"name\":\"参数可遍历\",\"definition\":\"\",\"id\":\"horizontalaccess\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"HorizontalAccess\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"},\"traversalParamNames\":{\"allowEmpty\":true,\"defaultVal\":[],\"label\":\"自定义可遍历参数字段\",\"type\":\"STRING_ARRAY\",\"desc\":\"自定义需要检查的可遍历参数字段\"},\"ignoreTraversalKeywords\":{\"allowEmpty\":false,\"defaultVal\":[\"page\",\"limit\",\"start\",\"offset\",\"flag\",\"flg\",\"type\",\"typ\",\"row\",\"random\",\"date\",\"screen\",\"status\",\"mode\",\"tab\",\"tag\",\"method\",\"action\",\"service\",\"viewstate\",\"eventvalidation\",\"appid\"],\"label\":\"可忽略的参数名关键字\",\"type\":\"STRING_ARRAY\",\"desc\":\"检查时忽略的参数名关键字\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\",\\\"password\\\"],\\\"traversalParamNames\\\":[],\\\"ignoreTraversalKeywords\\\":[\\\"page\\\",\\\"limit\\\",\\\"start\\\",\\\"offset\\\",\\\"flag\\\",\\\"flg\\\",\\\"type\\\",\\\"typ\\\",\\\"row\\\",\\\"random\\\",\\\"date\\\",\\\"screen\\\",\\\"status\\\",\\\"mode\\\",\\\"tab\\\",\\\"tag\\\",\\\"method\\\",\\\"action\\\",\\\"service\\\",\\\"viewstate\\\",\\\"eventvalidation\\\",\\\"appid\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"参数可遍历\",\"pluginName\":\"参数可遍历\"}],\"group\":\"\"},{\"code\":\"querynolimit\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"损坏的对象属性级别鉴权\",\"description\":\"在接口的请求中发现与返回数量相关的查询参数，可以通过修改参数，单次获取大量敏感数据。\",\"updateTime\":*************,\"type\":\"broken_object_property_level_authorization\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"需要检查接口中与返回数量相关的查询参数是否做了限制，避免接口被恶意利用单次获取大量数据；对于未及时改造的接口，需要对接口访问情况进行监控，关注数据暴露情况，及时发现异常获取数据的风险。\",\"enable\":true,\"name\":\"返回数据量可修改\",\"definition\":\"\",\"id\":\"querynolimit\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"QueryNoLimit\",\"paramFields\":{\"matchLabels\":{\"allowEmpty\":false,\"defaultVal\":[\"mobile\",\"idCard\",\"bankCard\"],\"label\":\"敏感数据标签\",\"type\":\"LABEL_ID_LIST\",\"desc\":\"对响应中出现相应敏感标签数据的请求进行检查\"}},\"params\":\"{\\\"matchLabels\\\":[\\\"mobile\\\",\\\"idCard\\\",\\\"bankCard\\\"]}\",\"passwordTag\":\"\",\"pluginDesc\":\"返回数据量可修改\",\"pluginName\":\"返回数据量可修改\"}],\"group\":\"\"},{\"code\":\"accountinfoinloginpage\",\"pluginType\":\"API_WEAKNESS\",\"weaknessType\":\"OWASP_API_SECURITY_TOP10\",\"utilizedWay\":\"\",\"typeName\":\"身份认证缺陷\",\"description\":\"网页源代码中存在账号密码等敏感信息，攻击者可以通过审查网页源代码获取到用户账号和密码，导致用户认证信息泄露。\",\"updateTime\":*************,\"type\":\"broken_authentication\",\"isHost\":false,\"utilizedInfluenceDesc\":\"\",\"groupName\":\"\",\"solution\":\"需要对网页源代码中存在账号密码等敏感信息的应用及时整改；对未及时整改的应用进行监控，及时发现应用的异常行为风险。\",\"enable\":true,\"name\":\"登录页面存在账号密码\",\"definition\":\"\",\"id\":\"accountinfoinloginpage\",\"pluginPolicies\":[{\"dataLabelTag\":\"\",\"enable\":true,\"id\":\"AccountInfoInLoginPage\",\"paramFields\":{},\"params\":\"{}\",\"passwordTag\":\"\",\"pluginDesc\":\"登录页面存在账号密码泄露问题\",\"pluginName\":\"登录页面存在账号密码\"}],\"group\":\"\"}]";
        List<WeaknessRule> weaknessRules = JSON.parseArray(json, WeaknessRule.class);
        Map<String, String> weaknessRuleMap = new HashMap<>();
        Map<String, String> weaknessRuleNameMap = new HashMap<>();

        for (WeaknessRule weaknessRule : weaknessRules) {
            weaknessRuleMap.put(weaknessRule.getId(), weaknessRule.getType());
        }

        for (WeaknessRule weaknessRule : weaknessRules) {
            weaknessRuleNameMap.put(weaknessRule.getId(), weaknessRule.getTypeName());
        }

        String collectionName = "httpApiWeakness";
        List<ApiWeakness> weaknesses;
        Long count = 0L;
        Query query = new Query().addCriteria(Criteria.where("delFlag").is(false)).limit(1000);
        while (true) {
            if (count != 0) {
                query = new Query().addCriteria(Criteria.where("delFlag").is(false)).skip(count).limit(1000);
            }
            weaknesses = mongoTemplate.find(query, ApiWeakness.class, collectionName);
            count += weaknesses.size();
            for (ApiWeakness apiWeakness : weaknesses) {
                Update update = new Update();
                update.set("operationId", generatorByDefault(apiWeakness));
                update.set("updateWeak", true);
                if (weaknessRuleMap.containsKey(apiWeakness.getWeaknessId())) {
                    String type = weaknessRuleMap.get(apiWeakness.getWeaknessId());
                    String typeName = weaknessRuleNameMap.get(apiWeakness.getWeaknessId());
                    if (type != null) {
                        update.set("type", type);
                        update.set("typeName", typeName);
                        update.set("category", type);
                    } else {
                        update.set("type", "other");
                        update.set("typeName", "Web安全缺陷");
                        update.set("category", "other");
                    }
                } else {
                    update.set("type", "other");
                    update.set("typeName", "Web安全缺陷");
                    update.set("category", "other");
                }
                //同步下应用名称
                if (DataUtil.isEmpty(apiWeakness.getApi().getAppName())) {
                    String appUri = apiWeakness.getAppUri();
                    Query appQuery = Query.query(Criteria.where("uri").is(appUri));
                    appQuery.fields().include("name");
                    HttpAppResource httpAppResource = mongoTemplate.findOne(appQuery, HttpAppResource.class, "httpApp");
                    update.set("api.appName", httpAppResource.getName());
                }
                //更新终端
                if (DataUtil.isNotEmpty(apiWeakness.getApi().getTerminals())) {
                    List<String> oldTerminals = apiWeakness.getApi().getTerminals();
                    List<String> newTermains = new ArrayList<>();
                    for (String termain : oldTerminals) {
                        newTermains.add(uaTypeMap.get(termain) != null ? uaTypeMap.get(termain) : termain);
                    }
                    update.set("api.terminals", newTermains);
                }

                mongoTemplate.updateMulti(new Query().addCriteria(Criteria.where("_id").is(apiWeakness.getId())), update, collectionName);
            }
            if (weaknesses.size() == 0) {
                break;
            }
        }

        log.info("update success，total:{}", count);
    }

    public String generatorByDefault(ApiWeakness apiWeakness) {
        //获取当前日期
        long now = apiWeakness.getEarlyTimestamp();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        String formattedDate = dateFormat.format(now);

        //随机生成
        StringBuffer buffer = new StringBuffer();
        String formattedStr = RandomUtil.randomString(5);
        buffer.append("W").append("-").append(formattedDate).append("-").append(formattedStr);
        return buffer.toString();
    }

    private Map<String, String> uaTypeMap = new HashMap<String, String>() {{
        put("python", "Python");
        put("php", "PHP");
        put("lua", "Lua");
        put("axis", "Axis");
        put("vue", "Vue");
        put("java", "Java");
        put("go", "Go");
        put("node", "Node");
        put("postman", "Postman");
        put("android", "Android");
        put("ios", "IOS");
        put("mobile", "Mobile");
        put("pc", "PC");
        put("jetty", "Jetty");
    }};

}