package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit.mix.schdule.domain.utils.ParameterHelper;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.service.manager.web.weakness.WeaknessStatisticsService;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/31 4:40 下午
 */
@Component
@Slf4j
public class WeaknessJob {

    private final WeaknessStatisticsService weaknessStatisticsService;

    public WeaknessJob(WeaknessStatisticsService weaknessStatisticsService) {
        this.weaknessStatisticsService = weaknessStatisticsService;
    }

    @LockedScheduler(cron = "0 0 0/6 * * ? ", executor = "weaknessJob", description = "弱点数量走势图", interval = 1000 * 60 * 60 * 24, intervalConditionSpEL = "#root.check()")
    public void execute() throws Exception {
        String s = ParameterHelper.get();
        Date date = null;
        if (!StringUtils.isNullOrEmpty(s)) {
            Map<String, Object> map = JSON.parseObject(s, Map.class);
            if (map.get("timestamp") != null) {
                date = new Date((Long) map.get("timestamp"));
            }
        }
        // 默认执行两天的数据
        if (date == null) {
            date = new Date();
            weaknessStatisticsService.executeDate(date);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            weaknessStatisticsService.executeDate(calendar.getTime());
        } else {
            weaknessStatisticsService.executeDate(date);
        }
    }

    public boolean check() {
        return weaknessStatisticsService.count() >= 100000;
    }
}
