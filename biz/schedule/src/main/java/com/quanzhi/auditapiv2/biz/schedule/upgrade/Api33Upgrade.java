package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.permission.domain.Condition;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysRole;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * create at 2024/12/11 10:37
 * @description: api3.2升级3.3逻辑
 **/
@Component
@Slf4j
public class Api33Upgrade  implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    public Api33Upgrade(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }


    @Override
    public int getVersion() {
        return 0;
    }

    @Override
    public long getVersionV2() {
        return 33020250224L;
    }

    @Override
    public void upgrade() throws Exception {
        //用户升级
        sysUserUpdate();
        //角色升级
        sysRoleleUpdate();
    }


    /**
     * 角色升级
     */
    private void sysRoleleUpdate() {
        List<String> defaultRoles = Arrays.asList("系统管理员", "审计管理员", "数据安全管理员");
        List<SysRole> sysRoles = mongoTemplate.find(new Query(), SysRole.class, "sysRole");
        for(SysRole sysRole:sysRoles){
            Update update=new Update();
            if(defaultRoles.contains(sysRole.getRoleName())){
                update.set("defaultFlag",true);
            }else{
                update.set("defaultFlag",false);
            }
            Condition condition1=new Condition();
            condition1.setKey("MENU");
            condition1.setOperator("in");
            if("审计管理员".equals(sysRole.getRoleName())){
                condition1.setValue(Arrays.asList("audit_record"));
            }else if("系统管理员".equals(sysRole.getRoleName())){
                condition1.setValue(Arrays.asList("account_management","data_group_management","role_management","safe_config"));
            }else{
                condition1.setValue(Arrays.asList("overview","api_list","webapp","data","weakness","risk_list","threat","exception","analysis_ip","analysis_account","analysis_file","analysis_trace","analysis_log","report","situation","control_upstream","control_route","control_cluster","api_settings_labels","api_settings_sensi_level","api_settings_risk_level","api_settings_state","api_settings_log","app_settings_analysis","app_settings_labels","app_settings_state","assets_filter","assets_definition","ip_settings_analysis","ip_settings_risk_level","network_segment","ip_settings_database","account_settings_analysis","account_risk_level","account_settings_state","account_settings_department_config","data_settings_labels","data_settings_sensi_level","data_settings_risk_level","data_settings_template","subscribe_configs","terminal_config","data_task","data_export","file_settings_sensi_level","data_kafka","setting_weakness","risk_settings_rule","risk_settings_quota","risk_settings_variable","risk_control","init_wizard","gateway_management","setting_plugin","operation_tools","document_management","data_saved","task_management","settings_multi_nodes","notice_record","system_authorization","settings_update","system_monitor","system_config","ai_model_setting", "system_status", "log_trace"));
            }
            Condition condition2=new Condition();
            condition2.setKey("ACTION");
            condition2.setOperator("in");
            condition2.setValue(Arrays.asList("WRITE"));

            Condition condition3=new Condition();
            condition3.setKey("FUNC");
            condition3.setOperator("in");
            condition3.setValue(Arrays.asList("sample:sampleList:cancelDesensitize"));

            update.set("menuConditions",Arrays.asList(condition1));
            update.set("actionConditions",Arrays.asList(condition2));
            update.set("funcConditions",Arrays.asList(condition3));


            mongoTemplate.upsert(new Query().addCriteria(Criteria.where("_id").is(sysRole.getId())),update,"sysRole");
        }
        log.info("sysRoleUpdate success");
    }

    /**
     * 用户升级
     */
    private void sysUserUpdate() {
        List<String> defaultUsers = Arrays.asList("sysadmin", "audit", "webadmin");
        List<SysUser> sysUsers = mongoTemplate.find(new Query(), SysUser.class, "sysUser");
        for(SysUser sysUser:sysUsers){
            Update update=new Update();
            if(defaultUsers.contains(sysUser.getUsername())){
                update.set("defaultFlag",true);
            }else{
                update.set("defaultFlag",false);
            }
            update.set("dataGroupIds",Arrays.asList("1"));
            mongoTemplate.upsert(new Query().addCriteria(Criteria.where("_id").is(sysUser.getRoleId())),update,"sysUser");
        }
        log.info("sysUserUpdate success");
    }


}