//package com.quanzhi.auditapiv2.biz.schedule.job;
//
//import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
//import com.quanzhi.auditapiv2.biz.schedule.task.structure.SpecialAppScanService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * create at 2021/12/31 11:15 上午
// * @description: 应用结构树扫描定时任务
// **/
//@Component
//@Slf4j
//public class SpecialAppScanJob {
//
//    @Autowired
//    private SpecialAppScanService specialAppScanService;
//
//    @LockedScheduler(cron = "0 0 */6 * * ?", executor = "specialAppScanJob", description = "对一些特殊的应用进行筛选，进行规则的提取")
//    public void execute() throws Exception {
//        doJob();
//    }
//
//
//    private void doJob() {
//        specialAppScanService.scan();
//    }
//}