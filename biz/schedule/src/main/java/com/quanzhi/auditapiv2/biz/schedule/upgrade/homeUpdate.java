package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.biz.schedule.job.HomeJob;
import com.quanzhi.auditapiv2.biz.schedule.job.RiskCountryCountScheduleJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: yangzx
 * @Date: 2025-02-17 14:53
 * @Description:
 */
@Component
@Slf4j
public class homeUpdate implements UpgradeService {

    private final HomeJob homeJob;
    private final RiskCountryCountScheduleJob riskCountryCountScheduleJob;

    public homeUpdate(HomeJob homeJob, RiskCountryCountScheduleJob riskCountryCountScheduleJob) {
        this.homeJob = homeJob;
        this.riskCountryCountScheduleJob = riskCountryCountScheduleJob;
    }

    @Override
    public int getVersion() {
        return 20250217;
    }

    @Override
    public long getVersionV2() {
        return 33020250217L;
    }

    @Override
    public void upgrade() throws Exception {
        homeJob.doJob();
        riskCountryCountScheduleJob.doJob();
        riskCountryCountScheduleJob.doDataRevealIpCountry();
    }

}
