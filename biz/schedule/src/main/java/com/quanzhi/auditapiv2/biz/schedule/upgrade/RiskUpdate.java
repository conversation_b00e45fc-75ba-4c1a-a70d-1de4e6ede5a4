package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create at 2022/8/10 2:12 下午
 * @description: 风险规则的一些字段升级
 **/
@Component
@Slf4j
public class RiskUpdate implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    private final IHttpAppService httpAppService;

    public RiskUpdate(MongoTemplate mongoTemplate, IHttpAppService httpAppService) {
        this.mongoTemplate = mongoTemplate;
        this.httpAppService = httpAppService;
    }

    @Override
    public int getVersion() {
        return 20240103;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        log.info("update risk,{}", DateUtil.currentDateTime());

        String collectionName = "riskInfo";
        List<RiskInfo> riskInfos;
        Long count = 0L;
        long time = System.currentTimeMillis() + 1000 * 60;
        Query query = new Query().addCriteria(Criteria.where("firstTime").lte(time).and("apiUrl").exists(false)).limit(100);
        do {
            if (count != 0) {
                query = new Query().addCriteria(Criteria.where("firstTime").lte(time).and("apiUrl").exists(false)).skip(count).limit(100);
            }
            riskInfos = mongoTemplate.find(query, RiskInfo.class, collectionName);
            count += riskInfos.size();
            for (RiskInfo riskInfo : riskInfos) {
                Update update = new Update();
                List<RiskInfo.Entity> entities = riskInfo.getEntities();
                List<RiskInfo.Entity> channels = riskInfo.getChannels();
                List<RiskInfo.Entity> relatedInfos = riskInfo.getRelatedInfos();
                entities.addAll(channels);
                entities.addAll(relatedInfos);
                for (RiskInfo.Entity entity : entities) {
                    if (entity.getType().equals("API")) {
                        update.set("apiUrl", entity.getValue().replaceAll("httpapi:", ""));
                        update.set("apiUri", entity.getValue());
                    } else if (entity.getType().equals("APP")) {
                        update.set("host", entity.getValue().replaceAll("httpapp:", ""));
                        update.set("appUri", entity.getValue());
                        HttpAppDto httpAppByAppUri = httpAppService.getHttpAppByAppUri(entity.getValue());
                        if (DataUtil.isNotEmpty(httpAppByAppUri)) {
                            update.set("appName", httpAppByAppUri.getName() != null ? httpAppByAppUri.getName() : "");
                            update.set("departments", httpAppByAppUri.getDepartments() != null ? httpAppByAppUri.getDepartments() : new ArrayList<>());
                        }
                    }
                }
                //补充rspDataDistinctCnt
                if (riskInfo.getDescription().containsKey("dataCnt")) {
                    update.set("rspDataDistinctCnt", riskInfo.getDescription().getLongValue("dataCnt"));
                }
                //补充rspLabelListCount
                if (DataUtil.isNotEmpty(riskInfo.getRspLabelList())) {
                    update.set("rspLabelListCount", riskInfo.getRspLabelList().size());
                } else {
                    update.set("rspLabelListCount", 0);
                }
                update.set("updateTime", System.currentTimeMillis());
                mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(riskInfo.getId())), update, collectionName);
            }
        } while (!riskInfos.isEmpty());

        log.info("update success,total{}", count);
    }

//    private void setAppOrApiInfo(Update update, RiskInfo.Entity entity) throws Exception {
//        if (entity.getType().equals("API")) {
//            update.set("apiUrl", entity.getValue().replaceAll("httpapi:", ""));
//            update.set("apiUri", entity.getValue());
//        } else if (entity.getType().equals("APP")) {
//            update.set("host", entity.getValue().replaceAll("httpapp:", ""));
//            update.set("appUri", entity.getValue());
//            HttpAppDto httpAppByAppUri = httpAppService.getHttpAppByAppUri(entity.getValue());
//            update.set("appName", httpAppByAppUri.getName());
//            update.set("departments", httpAppByAppUri.getDepartments());
//        }
//    }

}