package com.quanzhi.auditapiv2.biz.schedule.task.risk;

import com.mongodb.MongoNamespace;
import com.mongodb.client.MongoClient;
import com.quanzhi.audit_core.common.model.ApiCleanJobParam;
import com.quanzhi.auditapiv2.common.dal.config.MongodbConfig;
import com.quanzhi.auditapiv2.common.dal.mongoCollectionIndex.document.RiskSampleDocument;
import com.quanzhi.auditapiv2.core.model.event.RiskChangedEvent;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskSample;
import com.quanzhi.auditapiv2.core.trace.batch.AutoFlushBatch;
import com.quanzhi.auditapiv2.core.trace.batch.ConcurrentAutoFlushBatch;
import com.quanzhi.metabase.common.utils.task.IndexedProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.*;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class RiskClearService {
    public static final String RISK_SAMPLE = "riskSample";
    private final MongoTemplate mongoTemplate;
    private final MongoMappingContext mongoMappingContext;
    private final ApplicationEventPublisher eventPublisher;
    @Value("${audit.clear.risk.compact:true}")
    private boolean compact = true;
    @Value("${audit.clear.risk.count:10000000}")
    private long maxCount = 10000000;
    private static final int MAX_RISK_TEST_COUNT = 10;
    private static final String RISK_SAMPLE_TEMP_NAME = "riskSampleTemp";

    public void clearRisk(ApiCleanJobParam param) {
        // 查看风险样例数据量，如果数据量过大，要进行一次整理
        // long count = mongoTemplate.estimatedCount(RISK_SAMPLE);
        clearOld(param);
    }

    /**
     * 还不成熟，发现处理很慢，后面再排查
     * 另外还有一个新问题，如果直接往temp表插入数据，可能会导致磁盘满！！！
     * @param param
     */
    private void clearV2(ApiCleanJobParam param) {
        List<String> riskIds = new ArrayList<>();
        new IndexedProcessor<RiskInfo>() {
            @Override
            public void process(RiskInfo riskInfo) {
                List<Document> documents = mongoTemplate.find(Query.query(Criteria.where("riskId").is(riskInfo.getId())).limit(100), Document.class, RISK_SAMPLE);
                mongoTemplate.insert(documents, RISK_SAMPLE_TEMP_NAME);
                if (riskIds.size() < MAX_RISK_TEST_COUNT && new Random().nextInt(10) >= 8) {
                    riskIds.add(riskInfo.getId());
                }
            }

            @Override
            protected List<RiskInfo> fetch(String s, int i) {
                return getLastRiskInfos(param);
            }

            @Override
            protected String getIndex(List<RiskInfo> list) {
                return list.get(list.size() - 1).getId();
            }
        }.process();
        // 迁移完毕
        if (mongoTemplate.count(new Query(), RISK_SAMPLE_TEMP_NAME) > 0) {
            long size = riskIds.stream().filter(r -> mongoTemplate.exists(Query.query(Criteria.where("riskId").is(r)), RISK_SAMPLE_TEMP_NAME)).count();
            if (size >= riskIds.size() / 2) {
                drop();
                clearOld(param);
            } else {
                long originSize = riskIds.stream().filter(r -> mongoTemplate.exists(Query.query(Criteria.where("riskId").is(r)), RISK_SAMPLE)).count();
                if (size >= originSize) {
                    drop();
                    mongoTemplate.remove(Query.query(Criteria.where("firstTime").gte(param.getStartTimestamp()).lte(param.getEndTimestamp())).with(Sort.by(Sort.Order.asc("_id"))), RiskInfo.class);
                } else {
                    log.warn("too much risk have no sample. user clear old. newSize:{}, originSize:{}", size, originSize);
                    clearOld(param);
                }
            }
        }
        mongoTemplate.remove(Query.query(Criteria.where("firstTime").gte(param.getStartTimestamp()).lte(param.getEndTimestamp())), "aggRiskInfo");
        mongoTemplate.remove(Query.query(Criteria.where("firstTime").gte(param.getStartTimestamp()).lte(param.getEndTimestamp())), "riskInfo");
    }

    private void drop() {
        mongoTemplate.dropCollection(RISK_SAMPLE);
        //重试多次
        int count = 5;
        boolean success = false;
        while (count > 0) {
            try {
                mongoTemplate.getCollection(RISK_SAMPLE_TEMP_NAME).renameCollection(new MongoNamespace(RISK_SAMPLE));
                success = true;
                break;
            } catch (Exception e) {
                // rename失败，重复执行
                if (mongoTemplate.count(new Query(), RISK_SAMPLE_TEMP_NAME) > 0) {
                    mongoTemplate.dropCollection(RISK_SAMPLE);
                } else {
                    throw new IllegalStateException("rename fail ,but riskSampleTemp is missing");
                }
            }
            count--;
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                return;
            }
        }
        if (success) {
            //添加组合索引httpEvent.timestamp和_id的组合索引
            createIndex(RiskSampleDocument.class);
        }
    }

    private void clearOld(ApiCleanJobParam param) {
        try (AutoFlushBatch<RiskInfo> riskBatch = new ConcurrentAutoFlushBatch<>(list -> {
            mongoTemplate.remove(Query.query(Criteria.where("id").in(list.stream().map(RiskInfo::getId).collect(Collectors.toList()))), RiskInfo.class, "riskInfo");
            mongoTemplate.remove(Query.query(Criteria.where("riskId").in(list.stream().map(RiskInfo::getId).collect(Collectors.toList()))), RiskSample.class, "riskSample");
//            for (RiskInfo riskInfo : list) {
//                try {
//                    eventPublisher.publishEvent(new RiskChangedEvent(riskInfo));
//                } catch (Exception e) {
//                    log.error("publish event error", e);
//                }
//            }

        })) {
            new IndexedProcessor<RiskInfo>() {
                @Override
                public void process(RiskInfo riskInfo) {
                    riskInfo.setState(RiskInfo.RiskStateEnum.HAS_IGNORE.getState());
                    // 清理风险并清理所有对应样例
                    riskBatch.add(riskInfo);
                }

                @Override
                protected List<RiskInfo> fetch(String s, int i) {
                    return getRiskInfos(param);
                }

                @Override
                protected String getIndex(List<RiskInfo> list) {
                    return list.get(list.size() - 1).getId();
                }
            }.process();
        }
        // 避免某些未触发风险的样例存在，这里清理一下样例
        mongoTemplate.remove(Query.query(Criteria.where("createTime").gte(param.getStartTimestamp()).lte(param.getEndTimestamp())), "riskSample");
        mongoTemplate.remove(Query.query(Criteria.where("firstTime").gte(param.getStartTimestamp()).lte(param.getEndTimestamp())), "aggRiskInfo");

        if (compact) {
            Document document = new Document();
            document.put("compact", RISK_SAMPLE);
            mongoTemplate.executeCommand(document);
        }
    }

    private List<RiskInfo> getRiskInfos(ApiCleanJobParam param) {
        Query query = Query.query(Criteria.where("firstTime").gte(param.getStartTimestamp()).lte(param.getEndTimestamp())).with(Sort.by(Sort.Order.asc("_id")));
        return mongoTemplate.find(query, RiskInfo.class);
    }

    private List<RiskInfo> getLastRiskInfos(ApiCleanJobParam param) {
        Query query = Query.query(Criteria.where("firstTime").gt(param.getEndTimestamp())).with(Sort.by(Sort.Order.asc("_id")));
        return mongoTemplate.find(query, RiskInfo.class);
    }

    private void createIndex(Class<?> clz) {
        IndexOperations indexOps = mongoTemplate.indexOps(clz);
        IndexResolver resolver = new MongoPersistentEntityIndexResolver(mongoMappingContext);
        for (IndexDefinition next : resolver.resolveIndexFor(clz)) {
            try {
                indexOps.ensureIndex(next);
            } catch (Exception e) {
                log.error("ensure {} index {} error", clz.getName(), next.getIndexKeys(), e);
            }
        }
    }
}
