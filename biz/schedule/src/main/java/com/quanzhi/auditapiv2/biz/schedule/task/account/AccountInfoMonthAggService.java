package com.quanzhi.auditapiv2.biz.schedule.task.account;

import com.quanzhi.audit.mix.schdule.domain.repository.TaskProcessRepository;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.audit_core.common.model.RiskLevel;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.biz.schedule.task.AccountRiskRepository;
import com.quanzhi.auditapiv2.common.dal.dao.AccountInfoDao;
import com.quanzhi.auditapiv2.common.dal.entity.AccountInfo;
import com.quanzhi.auditapiv2.common.dal.entity.account.AbstractTarget;
import com.quanzhi.auditapiv2.common.dal.entity.account.AccountMonthStat;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.util.utils.DateFormat;
import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
import com.quanzhi.auditapiv2.core.model.event.BatchRiskChangedEvent;
import com.quanzhi.auditapiv2.core.model.event.RiskChangedEvent;
import com.quanzhi.auditapiv2.core.model.event.RiskInfoAddEvent;
import com.quanzhi.metabase.common.utils.DataUtil;
import com.quanzhi.metabase.core.model.dto.RiskLevelMatchDto;
import com.quanzhi.metabase.core.model.merge.Merger;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class AccountInfoMonthAggService extends AbstractMonthStatService<AccountInfo, AccountMonthStat> {

    private final AccountInfoDao accountInfoDao;

    private final AccountMerger accountMerger = new AccountMerger();

    private final AccountRiskRepository accountRiskRepository;

    @DynamicValue(dataId = "common.asset.life.state.config.json", groupId = "common", typeClz = AssetLifeStateConfig.class)
    @Setter
    private List<AssetLifeStateConfig> assetLifeStateConfigs;

    @DynamicValue(dataId = "common.risk.level.config.json", groupId = "common", typeClz = RiskLevel.class)
    private List<RiskLevel> riskLevelList;

    public AccountInfoMonthAggService(TaskProcessRepository taskProcessRepository, MongoTemplate mongoTemplate, AccountInfoDao accountInfoDao, AccountRiskRepository accountRiskRepository) {
        super(taskProcessRepository, mongoTemplate);
        this.accountInfoDao = accountInfoDao;
        this.accountRiskRepository = accountRiskRepository;
    }

    @Override
    protected AccountInfo getTarget(AccountMonthStat accountMonthStat) {
        AccountInfo accountInfo = accountInfoDao.getAccount(accountMonthStat.getAccount());
        return accountInfo == null ? AccountInfo.builder().account(accountMonthStat.getAccount()).id(UUID.randomUUID().toString()).build() : accountInfo;
    }

    @Override
    protected String getField() {
        return "account";
    }

    @Override
    protected void save(List<AccountInfo> list) {
        accountInfoDao.save(list);
    }

    @Override
    protected void process(AccountInfo accountInfo, AccountMonthStat accountMonthStat) {
        String lastDate = accountInfo.getLastDate();
        boolean create = DataUtil.isEmpty(lastDate);
        super.process(accountInfo, accountMonthStat);
        lastDate = create ? accountInfo.getLastDate() : lastDate;
        accountMerger.merge(accountInfo, accountMonthStat);
        AssetLifeStateConfig stateConfig = assetLifeStateConfigs == null ? null : assetLifeStateConfigs.stream().filter(config -> config.getAssetType() == AssetLifeStateConfig.AssetTypeEnum.ACCOUNT && Boolean.TRUE.equals(config.getReviveOpenFlag())).findFirst().orElse(null);
        checkAccountReviveState(stateConfig, accountInfo, lastDate);
        bindRisk(accountInfo, accountInfo.getLastDate(), create || accountInfo.getRiskLevel() == null || accountInfo.getRiskLevel() == 4);
        accountInfo.setUpdateTime(System.currentTimeMillis());
    }

    protected void checkAccountReviveState(AssetLifeStateConfig stateConfig, AbstractTarget target, String lastDate) {
        if (stateConfig == null) {
            return;
        }
        // 复活判断
        try {
            String curDate = target.getLastDate();
            Long eventTimestamp = null;
            if (DataUtil.isNotEmpty(curDate)) {
                eventTimestamp = DateFormat.date2TimeStamp(curDate, DateFormat.FORMAT_YMD);
            }
            //上次活跃时间
            Long preActiveTime;
            if (DataUtil.isNotEmpty(lastDate)) {
                preActiveTime = DateFormat.date2TimeStamp(lastDate, DateFormat.FORMAT_YMD);
            } else {
                return;
            }
            boolean reviveAdd = this.checkReviveAdd(stateConfig, preActiveTime == null ? eventTimestamp : preActiveTime);
            if (reviveAdd) {
                target.setReviveTime(eventTimestamp);
            }
        } catch (Exception e) {
            log.error("checkAccountReviveState error, id:[{}]", target.getId(), e);
        }
    }

    @EventListener(RiskChangedEvent.class)
    public void onEvent(RiskChangedEvent event) {
        AggRiskInfo riskInfo = event.getRiskInfo();
        bindRisk(riskInfo);
    }

    @EventListener(RiskInfoAddEvent.class)
    public void onEvent(RiskInfoAddEvent event) {
        bindRisk(event.getRiskInfo());
    }

    private void bindRisk(AggRiskInfo riskInfo) {
        if (riskInfo == null) {
            return;
        }
        try {
            if (riskInfo.getEntities() != null) {
                String acc = riskInfo.getEntities().stream().filter(e -> "ACCOUNT".equals(e.getType())).map(RiskInfo.Entity::getValue).findAny().orElse(null);
                if (acc == null) {
                    return;
                }
                AccountInfo accountInfo = accountInfoDao.getAccount(acc);
                if (accountInfo != null) {
                    bindRisk(accountInfo, null, true);
                    accountInfoDao.save(Collections.singletonList(accountInfo));
                }
            }
        } catch (Exception e) {
            log.error("bind risk error, riskId:[{}]", riskInfo.getId(), e);
        }
    }

    @EventListener(BatchRiskChangedEvent.class)
    public void onEvent(BatchRiskChangedEvent event) {
        List<AggRiskInfo> riskInfos = event.getRiskInfos();
        if (riskInfos != null) {
            for (AggRiskInfo riskInfo : riskInfos) {
                bindRisk(riskInfo);
            }
        }
    }

    private void bindRisk(AccountInfo accountInfo, String date, boolean refresh) {
        RiskLevelMatchDto.Risk risk = accountRiskRepository.getRiskInfoBy(accountInfo.getAccount(), date, refresh);
        if (risk != null) {
            accountInfo.setRiskInfo(risk);
            accountInfo.setRiskLevelName(DataUtil.isEmpty(risk.getLevelName()) ? "其他" : risk.getLevelName());
            accountInfo.setRiskLevel(DataUtil.isEmpty(risk.getRiskLevel()) ? 4 : risk.getRiskLevel());
        }
        if (riskLevelList != null) {
            for (RiskLevel riskLevel : riskLevelList) {
                if ("ACCOUNT".equals(riskLevel.getLevelType()) && riskLevel.getLevel().equals(accountInfo.getRiskLevel())) {
                    if (riskLevel.getEnableFlag() == null || !riskLevel.getEnableFlag()) {
                        accountInfo.setRiskLevel(4);
                        accountInfo.setRiskLevelName("其他");
                    }
                }
            }
        }
        if (accountInfo.getRiskLevel() == null) {
            accountInfo.setRiskLevel(4);
            accountInfo.setRiskLevelName("其他");
        }
    }

    public static final class AccountMerger extends Merger<AccountInfo> {
        @Override
        public void merge(AccountInfo target, Object source, String[] ignoreProperties) {
            super.merge(target, source, ignoreProperties);
            if (source instanceof AccountMonthStat) {
                AccountMonthStat sourceDateInfo = (AccountMonthStat) source;
                if (sourceDateInfo.getStaffInfo() != null) {
                    // 覆盖所有的组织架构数据
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffDepart())) {
                        target.setStaffDepart(sourceDateInfo.getStaffInfo().getStaffDepart());
                    }
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffId())) {
                        target.setStaffId(sourceDateInfo.getStaffInfo().getStaffId());
                    }
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffName())) {
                        target.setStaffName(sourceDateInfo.getStaffInfo().getStaffName());
                    }
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffBankCard())) {
                        target.setStaffBankCard(sourceDateInfo.getStaffInfo().getStaffBankCard());
                    }
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffMobile())) {
                        target.setStaffMobile(sourceDateInfo.getStaffInfo().getStaffMobile());
                    }
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffEmail())) {
                        target.setStaffEmail(sourceDateInfo.getStaffInfo().getStaffEmail());
                    }
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffChinese())) {
                        target.setStaffChinese(sourceDateInfo.getStaffInfo().getStaffChinese());
                    }
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffNickName())) {
                        target.setStaffNickName(sourceDateInfo.getStaffInfo().getStaffNickName());
                    }
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffIdCard())) {
                        target.setStaffIdCard(sourceDateInfo.getStaffInfo().getStaffIdCard());
                    }
                    if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffInfo().getStaffRole())) {
                        target.setStaffRole(sourceDateInfo.getStaffInfo().getStaffRole());
                    }
                }
            }
            target.setRspDataLabelCnt(target.getRspDataLabelList() == null ? 0L : target.getRspDataLabelList().size());
            target.setRelatedIpDistinctCnt(target.getRelatedIpList() == null ? 0L : target.getRelatedIpList().size());
        }
    }
}
