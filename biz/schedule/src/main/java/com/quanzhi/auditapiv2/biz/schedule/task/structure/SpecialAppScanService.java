package com.quanzhi.auditapiv2.biz.schedule.task.structure;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit_core.common.model.HistoryDataCleanTask;
import com.quanzhi.auditapiv2.common.dal.dao.IHistoryDataCleanDao;
import com.quanzhi.auditapiv2.common.dal.dao.IXxlJobDao;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.dal.dto.filter.EnableFilterDto;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.ConstantUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpApiService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.auditapiv2.core.service.manager.web.ISampleEventService;
import com.quanzhi.auditapiv2.core.service.manager.web.rule.ISmartFilterRuleService;
import com.quanzhi.metabase.core.model.filter.SmartFilterRule;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.query.SortOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * create at 2021/12/31 11:43 上午
 * @description:
 **/
@Service
@Slf4j
public class SpecialAppScanService {

    @Autowired
    private IHttpAppService httpAppService;

    @Autowired
    private IHttpApiService httpApiService;

    @Autowired
    private ISampleEventService sampleEventService;

    @Autowired
    private ISmartFilterRuleService smartFilterRuleService;

   /* @Autowired
    private IApiWeaknessService httpApiWeaknessService;*/

    @Autowired
    private IXxlJobDao xxlJobDao;

    @Autowired
    private IHistoryDataCleanDao historyDataCleanDao;


    public SpecialAppScanService() {
    }

    /**
     * 开始扫描
     */
    public void scan() {
        for (int page = 1; page < 10; page++) {
            try {
                ListOutputDto<HttpAppDto> httpAppList = httpAppService.getHttpAppList(new HashMap<>(), "appStat.allTypeApiCount", SortOrder.DESC.code, page, 10);
                if (httpAppList.getTotalCount() == 0) {
                    break;
                }
                boolean isBreak = false;
                for (HttpAppDto httpAppDto : httpAppList.getRows()) {
                    if (httpAppDto.getAppStat().getApiCount() < 50) {
                        isBreak = true;
                        break;
                    }
                    SmartFilterRule matchRule = checkIsAppGitLab(httpAppDto.getHost());
                    ;
                    // maven的路径判断
                    if (matchRule == null) {
                        matchRule = checkIsAppMaven(httpAppDto.getHost());
                    }
                    // minio的路径判断
                    if (matchRule == null) {
                        matchRule = checkIsAppMinio(httpAppDto.getHost());
                    }
                    if (matchRule != null) {
                        boolean exist = smartFilterRuleService.isRuleExist(matchRule);
                        if (exist) {
                            continue;
                        }
                        SmartFilterRule saveSmartFilterRule = smartFilterRuleService.saveSmartFilterRule(matchRule);
                        if (saveSmartFilterRule == null) {
                            continue;
                        }
                        //下发清理任务
                        HistoryDataCleanTask historyDataCleanTask = new HistoryDataCleanTask();
                        historyDataCleanTask.setName("智能过滤:"+saveSmartFilterRule.getHost()+"-"+saveSmartFilterRule.getCompareValue());
                        historyDataCleanTask.setCreateTime(System.currentTimeMillis());
                        historyDataCleanTask.setStartTime(System.currentTimeMillis());
                        historyDataCleanTask.setStatus(HistoryDataCleanTask.TaskStatusEnum.NEW.getValue());
                        historyDataCleanTask.setParams(JSON.toJSONString(saveSmartFilterRule));
                        HistoryDataCleanTask taskSave = historyDataCleanDao.save(null, historyDataCleanTask);
                        EnableFilterDto enableFilterDto = new EnableFilterDto();
                        enableFilterDto.setTaskId(taskSave.getId());
                        enableFilterDto.setFilterType(EnableFilterDto.FilterType.SMART);
                        enableFilterDto.setIds(Arrays.asList(saveSmartFilterRule.getId()));
                        xxlJobDao.triggerFilterRuleJob(enableFilterDto);
                        log.info("add a smartFilter Rule:{}",saveSmartFilterRule.toString());

                      /*  // 对现有接口进行清理
                        int maxCleanApiPage = 1000;
                        HttpApiSearchDto apiSearchDto = new HttpApiSearchDto();
                        apiSearchDto.setHost(httpAppDto.getHost());
                        for (int i = 1; i < maxCleanApiPage; i++) {
                            ListOutputDto<HttpApiResource> clearApiList = httpApiService.getHttpApis(i, 10, "apiStat.totalVisits", ConstantUtil.Sort.ASC, apiSearchDto);
                            if (CollectionUtils.isEmpty(clearApiList.getRows())) {
                                break;
                            }
                            for (HttpApiResource httpApiResource : clearApiList.getRows()) {
                                if (httpApiResource.getApiStat().getTotalVisits() > 20) {
                                    break;
                                }
                                ListOutputDto<HttpApiSample> sampleListResult = sampleEventService.getHttpEventSampleList(1, 2, httpApiResource.getUri());
                                int filterCount = 0;
                                for (HttpApiSample sample : sampleListResult.getRows()) {
                                    if (SmartFilterUtil.checkEventFilter(sample, matchRule)) {
                                        filterCount += 1;
                                    }
                                }
                                // 如果样例都是可以过滤的，将接口删除
                                if (filterCount == sampleListResult.getRows().size()) {
                                    Map<String, Object> updateMap = new HashMap<>();
                                    updateMap.put("delFlag", true);
                                    httpApiService.updateHttpApi(httpApiResource.getId(), updateMap);
                                    httpApiWeaknessService.updateHttpApiWeakness(httpApiResource.getUri(), updateMap);
                                }
                            }
                        }*/

                    }
                }
                if (isBreak) {
                    break;
                }
            } catch (Exception e) {

            }
        }
    }

    private SmartFilterRule checkIsAppGitLab(String host) {
        boolean match = false;
        String appBasePath = "/";
        try {
            HttpApiSearchDto apiSearchDto = new HttpApiSearchDto();
            apiSearchDto.setHost(host);
            apiSearchDto.setApiUrl("/blob/");
            ListOutputDto<HttpApiResource> apiList = httpApiService.getHttpApis(1, 1, "appStat.totalVisits", SortOrder.ASC.code, apiSearchDto);
            // 找到了gitlab的目录，找个样例看一下
            if (CollectionUtils.isNotEmpty(apiList.getRows())) {
                HttpApiResource httpApi = apiList.getRows().get(0);
                ListOutputDto<HttpApiSample> sampleListOutputDto = sampleEventService.getHttpEventSampleList(1, 1, httpApi.getUri());
                if (CollectionUtils.isNotEmpty(sampleListOutputDto.getRows())) {
                    for (HttpApiSample httpApiSample : sampleListOutputDto.getRows()) {
                        String rspBody = httpApiSample.getRsp().getBody();
                        if (rspBody != null) {
                            rspBody = rspBody.trim();
                            if (rspBody.startsWith("{") && rspBody.endsWith("}")) {
                                JSONObject jsonObject = JSONObject.parseObject(rspBody);
                                if (jsonObject.containsKey("commits_path")) {
                                    match = true;
                                    try {
                                        String commitsPath = jsonObject.getString("commits_path");
                                        String fileDir = commitsPath.substring(0, commitsPath.indexOf("/commits"));
                                        String url = httpApiSample.getReq().getUrl();
                                        URL urlInfo = new URL(url);
                                        if (urlInfo.getPath().indexOf(fileDir) > 0) {
                                            appBasePath = urlInfo.getPath().substring(0, urlInfo.getPath().indexOf(fileDir));
                                        } else {
                                            appBasePath = "/";
                                        }
                                    } catch (Exception e) {

                                    }
                                }
                            } else if (rspBody.startsWith("<!DOCTYPE html>")) {
                                Pattern pattern = Pattern.compile("<meta content=\"(.*)\" property=\"og:image\">", Pattern.MULTILINE);
                                Matcher matcher = pattern.matcher(rspBody);
                                if (matcher.matches()) {
                                    try {
                                        String iconUrl = matcher.group(1);
                                        URL iconUrlInfo = new URL(iconUrl);
                                        String iconPath = iconUrlInfo.getPath();
                                        if (iconPath.startsWith("/assets")) {
                                            appBasePath = "/";
                                        } else {
                                            appBasePath = iconPath.substring(0, iconPath.indexOf("/assets"));
                                        }
                                    } catch (Exception e) {

                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
        }
        if (match) {
            SmartFilterRule matchRule = new SmartFilterRule();
            matchRule.setHost(host);
            matchRule.setTarget("url");
            matchRule.setTargetOp("urlpath_after:" + appBasePath);
            matchRule.setCompareOp("not_start");
            matchRule.setCompareValue("/users");
            matchRule.setMatchRate(0);
            matchRule.setEnable(true);
            matchRule.setDelFlag(false);
            matchRule.setCreateTime(System.currentTimeMillis());
            matchRule.setUpdateTime(System.currentTimeMillis());
            return matchRule;
        }
        return null;
    }

    private SmartFilterRule checkIsAppMaven(String host) {
        boolean match = false;
        String appBasePath = "/";
        try {
            HttpApiSearchDto apiSearchDto = new HttpApiSearchDto();
            apiSearchDto.setHost(host);
            apiSearchDto.setApiUrl("/service/local/repositories");
            ListOutputDto<HttpApiResource> apiList = httpApiService.getHttpApis(1, 1, "apiStat.totalVisits", SortOrder.ASC.code, apiSearchDto);
            if (CollectionUtils.isNotEmpty(apiList.getRows())) {
                HttpApiResource httpApi = apiList.getRows().get(0);
                ListOutputDto<HttpApiSample> sampleListOutputDto = sampleEventService.getHttpEventSampleList(1, 1, httpApi.getUri());
                if (CollectionUtils.isNotEmpty(sampleListOutputDto.getRows())) {
                    for (HttpApiSample httpApiSample : sampleListOutputDto.getRows()) {
                        String rspBody = httpApiSample.getRsp().getBody();
                        if (rspBody != null) {
                            rspBody = rspBody.trim();
                            if (rspBody.startsWith("{") && rspBody.endsWith("}")) {
                                try {
                                    JSONObject jsonObject = JSON.parseObject(rspBody);
                                    if (jsonObject.containsKey("data")) {
                                        match = true;
                                        try {
                                            String url = httpApiSample.getReq().getUrl();
                                            URL urlInfo = new URL(url);
                                            appBasePath = urlInfo.getPath().substring(0, urlInfo.getPath().indexOf("/service"));
                                        } catch (Exception e) {

                                        }
                                    }
                                } catch (Exception e) {

                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
        }
        if (match) {
            SmartFilterRule matchRule = new SmartFilterRule();
            matchRule.setHost(host);
            matchRule.setTarget("url");
            matchRule.setTargetOp("urlpath_after:" + appBasePath);
            matchRule.setCompareOp("not_contain");
            matchRule.setCompareValue("/service/local/authentication");
            matchRule.setMatchRate(0);
            matchRule.setEnable(true);
            matchRule.setDelFlag(false);
            matchRule.setCreateTime(System.currentTimeMillis());
            matchRule.setUpdateTime(System.currentTimeMillis());
            return matchRule;
        }
        return null;
    }

    private SmartFilterRule checkIsAppMinio(String host) {
        boolean match = false;
        String baseAppPath = "/";
        try {
            HttpApiSearchDto apiSearchDto = new HttpApiSearchDto();
            apiSearchDto.setHost(host);
            apiSearchDto.setApiUrl("/webrpc");
            ListOutputDto<HttpApiResource> apiList = httpApiService.getHttpApis(1, 1, "apiStat.totalVisits", SortOrder.ASC.code, apiSearchDto);
            if (CollectionUtils.isNotEmpty(apiList.getRows())) {
                String apiPath = apiList.getRows().get(0).getApiUrl();
                try {
                    URL urlInfo = new URL(apiPath);
                    baseAppPath = urlInfo.getPath().substring(0, urlInfo.getPath().indexOf("/webrpc"));
                } catch (Exception e) {

                }
                apiSearchDto.setApiUrl(null);
                apiSearchDto.setRspContentTypes(Arrays.asList("html"));
                apiList = httpApiService.getHttpApis(1, 1, "appStat.apiCount", SortOrder.ASC.code, apiSearchDto);
                if (CollectionUtils.isNotEmpty(apiList.getRows())) {
                    HttpApiResource httpApi = apiList.getRows().get(0);
                    ListOutputDto<HttpApiSample> sampleListOutputDto = sampleEventService.getHttpEventSampleList(1, 1, httpApi.getUri());
                    if (CollectionUtils.isNotEmpty(sampleListOutputDto.getRows())) {
                        for (HttpApiSample httpApiSample : sampleListOutputDto.getRows()) {
                            if (httpApiSample.getRsp().getBody().startsWith("<!DOCTYPE html>") && httpApiSample.getRsp().getBody().contains("<title>MinIO Browser</title>")) {
                                match = true;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
        }
        if (match) {
            SmartFilterRule matchRule = new SmartFilterRule();
            matchRule.setHost(host);
            matchRule.setTarget("url");
            matchRule.setTargetOp("urlpath_after:" + baseAppPath);
            matchRule.setCompareOp("not_contain");
            matchRule.setCompareValue("/webrpc");
            matchRule.setMatchRate(0);
            matchRule.setEnable(true);
            matchRule.setDelFlag(false);
            matchRule.setCreateTime(System.currentTimeMillis());
            matchRule.setUpdateTime(System.currentTimeMillis());
            return matchRule;
        }
        return null;
    }
}