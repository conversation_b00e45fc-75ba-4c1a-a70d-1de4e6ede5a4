//package com.quanzhi.auditapiv2.biz.schedule.task;
//
//import com.quanzhi.audit_core.common.adaptive.AdaptiveCounting;
//import com.quanzhi.auditapiv2.common.dal.entity.AccountDateInfo;
//import com.quanzhi.auditapiv2.common.dal.entity.AccountInfo;
//import com.quanzhi.auditapiv2.common.dal.entity.IpDateInfo;
//import com.quanzhi.auditapiv2.common.dal.entity.IpInfo;
//import com.quanzhi.metabase.common.utils.ReflectUtil;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.ArrayList;
//import java.util.Collection;
//import java.util.List;
//import java.util.Optional;
//import java.util.stream.Collectors;
//import java.util.stream.Stream;
//
///**
// * <AUTHOR>
// * @date 2021/8/2 9:50 AM
// */
//@Slf4j
//public class IpAccountMerge {
//
//    public static void mergeLastDate(IpDateInfo source, IpInfo target) {
//
//        target.setLastDate( source.getDate() );
//    }
//
//    public static void mergeMaxRspDisCnt( IpDateInfo source, IpInfo target) {
//
//        try {
//            if( source.getMaxRspDataDistinctCnt() != null ) {
//
//                if (target.getMaxRspDataDistinctCnt() == null || source.getMaxRspDataDistinctCnt() > target.getMaxRspDataDistinctCnt()) {
//
//                    target.setMaxRspDataDistinctCnt( source.getMaxRspDataDistinctCnt() );
//                    target.setMaxRspDataDistinctCntDate( source.getDate() );
//                }
//            }
//        } catch (Exception e) {
//
//            log.error("mergeMaxRspDisCnt error",e);
//        }
//    }
//
//    public static void getRelatedAccountDistinctCnt(IpInfo target) {
//        if (target.getRelatedAccountList() != null) {
//            target.setRelatedAccountDistinctCnt(new Long(target.getRelatedAccountList().size()));
//        } else {
//            target.setRelatedAccountDistinctCnt(0L);
//        }
//
////        if(target.getRelatedAccountBucket() != null) {
////
////            target.setRelatedAccountDistinctCnt(
////                    target.getRelatedAccountBucket().cardinality()
////            );
////        } else {
////            target.setRelatedAccountDistinctCnt(0L);
////        }
//
//    }
//
//    public static void getRspDistinctCnt(IpInfo target) {
//
//        if(target.getRspDataBucket() != null) {
//
//            target.setRspDataDistinctCnt(
//                    target.getRspDataBucket().cardinality()
//            );
//        }
//
//    }
//
//    public static void mergeLastDate(AccountDateInfo source, AccountInfo target) {
//        if (source.getDate() == null) {
//            return;
//        }
//        if (target.getLastDate() == null || target.getLastDate().compareTo(source.getDate()) < 0) {
//            target.setLastDate( source.getDate() );
//        }
//    }
//
//    public static void mergeMaxRspDisCnt( AccountDateInfo source, AccountInfo target) {
//
//        try {
//
//            if( source.getMaxRspDataDistinctCnt() != null ) {
//
//                if (target.getMaxRspDataDistinctCnt() == null || source.getMaxRspDataDistinctCnt() > target.getMaxRspDataDistinctCnt()) {
//
//                    target.setMaxRspDataDistinctCnt( source.getMaxRspDataDistinctCnt() );
//                    target.setMaxRspDataDistinctCntDate( source.getDate() );
//                }
//            }
//        } catch (Exception e) {
//            log.error("mergeMaxRspDisCnt error",e);
//        }
//    }
//
//    public static void getRelatedIpDistinctCnt(AccountInfo target) {
//        if (target.getRelatedIpList() != null) {
//            target.setRelatedIpDistinctCnt(new Long(target.getRelatedIpList().size()));
//        } else {
//            target.setRelatedIpDistinctCnt(0L);
//        }
////        if(target.getRelatedIpBucket() != null) {
////
////            target.setRelatedIpDistinctCnt(
////                    target.getRelatedIpBucket().cardinality()
////            );
////        } else {
////            target.setRelatedIpDistinctCnt(0L);
////        }
//
//    }
//
//    /**
//     * 普通字段进行更新
//     * @param field
//     * @param source
//     * @param target
//     */
//    public static void mergeNormalField(String field,Object source,Object target) {
//
//        if(  ReflectUtil.getValue(source,field) != null) {
//
//            ReflectUtil.setValue(target,field, ReflectUtil.getValue(source,field));
//        }
//    }
//
//
//    /**
//     * 更新桶数据
//     * @param field
//     * @param source
//     */
//    public static void mergeFieldBucket(String field,Object source_today,Object source,Object target) {
//
//        try {
//            AdaptiveCounting oldVal = (AdaptiveCounting) ReflectUtil.getValue(source,field);
//
//            AdaptiveCounting newVal = (AdaptiveCounting) ReflectUtil.getValue(source_today,field);
//
//            if(newVal != null) {
//
//
//                if(oldVal == null) {
//
//                    ReflectUtil.setValue(target,field,newVal);
//
//                } else {
//
//                    ReflectUtil.setValue(target,
//                            field,
//                            AdaptiveCounting.mergeAll( Stream.of(oldVal,newVal).collect(Collectors.toList()) )
//                    );
//                }
//            }
//        } catch (Exception e) {
//
//            log.error("mergeFieldBucket error",e);
//        }
//
//    }
//
//    /**
//     * 更新数字
//     * @param field
//     * @param source
//     */
//    public static void mergeFieldCnt(String field, Object source_today,Object source,Object target) {
//
//        try {
//
//            ReflectUtil.setValue(target,field,
//                    Optional.ofNullable( (Long) (  ReflectUtil.getValue(source_today,field) ) ).orElse(0L)  +
//                            Optional.ofNullable( (Long) ReflectUtil.getValue(source,field) ).orElse(0L));
//
//        } catch (Exception e) {
//
//            log.error("mergeFieldCnt error",e);
//        }
//    }
//
//
//    /**
//     * 列表数据去重
//     * @param field
//     * @param source
//     */
//    public static void mergeFieldSet(String field, Object source_today, Object source,Object target) {
//
//        try {
//
//            ReflectUtil.setValue(target,field,
//                    Stream.of(
//                            Optional.ofNullable( (List) ReflectUtil.getValue(source_today,field) ).orElse(new ArrayList<>()),
//                            Optional.ofNullable( (List) ReflectUtil.getValue(source,field) ).orElse(new ArrayList<>()))
//                            .flatMap(Collection::stream).distinct()
//                            .collect(Collectors.toList()));
//
//        } catch (Exception e) {
//
//            log.error("mergeFieldSet error",e);
//        }
//    }
//}
