package com.quanzhi.auditapiv2.biz.schedule.task.ip;

import com.quanzhi.audit.mix.schdule.domain.repository.TaskProcessRepository;
import com.quanzhi.auditapiv2.biz.schedule.task.IpInfoTask;
import com.quanzhi.auditapiv2.biz.schedule.task.account.AbstractMonthStatService;
import com.quanzhi.auditapiv2.common.dal.dao.IIpInfoDao;
import com.quanzhi.auditapiv2.common.dal.entity.IpInfo;
import com.quanzhi.auditapiv2.common.dal.entity.ip.IPMonthStat;
import com.quanzhi.metabase.core.model.merge.Merger;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class IPInfoMonthAggService extends AbstractMonthStatService<IpInfo, IPMonthStat> {

    private final String IP_INFO_COLLECTION = "ipInfo";

    private final IPInfoMerge merge = new IPInfoMerge();

    private final IIpInfoDao iIpInfoDao;

    private final IpInfoTask ipInfoTask;

    public IPInfoMonthAggService(TaskProcessRepository taskProcessRepository, MongoTemplate mongoTemplate, IIpInfoDao iIpInfoDao, IpInfoTask ipInfoTask) {
        super(taskProcessRepository, mongoTemplate);
        this.iIpInfoDao = iIpInfoDao;
        this.ipInfoTask = ipInfoTask;
    }

    @Override
    protected IpInfo getTarget(IPMonthStat ipMonthStat) {
        IpInfo info = mongoTemplate.findOne(new Query(Criteria.where("ip").is(ipMonthStat.getIp())), IpInfo.class, IP_INFO_COLLECTION);
        return info == null ? IpInfo.builder().ip(ipMonthStat.getIp()).id(UUID.randomUUID().toString()).build() : info;
    }

    @Override
    protected String getField() {
        return "ip";
    }

    @Override
    protected void save(List<IpInfo> list) {
        iIpInfoDao.save(list);
    }

    @Override
    protected void process(IpInfo ts, IPMonthStat ipMonthStat) {
        super.process(ts, ipMonthStat);
        ipInfoTask.fillIpInfo(ts);
        merge.merge(ts, ipMonthStat);
        ts.setUpdateTime(System.currentTimeMillis());
        // 多节点同步中，将多个节点的数据进行累加为总访问量
    }

    public static final class IPInfoMerge extends Merger<IpInfo> {
        @Override
        public void merge(IpInfo target, Object source) {
            super.merge(target, source);
            target.setRspDataLabelCnt(target.getRspDataLabelList() == null ? 0L : target.getRspDataLabelList().size());
            target.setRelatedAccountDistinctCnt(target.getRelatedAccountList() == null ? 0L : target.getRelatedAccountList().size());
            if (source instanceof IPMonthStat) {
                IPMonthStat stat = (IPMonthStat) source;
                target.setCity(stat.getCity());
                target.setCountry(stat.getCountry());
                target.setProvince(stat.getProvince());
            }
        }
    }
}
