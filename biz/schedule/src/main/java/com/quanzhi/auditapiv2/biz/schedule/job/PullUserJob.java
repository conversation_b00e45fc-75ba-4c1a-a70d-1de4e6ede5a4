package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.SHA256Util;
import com.quanzhi.auditapiv2.core.service.SysUserService;
import com.quanzhi.auditapiv2.openapi.sdk.util.HttpsUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: K, 小康
 * @Date: 2024/09/29/上午10:45
 * @Description:
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "product.type",  havingValue = "tyy")
public class PullUserJob {
    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    @NacosValue(value = "${tyy.userLogin.appKey:appKey}", autoRefreshed = true)
    private String appKey;
    @NacosValue(value = "${tyy.userLogin.appCode:appCode}", autoRefreshed = true)
    private String appCode;
    @NacosValue(value = "${tyy.userLogin.appSecret:appSecret}", autoRefreshed = true)
    private String appSecret;
    @NacosValue(value = "${tyy.userLogin.eopAk:eopAk}", autoRefreshed = true)
    private String eopAk;
    @NacosValue(value = "${tyy.userLogin.eopSk:eopSk}", autoRefreshed = true)
    private String eopSk;
    @NacosValue(value = "${tyy.userLogin.userListUrl:url}", autoRefreshed = true)
    private String url;
    @NacosValue(value = "${tyy.userLogin.password:password}", autoRefreshed = true)
    private String password;

    private final SysUserService sysUserService;

    public static OkHttpClient okHttpClient = null;

    public PullUserJob(SysUserService sysUserService) {
        this.sysUserService = sysUserService;
    }

    @LockedScheduler(cron = "0 1 * * * ?", executor = "PullUserJob", description = "天翼云获取用户信息")
    public void execute() throws Exception {
        if (productType.equals(ProductTypeEnum.tyy.name())){
            doJob();
        }
    }

    public static OkHttpClient createOkHttpClient(){
        if (okHttpClient == null){
            okHttpClient = (HttpsUtils.getTrustAllClientBuilder())
                    .connectTimeout(30L, TimeUnit.SECONDS)
                    .readTimeout(30L, TimeUnit.SECONDS)
                    .build();
        }
        return okHttpClient;
    }

    public void doJob(){
        OkHttpClient okHttpClient = createOkHttpClient();

        long timeMillis = System.currentTimeMillis();
        long time = timeMillis - 1000 * 60 * 60;

        String reqUrl = url +"?timestamp="+time;
        String timestamp = String.valueOf(timeMillis);
        String signature = "timestamp="+time
                +"&x-uias-algorithm=SHA256"
                +"&x-uias-appkey="+appKey
                +"&x-uias-timestamp="+timestamp
                +appSecret;
        Request request = new Request.Builder()
                .get()
                .addHeader("x-uias-algorithm","SHA256")
                .addHeader("x-uias-appkey",appKey)
                .addHeader("x-uias-timestamp",timestamp)
                .addHeader("ctyun-eop-ak",eopAk)
                .addHeader("ctyun-eop-sk",eopSk)
                .addHeader("signature", SHA256Util.getSHA256Hash(signature))
                .url(reqUrl)
                .build();
        String string = "";
        try {
            string = okHttpClient.newCall(request).execute().body().string();
            log.info("请求天翼云查询应用账号列表:{}，结果：{} , signature{}",reqUrl,string,signature);
            //5.4 foreignUserId、openId、operationType
            if (DataUtil.isNotEmpty(string)){
                JSONObject jsonObject = JSONObject.parseObject(string);
                Object data = jsonObject.get("data");
                if (data instanceof  JSONArray){
                    JSONArray jsonArray = jsonObject.getJSONArray("data");
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject userInfo = jsonArray.getJSONObject(i);
                        String username = userInfo.getString("foreignUserId");
                        String openId = userInfo.getString("openId");
                        String operationType = userInfo.getString("operationType");
                        insertUser(username,openId,operationType);
                    }
                }else if (data instanceof List){
                    List list = (List) data;
                    for (Object o : list) {
                        JSONObject userInfo = (JSONObject)o;
                        String username = userInfo.getString("foreignUserId");
                        String openId = userInfo.getString("openId");
                        String operationType = userInfo.getString("operationType");
                        insertUser(username,openId,operationType);
                    }
                }
            }
        }catch (Exception e){
            log.error("请求天翼云查询应用账号列表:{}，结果：{} , signature{}, 失败原因：",reqUrl,string,signature,e);
        }
    }

    private void insertUser(String username,String openId,String operationType){
        if (DataUtil.isEmpty(username) || DataUtil.isEmpty(openId) || DataUtil.isEmpty(operationType)){
            return;
        }
        if ("create".equals(operationType) || "update".equals(operationType) || "disable".equals(operationType)){
            sysUserService.saveSysUser(username,password,openId);
        }
        if ("delete".equals(operationType)){
            sysUserService.removeByOpenId(openId);
        }


    }
}
