package com.quanzhi.auditapiv2.biz.schedule.job;

import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.biz.risk.service.DefinedEventService;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.risk.dto.common.RiskDescribeDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class RiskDescribeScheduleJob {
    private Logger logger = LoggerFactory.getLogger(RiskDescribeScheduleJob.class);

    //风险事件
    @Autowired
    private RiskInfoService riskInfoService;

    //风险事件CK
    @Autowired
    private DefinedEventService definedEventService;

    //应用service
    @Autowired
    private IHttpAppService httpAppServiceImpl;

    /**
     * 执行定时任务
     * @return
     * @throws Exception
     */
    @LockedScheduler(cron = "0 0 0/2 * * ?", executor = "riskDescribeScheduleJob", description = "聚合统计风险描述数据")
    public void execute() {
        logger.warn("聚合统计风险描述数据");
    }

    /**
     * 处理任务
     */
    public void doJob() throws Exception {

        try {
            
            //获取当前时间
            Long date = DateUtil.getTime2(DateUtil.getDateTime(DateUtil.DATE_PATTERN.YYYYMMDD));
            //开始时间
            Long startDate = date - 86400000 * 3;
            //结束时间
            Long endDate = System.currentTimeMillis();

            logger.warn("统计风险事件时间段：" + DateUtil.tm2time(startDate) + " - " + DateUtil.tm2time(endDate));
            //设置查询条件
            RiskSearchDto riskSearchDto = new RiskSearchDto();
            riskSearchDto.setFirstTimeStart(startDate);
            riskSearchDto.setFirstTimeEnd(endDate);
            List<RiskInfoDto> list = riskInfoService.getRiskInfoDtoList(riskSearchDto);
            for (RiskInfoDto riskInfoDto : list) {
                try {

                    RiskInfoDto riskInfo = new RiskInfoDto();
                    //风险id
                    riskInfo.setId(riskInfoDto.getId());

                    if(DataUtil.isEmpty(riskInfoDto.getVersion())) {

                        //获取风险描述
                        String sql = riskInfoService.getSql(riskInfoDto.getId());
                        RiskDescribeDto riskDescribeDto = definedEventService.getDescribeData(riskInfoDto, sql);

                        //同步风险描述
                        if(DataUtil.isNotEmpty(riskDescribeDto)) {
                            //风险描述
                            riskInfo.setRiskDescribe(riskDescribeDto);
                        }
                    }

                    //同步部门
                    if(DataUtil.isNotEmpty(riskInfoDto.getBizFields()) && DataUtil.isNotEmpty(riskInfoDto.getBizFields().get("appUri"))) {

                        Map<String, HttpAppResource.Department> departmentMap = new LinkedHashMap<String, HttpAppResource.Department>();
                        List<String> appUriList = riskInfoDto.getBizFields().get("appUri");
                        for (String appUri : appUriList) {

                            HttpAppDto httpAppDto = httpAppServiceImpl.getHttpAppByAppUri(appUri);
                            if(DataUtil.isNotEmpty(httpAppDto) && DataUtil.isNotEmpty(httpAppDto.getDepartments())) {

                                for (HttpAppResource.Department department : httpAppDto.getDepartments()) {

                                    departmentMap.put(department.getDepartment(), department);
                                }
                            }
                        }

                        //部门
                        List<HttpAppResource.Department> departmentList = new ArrayList<HttpAppResource.Department>() {
                            {
                                addAll(departmentMap.values());
                            }
                        };
                        riskInfo.setDepartments(departmentList);
                    }

                    if(DataUtil.isNotEmpty(riskInfo.getRiskDescribe()) || DataUtil.isNotEmpty(riskInfo.getDepartments())) {

                        riskInfoService.editRiskInfo(riskInfo);
                    }
                } catch (Exception e) {
                    logger.error("风险：" + riskInfoDto.getId(), e);
                }
            }
        } catch (Exception e) {
            logger.error("聚合统计风险描述数据", e);
            HttpResponseUtil.error(e.getMessage());
        }
    }
}
