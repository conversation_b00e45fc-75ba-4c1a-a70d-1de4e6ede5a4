package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.metabase.core.model.enums.OrderFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create at 2022/7/26 5:12 下午
 * @description:
 **/
@Component
@Slf4j
public class RiskOrderUpdate implements UpgradeService {

    @Autowired
    private  MongoTemplate mongoTemplate;

    @Override
    public int getVersion() {
        return 20220726;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
     //将一些orderFlag为null的历史数据赋值
        Query query=new Query();
        query.addCriteria(Criteria.where("orderFlag").is(null));
        Update update=new Update();
        update.set("orderFlag", OrderFlagEnum.ORDER_INIT.getCode());
        mongoTemplate.updateMulti(query,update,"riskInfo");
        log.info("风险建单状态值升级");
    }
}