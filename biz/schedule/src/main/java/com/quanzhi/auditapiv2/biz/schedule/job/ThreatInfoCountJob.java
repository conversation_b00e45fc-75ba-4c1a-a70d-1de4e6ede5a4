package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatIpService;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.AggRiskService;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.RiskV2Service;
import com.quanzhi.auditapiv2.common.dal.dto.aggRisk.RiskV2OperatorDto;
import com.quanzhi.auditapiv2.common.dal.dto.schedule.ScheduleLogDto;
import com.quanzhi.auditapiv2.common.dal.dto.schedule.ScheduleSearchDto;
import com.quanzhi.auditapiv2.common.dal.enums.NacosSingleKeyConfigEnum;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatInfo;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatIp;
import com.quanzhi.auditapiv2.core.service.manager.web.INacosSingleKeyUpdateService;
import com.quanzhi.auditapiv2.core.service.schedule.AuditScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;

/**
 * <AUTHOR>
 * create at 2021/8/12 5:40 下午
 * @description: 威胁Ip表信息统计
 **/
@Component
@Slf4j
public class ThreatInfoCountJob {

    @NacosValue(value = "${schedule.count.num:500000}", autoRefreshed = true)
    private Long scheduleCountNum;

    private final AuditScheduleService auditScheduleService;

    private final AggRiskService aggRiskService;

    @Autowired
    private ThreatIpService threatIpService;

    private final ThreatInfoService threatInfoService;

    @Autowired
    private INacosSingleKeyUpdateService nacosSingleKeyUpdateService;

    @Autowired
    private MongoTemplate mongoTemplate;

    public ThreatInfoCountJob(AuditScheduleService auditScheduleService, AggRiskService aggRiskService, ThreatInfoService threatInfoService) {
        this.auditScheduleService = auditScheduleService;
        this.aggRiskService = aggRiskService;
        this.threatInfoService = threatInfoService;
    }

    @LockedScheduler(cron = "0 0/30 * * * ?", executor = "threatInfoCountJob", name = "威胁信息统计", description = "统计存在关联风险的IP和账号数据",
            interval = 1000 * 60 * 60 * 24, intervalConditionSpEL = "#root.check()"
    )
    public void execute() throws Exception {
        doJob();
    }

    public boolean check() {
        long riskCount = aggRiskService.totalCount();
        return riskCount > scheduleCountNum;
    }

    /**
     * 威胁ip数据统计
     */
    public void doJob() throws Exception {
        log.info("开始进行威胁信息统计,{}", DateUtil.currentDateTime());
        StopWatch watch = new StopWatch();
        watch.start("threatInfo-task");
        //检查在线IP情报库的开关是否被打开
        Map<String, Object> results = nacosSingleKeyUpdateService.getNacosSingleKeyValus(
                Collections.singletonList(NacosSingleKeyConfigEnum.ONLINE_DATABASE_ENABLE.getSingleKey())
        );
        boolean onlineIpOpen = false;
        if (results != null && results.containsKey(NacosSingleKeyConfigEnum.ONLINE_DATABASE_ENABLE.getSingleKey())
                && results.get(NacosSingleKeyConfigEnum.ONLINE_DATABASE_ENABLE.getSingleKey()) != null
                && (Boolean) results.get(NacosSingleKeyConfigEnum.ONLINE_DATABASE_ENABLE.getSingleKey())) {
            onlineIpOpen = true;
        }

        int ipCount = 0;
        int accountCount = 0;
        Set<String> allThreatIps = new HashSet<>();
        Set<String> allThreatAccounts = new HashSet<>();
        while (true) {
            Set<String> threatIps = aggRiskService.selectThreatIp(ipCount, 1000);
            allThreatIps.addAll(threatIps);
            ipCount += 1000;
            Set<String> threatAccounts = aggRiskService.selectThreatAccount(accountCount, 1000);
            allThreatAccounts.addAll(threatAccounts);
            accountCount += 1000;

            if (threatIps.size() == 0 && threatAccounts.size() == 0) {
                break;
            }

            log.warn("威胁ip数量:{}，威胁账号数量:{}", threatIps.size(), threatAccounts.size());

            for (String ip : threatIps) {
                try {
                    ThreatIp threatIp = threatIpService.parseThreatIp(ip, onlineIpOpen);
                    if (threatIp != null) {
                        //API3.0 将威胁IP信息转换为威胁信息存入数据库
                        ThreatInfo threatInfo = ThreatInfo.ThreatInfoMapper.INSTANCE.convert(threatIp);
                        threatInfo.setThreatType("IP");
                        List<String> appUriList = getAppUriList("IP", ip);
                        threatInfo.setAppUriList(new HashSet<>(appUriList));
                        threatInfoService.upsertThreatInfo(threatInfo);
                    }
                } catch (Exception e) {
                    log.error("单条IP威胁信息转换出错", e);
                }
            }

            for (String threatAccount : threatAccounts) {
                try {
                    ThreatInfo threatInfo = threatInfoService.parseThreatAccount(threatAccount);
                    if (threatInfo != null) {
                        List<String> appUriList = getAppUriList("ACCOUNT", threatAccount);
                        threatInfo.setAppUriList(new HashSet<>(appUriList));
                        threatInfoService.upsertThreatInfo(threatInfo);
                    }
                } catch (Exception e) {
                    log.error("单条账号威胁信息转换出错", e);
                }
            }

        }

        watch.stop();
        log.info("威胁信息统计结束,{},耗时：{}秒", DateUtil.currentDateTime(), watch.getTotalTimeSeconds());

        //清理无关联风险的威胁信息
        List<ThreatInfo> threatInfos = threatInfoService.getAll();
        if (DataUtil.isNotEmpty(threatInfos)) {
            for (ThreatInfo threatInfo : threatInfos) {
                if (!allThreatIps.contains(threatInfo.getThreatEntity()) && !allThreatAccounts.contains(threatInfo.getThreatEntity())) {
                    threatInfo.setDelFlag(true);
                    threatInfoService.upsertThreatInfo(threatInfo);
                }
            }
        }
    }

    private List<String> getAppUriList(String type, String value) {
        String collection = "";
        Query query = new Query();
        Criteria criteria = new Criteria();
        if ("IP".equals(type)) {
            collection = "ipInfo";
            criteria.and("ip").is(value);
        }
        if ("ACCOUNT".equals(type)) {
            collection = "accountInfo";
            criteria.and("account").is(value);
        }
        query.addCriteria(criteria);
        Document document = mongoTemplate.findOne(query, Document.class, collection);
        if (document != null) {
            return document.getList("appUriList", String.class);
        }
        return new ArrayList<>();
    }

}