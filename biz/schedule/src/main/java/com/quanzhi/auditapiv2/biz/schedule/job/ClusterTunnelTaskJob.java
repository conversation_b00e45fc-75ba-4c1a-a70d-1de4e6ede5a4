package com.quanzhi.auditapiv2.biz.schedule.job;

import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit.mix.schdule.domain.utils.ParameterHelper;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterType;
import com.quanzhi.auditapiv2.core.service.node.cluster.ClusterNodeService;
import com.quanzhi.auditapiv2.core.service.node.cluster.dto.SyncDTO;
import com.quanzhi.auditapiv2.core.service.node.task.ClusterTunnelTaskExecutorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ClusterTunnelTaskJob {

    private final ClusterTunnelTaskExecutorService clusterTunnelTaskExecutorService;
    private final ClusterNodeService clusterNodeService;

    @LockedScheduler(cron = "-", executor = "ClusterTunnelTask", name = "多节点数据同步-手动执行", description = "如果该节点为主节点，将通知所有子节点同步数据（输入参数 all 表示全量同步）；否则推送数据给主节点（输入参数 all 表示全量同步）")
    public void execute() {
        ClusterType clusterType = clusterNodeService.getClusterType();
        if (clusterType == ClusterType.STANDALONE) {
            throw new IllegalStateException("未接入多节点系统，无法同步数据");
        }
        if (clusterType == ClusterType.SLAVE) {
            String id = ParameterHelper.get();
            if (id != null && id.equals("all")) {
                clusterNodeService.deleteAllProgress();
                id = null;
            }
            clusterTunnelTaskExecutorService.executeSync(clusterType, id);
        }else{
            String params = ParameterHelper.get();
            SyncDTO syncDTO = new SyncDTO();
            syncDTO.setAll(params != null && params.contains("all"));
            if (!syncDTO.isAll()) {
                syncDTO.setTaskId(syncDTO.getTaskId());
            }
            clusterNodeService.sync(syncDTO);
        }
    }
}
