package com.quanzhi.auditapiv2.biz.schedule.job;

import com.quanzhi.auditapiv2.core.service.execution.ScheduleExecutionProcess;
import org.springframework.scheduling.annotation.Scheduled;

//@Component
public class ScheduleExecutionJob {
    private final ScheduleExecutionProcess scheduleExecutionProcess;

    public ScheduleExecutionJob(ScheduleExecutionProcess scheduleExecutionProcess) {
        this.scheduleExecutionProcess = scheduleExecutionProcess;
    }

    @Scheduled(cron = "0 0/1 * * * ?")
    public void execute() {
        scheduleExecutionProcess.execute();
    }
}
