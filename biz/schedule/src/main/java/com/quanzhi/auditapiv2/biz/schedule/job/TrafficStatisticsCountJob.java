package com.quanzhi.auditapiv2.biz.schedule.job;

import cn.hutool.core.date.DateUtil;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.common.dal.entity.TrafficStatistics;
import com.quanzhi.monitor.client.constant.TimeConstant;
import com.quanzhi.monitor.client.pojo.Result;
import com.quanzhi.monitor.client.pojo.dto.KafkaTimestampOffsetDTO;
import com.quanzhi.monitor.client.sdk.MDRSdkClient;
import com.quanzhi.monitor.client.util.MDRClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;


/**
 * @author: yangzixian
 * @date: 21/3/2023 17:09
 * @description:
 */
@Component
@Slf4j
public class TrafficStatisticsCountJob {

    private String monitorServer = "monitor-server:8080";

    private final MongoTemplate mongoTemplate;

    private String collectionName = "trafficStatistics";

    public TrafficStatisticsCountJob(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @LockedScheduler(cron = "0 59 * * * ?", executor = "trafficStatisticsCountJob", name = "概览流量趋势统计任务", description = "统计概览页面流量趋势数据")
    public void execute() throws Exception {
        doJob();
    }

    public void doJob() throws Exception {
        MDRSdkClient mdrClientBuilder = MDRClientBuilder.mdrUrl(monitorServer);
        long startTime = com.quanzhi.auditapiv2.common.util.utils.DateUtil.getTodayZeroTimeStamp();
        long endTime = startTime + 24 * 60 * 60 * 1000 - 1;
//        Calendar cal = Calendar.getInstance();
//        //根据当前时间获取开始时间
//        cal.setTime(DateUtil.date(zero));
//        //获取当前时间前一月时间作为结束时间
//        cal.add(Calendar.MONTH, -1);
//        long startTime = cal.getTimeInMillis();
        //获取当日访问量
        Result<KafkaTimestampOffsetDTO> result = mdrClientBuilder.getKafkaTimestampOffset(null, "ApiEvents", startTime, endTime, TimeConstant.MILLISECOND);
        if (result.getCode().equals("200")) {
            //成功请求
            KafkaTimestampOffsetDTO data = result.getData();
            if (data.getTimestampOffset() != null && data.getTimestampOffset() >= 0) {
                //入库目前流量统计
                TrafficStatistics trafficStatistics = new TrafficStatistics();
                trafficStatistics.setDate(DateUtil.date(startTime).toString("yyyyMMdd"));
                trafficStatistics.setCount(result.getData().getTimestampOffset());
                Update update = new Update();
                update.set("count", trafficStatistics.getCount());
                mongoTemplate.upsert(new Query().addCriteria(Criteria.where("date").is(trafficStatistics.getDate())), update, collectionName);
            }
        } else if ("404".equals(result.getCode())) {
            //mdr版本不对 或者 网络路径问题
            throw new Exception("mdr版本不对 或者 网络路径问题");
        } else if ("400".equals(result.getCode())) {
            //请求问题
            throw new Exception("请求问题");
        }
    }

}
