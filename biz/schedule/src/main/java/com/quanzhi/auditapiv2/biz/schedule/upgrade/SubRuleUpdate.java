package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import cn.hutool.core.util.RandomUtil;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.common.dal.entity.CustomFields;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribePolicyRule;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.CustomFieldsServiceImpl;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.ResourceChangedEventNacosServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SubRuleUpdate implements UpgradeService {

    private final CustomFieldsServiceImpl customFieldsService;

    private final ResourceChangedEventNacosServiceImpl resourceChangedEventNacosService;

    public SubRuleUpdate(CustomFieldsServiceImpl customFieldsService, ResourceChangedEventNacosServiceImpl resourceChangedEventNacosService) {
        this.customFieldsService = customFieldsService;
        this.resourceChangedEventNacosService = resourceChangedEventNacosService;
    }

    @Override
    public int getVersion() {
        return 20231130;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        log.info("开始进行订阅规则升级,{}", DateUtil.currentDateTime());

        List<SubscribePolicyRule> subscribePolicyRules = resourceChangedEventNacosService.getAll();
        List<String> ids = new ArrayList<>();
        for (SubscribePolicyRule subscribePolicyRule : subscribePolicyRules) {
            if (subscribePolicyRule.getCustomFields() == null) {
                ids.add(subscribePolicyRule.getId());
            }
        }
        resourceChangedEventNacosService.delete(ids);

        log.info("订阅规则升级成功，共删除无效规则{}条", ids.size());
    }

}
