package com.quanzhi.auditapiv2.biz.schedule.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.mongodb.BasicDBObject;
import com.quanzhi.audit.mix.match.service.ScopeMatchService;
import com.quanzhi.audit_core.common.model.RiskInfoAgg;
import com.quanzhi.audit_core.common.model.RiskLevel;
import com.quanzhi.audit_core.common.model.ScopeMatchInfo;
import com.quanzhi.auditapiv2.common.dal.dao.IRiskLevelDao;
import com.quanzhi.dsl.DslContext;
import com.quanzhi.metabase.common.utils.DataUtil;
import com.quanzhi.metabase.core.model.dto.RiskLevelMatchDto;
import com.quanzhi.metabase.core.model.http.HttpResourceConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregationOptions;

/**
 * <AUTHOR>
 * @date 1:48 PM
 * @description
 */
@Slf4j
public class AbstactIpAndAccountTask {
    @Autowired
    private ScopeMatchService scopeMatchService;

    @Autowired
    private IRiskLevelDao riskLevelDao;

    @Autowired
    protected MongoTemplate mongoTemplate;

    // entityType : riskLevels
    private Map<String, List<RiskLevel>> riskLevelMap = new HashMap<>();

    @NacosConfigListener(dataId = "common.risk.level.config.json", groupId = "common", timeout = 30000)
    public void onMessage(String msg) {
        List<RiskLevel> riskLevelList = JSON.parseArray(msg, RiskLevel.class);

        getRiskLevelMap(riskLevelList);
    }

    private void getRiskLevelMap(List<RiskLevel> riskLevelList) {
        if (DataUtil.isNotEmpty(riskLevelList)) {
            riskLevelMap = riskLevelList.stream().filter(riskLevel -> riskLevel.getEnableFlag() != null && riskLevel.getEnableFlag()).collect(Collectors.groupingBy(RiskLevel::getLevelType));
        }
    }

    protected RiskLevelMatchDto.Risk getRiskInfo(String entity, String entityType) {
        try {
            List<AggregationOperation> operations = new ArrayList<>();
//            operations.add(Aggregation.unwind("entities"));

            // 测试
//            entity = "192.168.9.13";

//            String field = "bizFields." + entityType.toLowerCase();
//            Criteria criteria =  Criteria.where(field).is(entity))

            Criteria criteria = Criteria.where("entities.type").is(entityType);
            criteria = criteria.and("entities.value").is(entity).and("state").in(RiskInfoAgg.RiskStateEnum.HAS_HANDLE.getValue(), RiskInfoAgg.RiskStateEnum.NOT_HANDLE.getValue());

            operations.add(Aggregation.match(criteria));
            operations.add(Aggregation.group("level").min("firstTime").as("riskFirstTime").max("lastTime").as("riskLastTime").addToSet("policySnapshot.name").as("riskNames").count().as("riskCnt"));
            Aggregation aggregation = Aggregation.newAggregation(operations).withOptions(newAggregationOptions().allowDiskUse(true).build());

            List<RiskLevelAgg> appAccountDtos = mongoTemplate.aggregate(aggregation, HttpResourceConstant.AGG_RISK_INFO, RiskLevelAgg.class).getMappedResults();

            RiskLevelMatchDto.Risk riskResult = new RiskLevelMatchDto.Risk();
            riskResult.setRiskNames(new HashSet<>());
            riskResult.setTotalRiskCnt(0L);
            riskResult.setRiskCnt(0L);
            riskResult.setRiskFirstTime(0L);
            riskResult.setLevelNumMap(new HashMap<>());
            riskResult.setConfirmRiskCount(0L);
            if (appAccountDtos.isEmpty()) {
                fillRiskInfo(riskResult, entityType);
                return riskResult;
            }
            for (RiskLevelAgg risk : appAccountDtos) {
                riskResult.getRiskNames().addAll(risk.getRiskNames());
                Long num = riskResult.getLevelNumMap().get(risk.getId());
                if (num == null) {
                    num = risk.getRiskCnt();
                } else {
                    num += risk.getRiskCnt();
                }
                riskResult.getLevelNumMap().put(risk.getId(), num);
                riskResult.setTotalRiskCnt(riskResult.getTotalRiskCnt() + risk.getRiskCnt());
                riskResult.setRiskCnt(riskResult.getRiskCnt() + risk.getRiskCnt());
                if (riskResult.getRiskFirstTime() == null || riskResult.getRiskFirstTime() == 0) {
                    riskResult.setRiskFirstTime(risk.getRiskFirstTime());
                } else if (riskResult.getRiskFirstTime() > risk.getRiskFirstTime()) {
                    riskResult.setRiskFirstTime(risk.getRiskFirstTime());
                }
                if (riskResult.getRiskLastTime() == null || riskResult.getRiskLastTime() == 0) {
                    riskResult.setRiskLastTime(risk.getRiskLastTime());
                } else if (riskResult.getRiskLastTime() < risk.getRiskLastTime()) {
                    riskResult.setRiskLastTime(risk.getRiskLastTime());
                }
            }
            riskResult.setConfirmRiskCount(mongoTemplate.count(Query.query(Criteria.where("entities.type").is(entityType).and("state").in(RiskInfoAgg.RiskStateEnum.HAS_HANDLE.getValue()).and("entities.value").is(entity)),
                    HttpResourceConstant.AGG_RISK_INFO));
            long riskLevelLow = riskResult.getLevelNumMap() == null ? 0L : (riskResult.getLevelNumMap().get(1) == null ? 0L : riskResult.getLevelNumMap().get(1));
            long riskLevelMedium = riskResult.getLevelNumMap() == null ? 0L : (riskResult.getLevelNumMap().get(2) == null ? 0L : riskResult.getLevelNumMap().get(2));
            long riskLevelHigh = riskResult.getLevelNumMap() == null ? 0L : (riskResult.getLevelNumMap().get(3) == null ? 0L : riskResult.getLevelNumMap().get(3));
            riskResult.setRiskLevelLow(riskLevelLow);
            riskResult.setRiskLevelMedium(riskLevelMedium);
            riskResult.setRiskLevelHigh(riskLevelHigh);
            this.fillRiskInfo(riskResult, entityType);
            return riskResult;
        } catch (Exception e) {
            log.error("补充风险信息失败：", e);
        }

        return new RiskLevelMatchDto.Risk();
    }

    // 补充匹配的风险名称、等级
    private void fillRiskInfo(RiskLevelMatchDto.Risk risk, String entityType) {
        // 初次访问，加载数据
        if (DataUtil.isEmpty(riskLevelMap)) {
            List<RiskLevel> riskLevels = riskLevelDao.getAll();
            getRiskLevelMap(riskLevels);
        }

        List<RiskLevel> riskLevelList = riskLevelMap.get(entityType);

        if (DataUtil.isNotEmpty(riskLevelList)) {
            DslContext dslContext = buildScopeDslContext(risk, null);

            for (RiskLevel riskLevel : riskLevelList) {
                if (riskLevel.getEnableFlag() != null && riskLevel.getEnableFlag()) {
                    ScopeMatchInfo subjectMatchInfo = riskLevel.getScopeMatchInfo();

                    if (dslContext != null) {
                        boolean match = false;
                        try {
                            match = scopeMatchService.match(dslContext, subjectMatchInfo);
                        } catch (Exception e) {
                            log.error("match error", e);
                        }
                        if (match) {
                            risk.setLevelName(riskLevel.getLevelName());
                            risk.setRiskLevel(riskLevel.getLevel());

                            break;
                        }
                    }
                }
            }
        }else{
            log.warn("empty risk config, type: {}", entityType);
        }
    }

    // 匹配风险、弱点规则
    private DslContext buildScopeDslContext(RiskLevelMatchDto.Risk risk, RiskLevelMatchDto.Weakness weak) {
        if (risk == null && weak == null) {
            return null;
        }

        DslContext dslContext = new DslContext();

        if (risk != null) {
            dslContext.put(AbstactIpAndAccountTask.Subject.RISK_NAME, risk.getRiskNames());
            dslContext.put(AbstactIpAndAccountTask.Subject.RISK_LEVEL_HIGH, risk.getRiskLevelHigh());
            dslContext.put(AbstactIpAndAccountTask.Subject.RISK_LEVEL_MEDIUM, risk.getRiskLevelMedium());
            dslContext.put(AbstactIpAndAccountTask.Subject.RISK_LEVEL_LOW, risk.getRiskLevelLow());
        } else {
            dslContext.put(AbstactIpAndAccountTask.Subject.RISK_NAME, new HashSet<String>());
            dslContext.put(AbstactIpAndAccountTask.Subject.RISK_LEVEL_HIGH, 0L);
            dslContext.put(AbstactIpAndAccountTask.Subject.RISK_LEVEL_MEDIUM, 0L);
            dslContext.put(AbstactIpAndAccountTask.Subject.RISK_LEVEL_LOW, 0L);
        }

        if (weak != null) {
            dslContext.put(AbstactIpAndAccountTask.Subject.WEAKNESS_NAME, weak.getWeaknessNames());
            dslContext.put(AbstactIpAndAccountTask.Subject.WEAKNESS_LEVEL_HIGH, weak.getWeaknessLevelHigh());
            dslContext.put(AbstactIpAndAccountTask.Subject.WEAKNESS_LEVEL_MEDIUM, weak.getWeaknessLevelMedium());
            dslContext.put(AbstactIpAndAccountTask.Subject.WEAKNESS_LEVEL_LOW, weak.getWeaknessLevelLow());
        } else {
            dslContext.put(AbstactIpAndAccountTask.Subject.WEAKNESS_NAME, new HashSet<String>());
            dslContext.put(AbstactIpAndAccountTask.Subject.WEAKNESS_LEVEL_HIGH, 0L);
            dslContext.put(AbstactIpAndAccountTask.Subject.WEAKNESS_LEVEL_MEDIUM, 0L);
            dslContext.put(AbstactIpAndAccountTask.Subject.WEAKNESS_LEVEL_LOW, 0L);
        }

        return dslContext;
    }

    // 定义规则名称
    private static class Subject {
//        com.quanzhi.audit.mix.match.service.ScopeManager#createLevelData    追加枚举

        public static String RISK_NAME = "RISK_NAME";
        public static String RISK_LEVEL_HIGH = "RISK_LEVEL_3";
        public static String RISK_LEVEL_MEDIUM = "RISK_LEVEL_2";
        public static String RISK_LEVEL_LOW = "RISK_LEVEL_1";

        public static String WEAKNESS_NAME = "WEAKNESS_NAME";
        public static String WEAKNESS_LEVEL_HIGH = "WEAKNESS_LEVEL_3";
        public static String WEAKNESS_LEVEL_MEDIUM = "WEAKNESS_LEVEL_2";
        public static String WEAKNESS_LEVEL_LOW = "WEAKNESS_LEVEL_1";
    }

    @Data
    public static final class RiskLevelAgg {
        private int id;
        private List<String> riskNames;
        private long riskCnt;
        private Long riskFirstTime;
        private Long riskLastTime;
        private Map<Integer, Long> stateCounts;
    }
}
