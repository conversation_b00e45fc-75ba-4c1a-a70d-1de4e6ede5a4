package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatIpService;
import com.quanzhi.auditapiv2.common.dal.enums.NacosSingleKeyConfigEnum;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatInfo;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatIp;
import com.quanzhi.auditapiv2.core.service.directive.DirectiveService;
import com.quanzhi.auditapiv2.core.service.manager.web.INacosSingleKeyUpdateService;
import com.quanzhi.auditapiv2.core.service.schedule.AuditScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;

/**
 * <AUTHOR>
 * create at 2024/7/9 5:40 下午
 * @description: 指令同步重试
 **/
@Component
@Slf4j
public class DirectiveRetryJob {

    private final DirectiveService directiveService;

    public DirectiveRetryJob(DirectiveService directiveService) {
        this.directiveService = directiveService;
    }

    @LockedScheduler(cron = "0 0/30 * * * ?", executor = "directiveRetryJob", name = "指令同步重试", description = "重试需要延时同步的指令")
    public void execute() throws Exception {
        doJob();
    }

    /**
     * 指令同步重试
     */
    public void doJob() throws Exception {
        log.info("开始进行指令同步重试,{}", DateUtil.currentDateTime());
        StopWatch watch = new StopWatch();
        watch.start("directiveRetryJob-task");

        directiveService.retryError();

        log.info("指令同步重试结束,{},耗时：{}秒", DateUtil.currentDateTime(), watch.getTotalTimeSeconds());
    }

}