package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.alibaba.fastjson.JSONObject;
import com.dtzhejiang.openapi.com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.common.dal.common.entity.ViewMenu;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * create at 2022/8/10 2:12 下午
 * @description: 风险规则的一些字段升级
 **/
@Component
@Slf4j
public class ViewMenuUpdate implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    public ViewMenuUpdate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public int getVersion() {
        return 20241025;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        log.info("update viewMenu,{}", DateUtil.currentDateTime());

        String aggRiskViewMenu = "{\"_id\":\"AggRiskViewMenuProcess\",\"viewMenuState\":false,\"label\":\"风险视图\",\"type\":\"category\",\"searchParams\":\"\",\"isDefault\":true,\"moveEnable\":false,\"count\":null,\"children\":[{\"_id\":\"allAggRisk\",\"label\":\"全部风险\",\"type\":\"view\",\"searchParams\":{\"state\":[0,2],\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"state\":[0,2],\"delFlag\":false}},\"groupParams\":null,\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null},{\"_id\":\"highLevelAggRisk\",\"label\":\"高危风险\",\"type\":\"view\",\"searchParams\":{\"state\":[0,2],\"level\":[3],\"delFlag\":false},\"viewFeSearchParams\":{\"groupField\":\"name\",\"tableSearchParams\":null,\"searchParams\":{\"state\":[0,2],\"level\":[3],\"delFlag\":false}},\"groupParams\":null,\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null},{\"_id\":\"dataBreachAggRisk\",\"label\":\"数据泄漏类\",\"type\":\"category\",\"searchParams\":{\"state\":[0,2],\"type\":\"数据泄漏类\",\"delFlag\":false},\"groupParams\":{\"group\":\"数据泄漏类\"},\"isDefault\":true,\"moveEnable\":false,\"count\":null,\"children\":null},{\"_id\":\"webAttackAggRisk\",\"label\":\"Web攻击类\",\"type\":\"category\",\"searchParams\":{\"state\":[0,2],\"type\":\"Web攻击类\",\"delFlag\":false},\"groupParams\":{\"group\":\"Web攻击类\"},\"isDefault\":true,\"moveEnable\":false,\"count\":null,\"children\":null},{\"_id\":\"accountSecurityAggRisk\",\"label\":\"账号安全类\",\"type\":\"category\",\"searchParams\":{\"state\":[0,2],\"type\":\"账号安全类\",\"delFlag\":false},\"groupParams\":{\"group\":\"账号安全类\"},\"isDefault\":true,\"moveEnable\":false,\"count\":null,\"children\":null}]}";
        String dataViewMenu = "{\"_id\":\"DataViewMenuProcess\",\"viewMenuState\":false,\"type\":\"category\",\"searchParams\":\"\",\"isDefault\":true,\"moveEnable\":false,\"count\":null,\"label\":\"数据视图\",\"children\":[{\"_id\":\"allData\",\"label\":\"全部数据\",\"type\":\"view\",\"searchParams\":{\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"delFlag\":false}},\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null},{\"_id\":\"dataLevel\",\"label\":\"数据敏感级别\",\"type\":\"category\",\"searchParams\":{},\"isDefault\":true,\"moveEnable\":false,\"count\":null,\"children\":[{\"_id\":\"four\",\"label\":\"四级\",\"type\":\"view\",\"searchParams\":{\"dataLevel\":[4],\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"dataLevel\":[4],\"delFlag\":false}},\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null},{\"_id\":\"three\",\"label\":\"三级\",\"type\":\"view\",\"searchParams\":{\"dataLevel\":[3],\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"dataLevel\":[3],\"delFlag\":false}},\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null},{\"_id\":\"two\",\"label\":\"二级\",\"type\":\"view\",\"searchParams\":{\"dataLevel\":[2],\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"dataLevel\":[2],\"delFlag\":false}},\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null},{\"_id\":\"one\",\"label\":\"一级\",\"type\":\"view\",\"searchParams\":{\"dataLevel\":[1],\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"dataLevel\":[1],\"delFlag\":false}},\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null}]},{\"_id\":\"dataRiskLevel\",\"label\":\"数据风险级别\",\"type\":\"category\",\"searchParams\":{},\"isDefault\":true,\"moveEnable\":false,\"count\":null,\"children\":[{\"_id\":\"high\",\"label\":\"高风险\",\"type\":\"view\",\"searchParams\":{\"dataRiskLevel\":3,\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"dataRiskLevel\":3,\"delFlag\":false}},\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null},{\"_id\":\"mid\",\"label\":\"中风险\",\"type\":\"view\",\"searchParams\":{\"dataRiskLevel\":2,\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"dataRiskLevel\":2,\"delFlag\":false}},\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null},{\"_id\":\"low\",\"label\":\"低风险\",\"type\":\"view\",\"searchParams\":{\"dataRiskLevel\":1,\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"dataRiskLevel\":1,\"delFlag\":false}},\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null},{\"_id\":\"non\",\"label\":\"无风险\",\"type\":\"view\",\"searchParams\":{\"dataRiskLevel\":0,\"delFlag\":false},\"viewFeSearchParams\":{\"tableSearchParams\":null,\"searchParams\":{\"dataRiskLevel\":0,\"delFlag\":false}},\"isDefault\":true,\"moveEnable\":false,\"count\":0,\"children\":null}]}]}";
        ViewMenu aggRiskView = JSONObject.parseObject(aggRiskViewMenu, ViewMenu.class);
        ViewMenu dataView = JSONObject.parseObject(dataViewMenu, ViewMenu.class);
        //新增两个视图
        mongoTemplate.save(aggRiskView, "viewMenu");
        mongoTemplate.save(dataView, "viewMenu");

        //更新异常视图
        ViewMenu riskView = mongoTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is("RiskViewMenuProcess")), ViewMenu.class, "viewMenu");
        String newRiskViewStr = JSON.toJSONString(riskView).replaceAll("风险", "异常");
        ViewMenu newRiskView = JSONObject.parseObject(newRiskViewStr, ViewMenu.class);
        mongoTemplate.remove(new Query().addCriteria(Criteria.where("_id").is("RiskViewMenuProcess")));
        mongoTemplate.save(newRiskView, "viewMenu");

        log.info("update success!");
    }

}