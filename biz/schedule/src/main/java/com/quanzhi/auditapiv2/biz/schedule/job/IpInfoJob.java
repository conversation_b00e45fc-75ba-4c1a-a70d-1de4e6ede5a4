package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit.mix.schdule.domain.utils.ParameterHelper;
import com.quanzhi.auditapiv2.biz.schedule.task.ip.IPInfoMonthAggService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IpInfoJob {
    private final IPInfoMonthAggService ipInfoMonthAggService;
    public IpInfoJob(IPInfoMonthAggService ipInfoMonthAggService) {
        this.ipInfoMonthAggService = ipInfoMonthAggService;
    }
    @NacosValue(value = "${schedule.count.num:500000}", autoRefreshed = true)
    private long scheduleCountNum;

    /**
     * 执行定时任务
     */
    @LockedScheduler(cron = "0 20 4-22/2 * * ?", executor = "IpInfoJob", interval = 1000 * 60 * 60 * 24, intervalConditionSpEL = "#root.check()", description = "计算IP清单数据，用于IP列表和IP详情的数据展示")
    public void execute() {
        doJob(ParameterHelper.get());
    }

    public boolean check() {
        return ipInfoMonthAggService.count() > scheduleCountNum;
    }

    /**
     * 处理任务
     */
    public void doJob(String date) {
        ipInfoMonthAggService.stat();
    }

}
