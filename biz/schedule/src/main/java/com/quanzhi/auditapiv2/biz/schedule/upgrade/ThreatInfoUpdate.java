package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatIpService;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatInfo;
import com.quanzhi.auditapiv2.core.risk.entity.ThreatIp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2023/8/10 2:12 下午
 * @description: 将旧的威胁IP信息转换为威胁信息存入数据库
 **/
@Component
@Slf4j
public class ThreatInfoUpdate implements UpgradeService {

    private final ThreatInfoService threatInfoService;

    private final ThreatIpService threatIpService;

    public ThreatInfoUpdate(ThreatInfoService threatInfoService, ThreatIpService threatIpService) {
        this.threatInfoService = threatInfoService;
        this.threatIpService = threatIpService;
    }

    @Override
    public int getVersion() {
        return 20230831;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        log.info("update threatInfo,{}", DateUtil.currentDateTime());

        //获取旧的威胁IP信息
        List<ThreatIp> threatIps = threatIpService.getAll();
        //提取ip信息
        List<String> ips = threatIps.stream().map(ThreatIp::getIp).collect(Collectors.toList());
        for (String ip : ips) {
            try {
                ThreatIp threatIp = threatIpService.parseThreatIp(ip, null);
                if (threatIp != null) {
                    //API3.0 将威胁IP信息转换为威胁信息存入数据库
                    ThreatInfo threatInfo = ThreatInfo.ThreatInfoMapper.INSTANCE.convert(threatIp);
                    threatInfo.setThreatType("IP");
                    threatInfoService.upsertThreatInfo(threatInfo);
                }
            } catch (Exception e) {
                log.error("update error:", e);
            }
        }

        log.info("update success!");
    }
}