package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.model.EventFilterPlugin;
import com.quanzhi.auditapiv2.common.dal.dto.filter.AssetFilterDTO;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.convert.EventFilterPluginConverter;
import com.quanzhi.auditapiv2.core.service.manager.web.IEventFilterPluginService;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceFilterService;
import com.quanzhi.metabase.client.filter.FilterContext;
import com.quanzhi.metabase.core.model.filter.AssetFilterRule;
import com.quanzhi.metabase.core.model.filter.FilterHttpEventStat;
import com.quanzhi.metabase.core.model.filter.SmartFilterRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2024/1/2 17:47
 * @description: 3.1的资产过滤规则升到3.2
 **/
@Component
@Slf4j
public class AssetFilterRuleUpdate2 implements UpgradeService {


    private final IResourceFilterService resourceService;
    private final MongoTemplate mongoTemplate;
    private final IEventFilterPluginService eventFilterPluginServiceImpl;


    public AssetFilterRuleUpdate2(IResourceFilterService resourceService, MongoTemplate mongoTemplate, IEventFilterPluginService eventFilterPluginServiceImpl) {
        this.resourceService = resourceService;
        this.mongoTemplate = mongoTemplate;
        this.eventFilterPluginServiceImpl = eventFilterPluginServiceImpl;
    }


    @DynamicValue(dataId = "common.eventfilterplugin.json", groupId = "common", typeClz = EventFilterPlugin.class, convertClz = EventFilterPluginConverter.class)
    private List<EventFilterPlugin> eventFilterPlugins;


    private List<String> defaultFilterRuleIds=Arrays.asList("FILTER_APP_ID","FILTER_API_ID","FILTER_REQ_SUFFIX_ID","FILTER_REQ_UA_ID","FILTER_RSP_CODE_ID","FILTER_RSP_CONTENT_ID","FILTER_RSP_CONTENT_TYPE_ID","FILTER_REQ_METHOD");


    @Override
    public int getVersion() {
        return 20241025;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    /**
     * 升级逻辑
     * 1、规则过滤升级resourceList、生效范围等字段
     * 2、白名单升级内置的，和整合之前的
     * 3、ip段、IP掩码等规则合到ip一条规则里
     * 4、同步插件过滤和智能过滤
     */
    @Override
    public void upgrade() {
        log.info("start AssetFilterRuleUpdate2");
        upgradePluginFilter();
        upgradeRuleFilter();
        upgradeSmartFilter();
        log.info("finish AssetFilterRuleUpdate2");

    }

    /**
     * 同步智能过滤规则
     */
    private void upgradeSmartFilter() {
        try {
            List<AssetFilterRule> smartRules = mongoTemplate.find(new Query().addCriteria(Criteria.where("configType").is(AssetFilterRule.ConfigTypeEnum.FILTER.name()).and("delFlag").is(false)
                    .and("ruleType").is(AssetFilterRule.RuleType.SMART.name())), AssetFilterRule.class);
            if (smartRules.isEmpty()) {
                List<SmartFilterRule> smartFilterRules = mongoTemplate.find(new Query().addCriteria(Criteria.where("enable").is(true).and("delFlag").is(false)), SmartFilterRule.class);
                for (SmartFilterRule smartFilterRule : smartFilterRules) {
                    //只同步返回内容的规则
                    if (smartFilterRule.getTarget().equals("rsp.body")&&DataUtil.isNotEmpty(smartFilterRule.getCompareValue())) {
                        AssetFilterRule assetFilterRule = new AssetFilterRule();
                        assetFilterRule.setConfigType(AssetFilterRule.ConfigTypeEnum.FILTER);
                        assetFilterRule.setOpenFlag(true);
                        assetFilterRule.setDelFlag(false);
                        assetFilterRule.setQzFlag(false);
                        assetFilterRule.setRuleType(AssetFilterRule.RuleType.SMART);
                        assetFilterRule.setType(AssetFilterRule.AssetResourceTypeEnum.RSP_CONTENT.name());
                        assetFilterRule.setEffectScopes(Arrays.asList(smartFilterRule.getHost()));
                        assetFilterRule.setResourceList(Arrays.asList(smartFilterRule.getCompareValue()));
                        assetFilterRule.setSmartFilterRule(smartFilterRule);
                        assetFilterRule.setId(smartFilterRule.getId());
                        resourceService.saveAssetFilterRule(assetFilterRule);
                    }
                    //删除原有规则
                    mongoTemplate.upsert(Query.query(Criteria.where("_id").is(smartFilterRule.getId())), Update.update("delFlag", true), SmartFilterRule.class);
                }
                //删除未启用的规则
                mongoTemplate.remove(Query.query(Criteria.where("enable").is(false)), SmartFilterRule.class);
            }
        } catch (Exception e) {
           log.error("upgradeSmartFilter error:",e);
        }
    }

    /**
     * 同步插件过滤规则
     */
    private void upgradePluginFilter() {
        try {
            List<AssetFilterRule> pluginRules = mongoTemplate.find(new Query().addCriteria(Criteria.where("configType").is(AssetFilterRule.ConfigTypeEnum.FILTER.name()).and("delFlag").is(false)
                    .and("ruleType").is(AssetFilterRule.RuleType.PLUGIN.name())), AssetFilterRule.class);
            if (pluginRules.isEmpty()) {
                List<String> syncPluginIds = Arrays.asList("illegalhostfilter", "redirectfilter", "notfoundfilter", "contentnotmatchfilter", "illegalurlpathfilter");
                for (EventFilterPlugin eventFilterPlugin : eventFilterPlugins) {
                    if (syncPluginIds.contains(eventFilterPlugin.getId())) {
                        AssetFilterRule assetFilterRule = new AssetFilterRule();
                        assetFilterRule.setConfigType(AssetFilterRule.ConfigTypeEnum.FILTER);
                        assetFilterRule.setOpenFlag(true);
                        assetFilterRule.setDelFlag(false);
                        assetFilterRule.setQzFlag(true);
                        assetFilterRule.setRuleType(AssetFilterRule.RuleType.PLUGIN);
                        assetFilterRule.setType(eventFilterPlugin.getName());
                        assetFilterRule.setEffectScopes(Arrays.asList("ALL"));
                        assetFilterRule.setEventFilterPlugin(eventFilterPlugin);
                        assetFilterRule.setId(eventFilterPlugin.getId());
                        resourceService.saveAssetFilterRule(assetFilterRule);
                    } else {
                        //删除
                        try {
                            eventFilterPluginServiceImpl.delEventFilterPlugin(eventFilterPlugin.getId());
                        } catch (Exception e) {
                            //ignore
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("upgradePluginFilter error:",e);
        }
    }

    /**
     * 升级原先的规则过滤
     */
    private void upgradeRuleFilter() {
        try {
            upgradePassRule();
        } catch (Exception e) {
            log.error("upgradePassRule error:", e);
        }

        try {
            upgradeFilterRule();
        } catch (Exception e) {
            log.error("upgradeFilterRule error:", e);
        }
    }

    private void upgradeFilterRule() {
        //升级排除名单
        List<AssetFilterRule> filterRules = mongoTemplate.find(new Query().addCriteria(Criteria.where("configType").is(AssetFilterRule.ConfigTypeEnum.FILTER.name()).and("delFlag").is(false)), AssetFilterRule.class);
        for (AssetFilterRule filterRule : filterRules) {
            if (DataUtil.isNotEmpty(filterRule.getResourceList())) {
                continue;
            }
            if (filterRule.getRuleType()!=null&&AssetFilterRule.RuleType.PLUGIN.name().equals(filterRule.getRuleType().name())) {
                continue;
            }
            String resource = filterRule.getResource();
            if(DataUtil.isEmpty(resource)){
                continue;
            }
            if(defaultFilterRuleIds.contains(filterRule.getId())){
                //内置的规则不处理
                continue;
            }
            List<String> oldResources = Arrays.stream(resource.split(",")).collect(Collectors.toList());
            if (AssetFilterRule.AssetResourceTypeEnum.DST_IP_MASK.name().equals(filterRule.getType()) ||
                    AssetFilterRule.AssetResourceTypeEnum.DST_IP_SEG.name().equals(filterRule.getType())
            ) {
                filterRule.setType(AssetFilterRule.AssetResourceTypeEnum.DST_IP.name());
            }
            if (AssetFilterRule.AssetResourceTypeEnum.IP_MASK.name().equals(filterRule.getType()) ||
                    AssetFilterRule.AssetResourceTypeEnum.IP_SEG.name().equals(filterRule.getType())
            ) {
                filterRule.setType(AssetFilterRule.AssetResourceTypeEnum.IP.name());
            }
            filterRule.setRuleType(AssetFilterRule.RuleType.RULE);
            filterRule.setResourceList(oldResources);
            filterRule.setEffectScopes(Arrays.asList("ALL"));
            filterRule.setQzFlag(filterRule.getId() != null && filterRule.getId().contains("_") ? true : false);
            resourceService.saveAssetFilterRule(filterRule);
        }
    }

    private void upgradePassRule() {
        //升级白名单相关
        List<AssetFilterRule> passRules = mongoTemplate.find(new Query().addCriteria(Criteria.where("configType").is(AssetFilterRule.ConfigTypeEnum.PASS.name()).and("delFlag").is(false)), AssetFilterRule.class);
        List<String> dstIps = new ArrayList<>();
        List<String> apps = new ArrayList<>();
        for (AssetFilterRule passRule : passRules) {
            if (DataUtil.isNotEmpty(passRule.getResourceList())) {
                continue;
            }
            String resource = passRule.getResource();
            if(DataUtil.isEmpty(resource)){
                continue;
            }
            List<String> oldResources = Arrays.stream(resource.split(",")).collect(Collectors.toList());
            if (AssetFilterRule.AssetResourceTypeEnum.RSP_CONTENT_TYPE.name().equals(passRule.getType())) {
                passRule.setRuleType(AssetFilterRule.RuleType.RULE);
                passRule.setResourceList(oldResources);
                //响应contentType白名单在3.2里变成排除名单
                passRule.setConfigType(AssetFilterRule.ConfigTypeEnum.FILTER);
                passRule.setQzFlag(passRule.getId() != null && passRule.getId().contains("_") ? true : false);
                passRule.setEffectScopes(Arrays.asList("ALL"));
                resourceService.saveAssetFilterRule(passRule);
                continue;
            }
            if (AssetFilterRule.AssetResourceTypeEnum.APP.name().equals(passRule.getType())) {
                apps.addAll(oldResources);
            } else if (AssetFilterRule.AssetResourceTypeEnum.DST_IP.name().equals(passRule.getType()) ||
                    AssetFilterRule.AssetResourceTypeEnum.DST_IP_MASK.name().equals(passRule.getType()) ||
                    AssetFilterRule.AssetResourceTypeEnum.DST_IP_SEG.name().equals(passRule.getType())
            ) {
                dstIps.addAll(oldResources);
            }
            //先都删除
            passRule.setDelFlag(true);
            passRule.setOperator("upgrade");
            resourceService.saveAssetFilterRule(passRule);
        }
        if (apps.size() > 0) {
            AssetFilterRule assetFilterRule = new AssetFilterRule();
            assetFilterRule.setConfigType(AssetFilterRule.ConfigTypeEnum.PASS);
            assetFilterRule.setOpenFlag(true);
            assetFilterRule.setRuleType(AssetFilterRule.RuleType.RULE);
            assetFilterRule.setType(AssetFilterRule.AssetResourceTypeEnum.APP.name());
            assetFilterRule.setResourceList(apps);
            assetFilterRule.setQzFlag(assetFilterRule.getId() != null && assetFilterRule.getId().contains("_") ? true : false);
            resourceService.saveAssetFilterRule(assetFilterRule);
        }
        if (dstIps.size() > 0) {
            AssetFilterRule assetFilterRule = new AssetFilterRule();
            assetFilterRule.setConfigType(AssetFilterRule.ConfigTypeEnum.PASS);
            assetFilterRule.setOpenFlag(true);
            assetFilterRule.setRuleType(AssetFilterRule.RuleType.RULE);
            assetFilterRule.setType(AssetFilterRule.AssetResourceTypeEnum.DST_IP.name());
            assetFilterRule.setResourceList(dstIps);
            assetFilterRule.setQzFlag(assetFilterRule.getId() != null && assetFilterRule.getId().contains("_") ? true : false);
            resourceService.saveAssetFilterRule(assetFilterRule);
        } /*else {
            AssetFilterDTO assetFilterDTO = new AssetFilterDTO();
            assetFilterDTO.setType(Arrays.asList(AssetFilterRule.AssetResourceTypeEnum.DST_IP.name()));
            assetFilterDTO.setConfigType(AssetFilterRule.ConfigTypeEnum.PASS.name());
            long totalCount = resourceService.getAssetFilterList(assetFilterDTO).getTotalCount();
            if(totalCount==0){
                //如果没配置过目的IP，就内置内网IP段
                AssetFilterRule assetFilterRule = new AssetFilterRule();
                assetFilterRule.setConfigType(AssetFilterRule.ConfigTypeEnum.PASS);
                assetFilterRule.setOpenFlag(false);
                assetFilterRule.setRuleType(AssetFilterRule.RuleType.RULE);
                assetFilterRule.setType(AssetFilterRule.AssetResourceTypeEnum.DST_IP.name());
                String defaultDstIps = "10.0.0.0-**************,**********-***************,***********-***************,**********-**************,***********-***************";
                assetFilterRule.setResourceList(Arrays.stream(defaultDstIps.split(",")).collect(Collectors.toList()));
                assetFilterRule.setQzFlag(assetFilterRule.getId() != null && assetFilterRule.getId().contains("_") ? true : false);
                resourceService.saveAssetFilterRule(assetFilterRule);
            }
        }*/
        //同步之前白名单过滤日志总数
        Long passRuleAllFilterCount = 0L;
        for (AssetFilterRule passRule : passRules) {
            FilterHttpEventStat filterHttpEventStat = mongoTemplate.findOne(Query.query(Criteria.where("_id").is(passRule.getId())), FilterHttpEventStat.class);
            if (filterHttpEventStat != null) {
                passRuleAllFilterCount += filterHttpEventStat.getFilterCount();
            }
        }
        FilterHttpEventStat filterHttpEventStat = new FilterHttpEventStat();
        filterHttpEventStat.setFilterCount(passRuleAllFilterCount);
        filterHttpEventStat.setRuleId(FilterContext.PASS_RULE_FILTER);
        filterHttpEventStat.setCreateTime(System.currentTimeMillis());
        filterHttpEventStat.setUpdateTime(System.currentTimeMillis());
        mongoTemplate.save(filterHttpEventStat);
    }


}