package com.quanzhi.auditapiv2.biz.schedule.task.structure;

import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiDao;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpAppDao;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.dal.entity.UrlStructureLeafInfo;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.*;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpApiService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.auditapiv2.core.service.manager.web.IUrlStructureService;
import com.quanzhi.metabase.core.model.enums.ApiTypeEnum;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.net.URL;
import java.util.*;

/**
 * <AUTHOR>
 * create at 2021/12/31 11:43 上午
 * @description:
 **/
@Service
@Slf4j
public class AppStructureTreeScanService {

    @Autowired
    private EventFilterProcess eventFilterProcess;

    private List<String> needFilterKeywords = Arrays.asList("/.ssh", "/.git", "/.svn", ".mdb", "nonexist");

    @Autowired
    private IUrlStructureService urlStructureService;

    @Autowired
    private IHttpAppDao httpAppDao;

    @Autowired
    private IHttpApiDao httpApiDao;


   /* @Autowired
    private IHttpApiService httpApiService;*/


    public void scan() {
        log.info("smartFilter rule study start,{}", DateUtil.currentDateTime());
        StopWatch watch = new StopWatch();
        watch.start("smartFilterStudy-task");
        //单页面应用的过滤
        chekcSinglePageFilter();
        //扫描登录接口的过滤
        checkScanLoginFilter();
        //携带攻击关键词的过滤
        checkAttackKeywordFilter();
        //获取接口数大于200的应用
        List<HttpAppResource> apiCountTopApps = getApiCountTopApps(200);
        //接口数大于200的应用全路径判断
        checkAppAllUrlScan(apiCountTopApps);
        //接口数大于200的应用只针对二级路径判断
        checkAppSecondNodeScan(apiCountTopApps);
        watch.stop();
        log.info("smartFilter rule study finish,{},cost：{}秒", DateUtil.currentDateTime(), watch.getTotalTimeSeconds());
    }


    /**
     * 接口数大于200的应用只针对二级路径判断
     */
    private void checkAppSecondNodeScan(List<HttpAppResource> apiCountTopApps) {
        if (apiCountTopApps.isEmpty()) {
            return;
        }
        HttpApiSearchDto apiSearchDto = new HttpApiSearchDto();
        for (HttpAppResource httpAppDto : apiCountTopApps) {
            String appHost=httpAppDto.getHost();
            List<UrlStructureLeafInfo> urlStructureLeafInfoList = urlStructureService.pageNodeByUrlPath(appHost, 30);

            if (CollectionUtils.isEmpty(urlStructureLeafInfoList)) {
                continue;
            }
            // 处理叶子节点较多的一个节点
            apiSearchDto.setHost(appHost);
            apiSearchDto.setDelFlag(false);
            for (UrlStructureLeafInfo urlStructureLeafInfo : urlStructureLeafInfoList) {
                String nodeName = urlStructureLeafInfo.getId();

                List<HttpApiSearchDto.UrlPaths> urlPaths = new ArrayList<>();
                HttpApiSearchDto.UrlPaths path = new HttpApiSearchDto.UrlPaths();
                path.setUrlPath(nodeName);
                path.setUrlNode(2);
                urlPaths.add(path);
                apiSearchDto.setUrlPathList(urlPaths);
                apiSearchDto.setUrlNode(2);

                apiSearchDto.setShowFields(Arrays.asList("apiUrl"));
                List<String> apiUrls = new ArrayList<>();
                List<HttpApiResource> httpApis = httpApiDao.getHttpApis(1, 500, null, null, apiSearchDto);
                if (DataUtil.isEmpty(httpApis)) {
                    continue;
                }
                httpApis.forEach(e -> apiUrls.add(e.getApiUrl()));
                List<String> maybeScanUrls = eventFilterProcess.processAllApiUrl(apiUrls);
                eventFilterProcess.processSubApp(appHost,appHost, maybeScanUrls);
            }
            log.info("start process app {} has scan", appHost);
        }


    }

    /**
     * 接口数大于200的应用全路径判断
     */
    private void checkAppAllUrlScan(List<HttpAppResource> topApps) {
        if (topApps.isEmpty()) {
            return;
        }
        for (HttpAppResource appResource : topApps) {
            String appHost = appResource.getHost();
            try {
                HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
                httpApiSearchDto.setHost(appHost);
                httpApiSearchDto.setDelFlag(false);
                // 查询最近24个小时新增的接口
                httpApiSearchDto.setDiscoverTimeStart(System.currentTimeMillis() - 24 * 3600 * 1000);
                httpApiSearchDto.setDiscoverTimeEnd(System.currentTimeMillis());
                httpApiSearchDto.setShowFields(Arrays.asList("apiUrl", "apiStat", "host"));

                List<HttpApiResource> httpApis = httpApiDao.getHttpApis(1, 500, null, null, httpApiSearchDto);

                Map<String, Long> apiVisitCounts = new HashMap<>();
                List<String> hostApiUrls = new ArrayList<>();
                for (HttpApiResource httpApi : httpApis) {
                    hostApiUrls.add(httpApi.getApiUrl());
                    apiVisitCounts.put(httpApi.getApiUrl(), httpApi.getApiStat().getTotalVisits());
                }
                List<String> maybeScanUrls = eventFilterProcess.processAllApiUrl(hostApiUrls);
                if (DataUtil.isNotEmpty(maybeScanUrls)) {
                    eventFilterProcess.processSubApp(appHost, appHost, maybeScanUrls);
                    log.info("start process app {} has scan", appHost);
                } else {
                    long pastTime = System.currentTimeMillis() - appResource.getCreateTime();
                    List<String> spareUrls = new ArrayList<>();
                    for (String url : hostApiUrls) {
                        if (apiVisitCounts.get(url) <= 3) {
                            spareUrls.add(url);
                        }
                    }
                    // 应用新增一天以后的某一天，一个应用下突然新增超过100个接口，并且这些接口的访问量都很低
                    if (pastTime > 36 * 3600 * 1000 && spareUrls.size() >= 100) {
                        eventFilterProcess.processSubApp(appHost, appHost, spareUrls);
                        log.info("start process app {} has scan", appHost);
                    }
                }
            } catch (Exception e) {
                log.error("process filter error : " + appResource.getHost());
            }
        }
    }

    private List<HttpAppResource> getApiCountTopApps(int apiCountLimit) {
        Map<String, Object> queryCondition = new HashMap<>();
        queryCondition.put("appStat.allTypeApiCount_gte", apiCountLimit);
        queryCondition.put("delFlag", false);
        queryCondition.put("showFields", Arrays.asList("host", "createTime"));
        List<HttpAppResource> httpAppResourceList = new ArrayList<>();
        try {
            httpAppResourceList = httpAppDao.selectHttpAppList(queryCondition, "appStat.allTypeApiCount", ConstantUtil.Sort.DESC, 1, 100);
        } catch (Exception e) {

        }
        return httpAppResourceList;
    }

    /**
     * 携带攻击关键词的过滤
     */
    private void checkAttackKeywordFilter() {
        Map<String, Set<String>> urlsByRealApp = new HashMap<>();
        for (String needFilterKeyword : needFilterKeywords) {
            HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
            httpApiSearchDto.setApiSearch(needFilterKeyword);
            httpApiSearchDto.setDelFlag(false);
            // 查询最近12个小时新增的接口
            httpApiSearchDto.setDiscoverTimeStart(System.currentTimeMillis() - 12 * 3600 * 1000);
            httpApiSearchDto.setDiscoverTimeEnd(System.currentTimeMillis());
            httpApiSearchDto.setShowFields(Arrays.asList("apiUrl", "host"));

            List<HttpApiResource> httpApis = httpApiDao.getHttpApis(1, 1000, null, null, httpApiSearchDto);
            groupApisByHost(httpApis, urlsByRealApp);
        }
        for (String realApp : urlsByRealApp.keySet()) {
            //单个应用下，存在5个携带攻击关键词的可能是扫描
            if (urlsByRealApp.get(realApp).size() > 5) {
                eventFilterProcess.processSubApp(realApp, realApp, urlsByRealApp.get(realApp));
                log.info("checkAttackKeywordFilter start process app {} has scan", realApp);
            }
        }
    }

    /**
     * 扫描登录接口流量的过滤
     */
    private void checkScanLoginFilter() {
        HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
        httpApiSearchDto.setReqDataLabels(Arrays.asList("password"));
        httpApiSearchDto.setDelFlag(false);
        // 查询最近12个小时新增的接口
        httpApiSearchDto.setDiscoverTimeStart(System.currentTimeMillis() - 12 * 3600 * 1000);
        httpApiSearchDto.setDiscoverTimeEnd(System.currentTimeMillis());
        httpApiSearchDto.setShowFields(Arrays.asList("apiUrl", "host"));
        List<HttpApiResource> httpApis = httpApiDao.getHttpApis(1, 500, null, null, httpApiSearchDto);
        Map<String, Set<String>> urlsByApp = groupApisByHost(httpApis, new HashMap<>());
        for (String realApp : urlsByApp.keySet()) {
            // 单个应用下，存在超过5个请求中携带密码的，可能是扫描登录接口
            if (urlsByApp.get(realApp).size() > 5) {
                eventFilterProcess.processSubApp(realApp, realApp, urlsByApp.get(realApp));
                log.info("checkScanLoginFilter start process app {} has scan", realApp);
            }
        }
    }

    /**
     * 单页面应用流量的过滤
     */
    private void chekcSinglePageFilter() {
        HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
        httpApiSearchDto.setApiType(Arrays.asList(ApiTypeEnum.SPA_HTML.getCode()));
        httpApiSearchDto.setDelFlag(false);
        // 查询最近12个小时新增的接口
        httpApiSearchDto.setDiscoverTimeStart(System.currentTimeMillis() - 12 * 3600 * 1000);
        httpApiSearchDto.setDiscoverTimeEnd(System.currentTimeMillis());
        httpApiSearchDto.setShowFields(Arrays.asList("apiUrl", "host"));
        List<HttpApiResource> httpApis = httpApiDao.getHttpApis(1, 1000, null, null, httpApiSearchDto);

        Map<String, Set<String>> urlsByApp = groupApisByHost(httpApis, new HashMap<>());
        for (String realApp : urlsByApp.keySet()) {
            // 单个应用下，存在超过10个单页面应用接口 可能是扫描
            if (urlsByApp.get(realApp).size() > 10) {
                eventFilterProcess.processSubApp(realApp, realApp, urlsByApp.get(realApp));
                log.info("chekcSinglePageFilter start process app {} has scan", realApp);
            }
        }
    }

    private Map<String, Set<String>> groupApisByHost(List<HttpApiResource> httpApis, Map<String, Set<String>> hostUrlsMap) {
        if (hostUrlsMap == null) {
            hostUrlsMap = new HashMap<>();
        }
        for (HttpApiResource apiResource : httpApis) {
            String host = apiResource.getHost();
            if (!hostUrlsMap.containsKey(host)) {
                hostUrlsMap.put(host, new HashSet<>());
            } else {
                hostUrlsMap.get(host).add(apiResource.getApiUrl());
            }
        }
        return hostUrlsMap;
    }


    /*  *//**
     * 开始扫描
     *//*
    public void scan() {
        log.info("smartFilter rule study start,{}", DateUtil.currentDateTime());
        StopWatch watch = new StopWatch();
        watch.start("smartFilterStudy-task");
        // 扫描
        int apiCountLimit = 100;
        List<HttpAppResource> httpAppResourceList = null;
        try {
            Map<String, Object> queryCondition = new HashMap<>();
            queryCondition.put("appStat.allTypeApiCount_gte", apiCountLimit);
            queryCondition.put("showFields", Arrays.asList("host", "createTime"));
            httpAppResourceList = httpAppDao.selectHttpAppList(queryCondition, "appStat.allTypeApiCount", ConstantUtil.Sort.DESC, 1, 100);
        } catch (Exception e) {
            log.error("get top apiCount app list error ", e);
        }
        // 全部路径进行接口过滤的判断
        if (!CollectionUtils.isEmpty(httpAppResourceList)) {
            for (HttpAppResource appResource : httpAppResourceList) {
                String appHost = appResource.getHost();
                try {
                    HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
                    httpApiSearchDto.setHost(appHost);
                    httpApiSearchDto.setDelFlag(false);
                    // 查询最近24个小时新增的接口
                    httpApiSearchDto.setDiscoverTimeStart(System.currentTimeMillis() - 24 * 3600 * 1000);
                    httpApiSearchDto.setDiscoverTimeEnd(System.currentTimeMillis());
                    httpApiSearchDto.setShowFields(Arrays.asList("apiUrl", "apiStat"));
                    ListOutputDto<HttpApiResource> httpApiListOutputDto = httpApiService.getHttpApisFields(1, 500, null, null, httpApiSearchDto, Arrays.asList("apiUrl", "apiStat.totalVisits", "host"));
                    if (httpApiListOutputDto.getTotalCount() == 0) {
                        continue;
                    }
                    Map<String, Long> apiVisitCounts = new HashMap<>();
                    List<String> hostApiUrls = new ArrayList<>();
                    for (HttpApiResource httpApi : httpApiListOutputDto.getRows()) {
                        hostApiUrls.add(httpApi.getApiUrl());
                        apiVisitCounts.put(httpApi.getApiUrl(), httpApi.getApiStat().getTotalVisits());
                    }
                    List<String> maybeScanUrls = eventFilterProcess.processAllApiUrl(hostApiUrls);
                    if (maybeScanUrls != null && maybeScanUrls.size() > 0) {
                        eventFilterProcess.processSubApp(appHost, appHost, maybeScanUrls);
                        log.info("start process app {} has scan", appHost);
                    } else {
                        long pastTime = System.currentTimeMillis() - appResource.getCreateTime();
                        List<String> spareUrls = new ArrayList<>();
                        for (String url : hostApiUrls) {
                            if (apiVisitCounts.get(url) <= 3) {
                                spareUrls.add(url);
                            }
                        }
                        // 应用新增一天以后的某一天，一个应用下突然新增超过100个接口，并且这些接口的访问量都很低
                        if (pastTime > 36 * 3600 * 1000 && spareUrls.size() >= 100) {
                            eventFilterProcess.processSubApp(appHost, appHost, spareUrls);
                            log.info("start process app {} has scan", appHost);
                        }
                    }
                } catch (Exception e) {
                    log.error("process filter error : " + appResource.getHost());
                }
            }
        }
        // 特殊攻击关键字和扫描登录接口的流量过滤
        try {
            Map<String, Set<String>> urlsByRealApp = new HashMap<>();
            Map<String, String> realAppHostMap = new HashMap<>();
            for (String needFilterKeyword : needFilterKeywords) {
                HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
                httpApiSearchDto.setApiSearch(needFilterKeyword);
                httpApiSearchDto.setDelFlag(false);
                // 查询最近12个小时新增的接口
                httpApiSearchDto.setDiscoverTimeStart(System.currentTimeMillis() - 12 * 3600 * 1000);
                httpApiSearchDto.setDiscoverTimeEnd(System.currentTimeMillis());
                ListOutputDto<HttpApiResource> httpApiListOutputDto = httpApiService.getHttpApisFields(1, 1000, null, null, httpApiSearchDto, Arrays.asList("apiUrl", "host"));

                groupApis(httpApiListOutputDto, urlsByRealApp, realAppHostMap);
            }
            HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
            httpApiSearchDto.setReqDataLabels(Arrays.asList("password"));
            httpApiSearchDto.setDelFlag(false);
            // 查询最近12个小时新增的接口
            httpApiSearchDto.setDiscoverTimeStart(System.currentTimeMillis() - 12 * 3600 * 1000);
            httpApiSearchDto.setDiscoverTimeEnd(System.currentTimeMillis());

            Map<String, Set<String>> urlsByRealAppPwd = new HashMap<>();
            ListOutputDto<HttpApiResource> httpApiListOutputDto = httpApiService.getHttpApisFields(1, 500, null, null, httpApiSearchDto, Arrays.asList("apiUrl", "host"));
            groupApis(httpApiListOutputDto, urlsByRealAppPwd, realAppHostMap);
            // 对返回数据进行判断，如果单个应用下登录接口数量很少的，则可能不是扫描，不做另外处理
            for (String key : urlsByRealAppPwd.keySet()) {
                // 单个应用下，存在超过5个请求中包含密码的接口，可能是扫描的登录
                if (urlsByRealAppPwd.get(key).size() > 5) {
                    if (urlsByRealApp.containsKey(key)) {
                        urlsByRealApp.get(key).addAll(urlsByRealAppPwd.get(key));
                    } else {
                        urlsByRealApp.put(key, urlsByRealAppPwd.get(key));
                    }
                }
            }
            for (String realApp : urlsByRealApp.keySet()) {
                if (urlsByRealApp.get(realApp).size() > 5) {
                    eventFilterProcess.processSubApp(realAppHostMap.get(realApp), realApp, urlsByRealApp.get(realApp));
                }
            }
        } catch (Exception e) {
            log.error("process url filter special url error : ", e);
        }
        // 单页面应用的过滤
        try {
            Map<String, Set<String>> urlsByRealApp = new HashMap<>();
            Map<String, String> realAppHostMap = new HashMap<>();
            HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
            httpApiSearchDto.setApiType(Arrays.asList(ApiTypeEnum.SPA_HTML.getCode()));
            httpApiSearchDto.setDelFlag(false);
            // 查询最近12个小时新增的接口
            httpApiSearchDto.setDiscoverTimeStart(System.currentTimeMillis() - 12 * 3600 * 1000);
            httpApiSearchDto.setDiscoverTimeEnd(System.currentTimeMillis());

            ListOutputDto<HttpApiResource> httpApiListOutputDto = httpApiService.getHttpApisFields(1, 1000, null, null, httpApiSearchDto, Arrays.asList("apiUrl", "host"));
            groupApis(httpApiListOutputDto, urlsByRealApp, realAppHostMap);
            // 对返回数据进行判断，如果单个应用下登录接口数量很少的，则可能不是扫描，不做另外处理
            for (String realApp : urlsByRealApp.keySet()) {
                // 单个应用下，存在超过5个请求中包含密码的接口，可能是扫描的登录
                if (urlsByRealApp.get(realApp).size() > 10) {
                    eventFilterProcess.processSubApp(realAppHostMap.get(realApp), realApp, urlsByRealApp.get(realApp));
                }
            }
        } catch (Exception e) {

        }
        if (!CollectionUtils.isEmpty(httpAppResourceList)) {
            HttpApiSearchDto apiSearchDto = new HttpApiSearchDto();
            for (HttpAppResource httpAppDto : httpAppResourceList) {
                //List<UrlStructureLeafInfo> urlStructureLeafInfoList = urlStructureService.pageNodeByLeafCount(httpAppDto.getHost(), 10);
                List<UrlStructureLeafInfo> urlStructureLeafInfoList = urlStructureService.pageNodeByUrlPath(httpAppDto.getHost(), 30);

                if (CollectionUtils.isEmpty(urlStructureLeafInfoList)) {
                    continue;
                }
                // 处理叶子节点较多的一个节点
                apiSearchDto.setHost(httpAppDto.getHost());
                apiSearchDto.setDelFlag(false);
                for (UrlStructureLeafInfo urlStructureLeafInfo : urlStructureLeafInfoList) {
                    String nodeName = urlStructureLeafInfo.getId();

                    List<HttpApiSearchDto.UrlPaths> urlPaths = new ArrayList<>();
                    HttpApiSearchDto.UrlPaths path = new HttpApiSearchDto.UrlPaths();
                    path.setUrlPath(nodeName);
                    path.setUrlNode(2);
                    urlPaths.add(path);
                    apiSearchDto.setUrlPathList(urlPaths);
                    apiSearchDto.setUrlNode(2);

                    apiSearchDto.setShowFields(Arrays.asList("apiUrl"));
                    List<String> apiUrls = new ArrayList<>();
                    try {
                        ListOutputDto<HttpApiResource> httpApis = httpApiService.getHttpApis(1, 500, null, null, apiSearchDto);
                        if (httpApis.getTotalCount() > 0) {
                            httpApis.getRows().forEach(e -> apiUrls.add(e.getApiUrl()));
                        }
                    } catch (Exception e) {

                    }
                    if (!apiUrls.isEmpty()) {
                        try {
                            List<Object> result = (List<Object>) eventFilterProcess.process(apiUrls);
                            eventFilterProcess.processApp(httpAppDto.getHost(), result);
                        } catch (Exception e) {
                            log.error("process url structure error: ", e);
                        }
                    }
                }
            }
        }
        watch.stop();
        log.info("smartFilter rule study finish,{},cost：{}秒", DateUtil.currentDateTime(), watch.getTotalTimeSeconds());

    }

    private void groupApis(ListOutputDto<HttpApiResource> httpApiListOutputDto, Map<String, Set<String>> urlsByRealApp, Map<String, String> realAppHostMap) {
        if (httpApiListOutputDto.getTotalCount() > 0) {
            for (HttpApiResource httpApi : httpApiListOutputDto.getRows()) {
                String realApp = httpApi.getHost();
                if (!urlsByRealApp.containsKey(realApp)) {
                    urlsByRealApp.put(realApp, new HashSet<>());
                }
                urlsByRealApp.get(realApp).add(httpApi.getApiUrl());
                realAppHostMap.put(realApp, httpApi.getHost());
            }
        }
    }
*/
}