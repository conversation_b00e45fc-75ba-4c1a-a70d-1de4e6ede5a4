//package com.quanzhi.auditapiv2.biz.schedule.job;
//
//import com.alibaba.nacos.api.config.annotation.NacosValue;
//import com.quanzhi.audit.analysis.enums.JobType;
//import com.quanzhi.audit.analysis.facade.TaskManagerFacade;
//import com.quanzhi.audit.mix.modularity.event.ModuleChangeEvent;
//import com.quanzhi.audit.mix.modularity.model.ModuleConfig;
//import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
//import com.quanzhi.metabase.common.utils.task.NamedThreadFactory;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.context.event.ApplicationReadyEvent;
//import org.springframework.context.event.EventListener;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDate;
//import java.time.ZoneId;
//import java.time.format.DateTimeFormatter;
//import java.util.Calendar;
//import java.util.Date;
//import java.util.Optional;
//import java.util.concurrent.Executors;
//import java.util.concurrent.TimeUnit;
//
//@Component
//@Slf4j
//public class ClickhouseQueryJob  {
//
//    private TaskManagerFacade taskManagerFacade;
//
//    @NacosValue(value = "${clickhouse.query.task.enable:true}",autoRefreshed = true)
//    private Boolean enableClickhouseQueryTask;
//
//    private static final Integer FIX_DELAY_MINUTES = 5;
//
//    private static final String EVENT = "event";
//
//    private volatile static boolean executeFlag = false;
//
//    public ClickhouseQueryJob(TaskManagerFacade taskManagerFacade){
//        this.taskManagerFacade = taskManagerFacade;
//    }
//
//    @EventListener(ApplicationReadyEvent.class)
//    public void execute(){
//        if(enableClickhouseQueryTask){
//            Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory("clickhouse-cron-query"))
//                    .scheduleAtFixedRate(() -> {
//                        try {
//                            if(executeFlag){
//                                doJob();
//                            }
//                        } catch (Throwable e) {
//                            log.error("clickhouse schedule query error:{}", e);
//                        }
//                    }, FIX_DELAY_MINUTES, FIX_DELAY_MINUTES, TimeUnit.MINUTES);
//        }
//    }
//
//    @EventListener(ModuleChangeEvent.class)
//    public void onEvent(ModuleChangeEvent event) {
//        Optional<ModuleConfig> moduleConfig = event.getModuleConfigList().stream().filter(s -> EVENT.equals(s.getModuleId())).findAny();
//        if(moduleConfig.isPresent() && moduleConfig.get().getOpenFlag() != null){
//            if(moduleConfig.get().getOpenFlag()){
//                executeFlag = true;
//            } else{
//                executeFlag = false;
//            }
//        }
//    }
//
//    /**
//     * 处理任务
//     */
//    public void doJob(){
//        try {
//            LocalDate currentDate = LocalDate.now(ZoneId.of("Asia/Shanghai"));
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//            String formatCurrentDate = currentDate.format(formatter);
//            LocalDate oneWeekAgo = currentDate.minusWeeks(1);
//            String formatOneWeekAgo = oneWeekAgo.format(formatter);
//            long startTimestamp = DateUtil.getDateStartSeconds(formatOneWeekAgo);
//            long endTimestamp = DateUtil.getDateEndSeconds(formatCurrentDate);
//            String countSql = "SELECT count(*),any(date) from future_http_defined_event WHERE timestamp >= " + startTimestamp + " AND timestamp <= " + endTimestamp;
//            String querySql = "SELECT timestamp,net_srcIp_v4,net_srcIp_v6,net_srcPort,dstIpPosition_city,net_dstIp_v4,net_dstIp_v6,net_dstPort,originIpPosition_city,account,ip_v4,ip_v6,host,apiUrl,classifications,featureLabels,apiLevel,appName,appLevel,reqContentType,reqMethod,reqDataLabelIds,rspDataLabelIds,uaType,ua,referer,rspContentType,rspContentLength,rspStatus,appFeatureLabels FROM future_http_defined_event WHERE timestamp >= " + startTimestamp + " AND timestamp <= " + endTimestamp + " ORDER BY timestamp DESC LIMIT 0,10";
//            taskManagerFacade.query(countSql, JobType.AGGTASK);
//            taskManagerFacade.query(querySql,JobType.QUERYTASK);
//        }catch (Exception e){
//            log.error("clickhouse cache query error:{}",e);
//        }
//    }
//}
