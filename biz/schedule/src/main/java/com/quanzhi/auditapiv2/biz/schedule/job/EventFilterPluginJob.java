package com.quanzhi.auditapiv2.biz.schedule.job;

import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit.mix.schdule.domain.entity.TriggerStatus;
import com.quanzhi.audit.mix.schdule.domain.utils.ParameterHelper;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IEventFilterPluginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create at 2021/4/24 11:46 下午
 * @description: 历史数据清理任务-原spark数据清理逻辑
 **/
//@Component
@Slf4j
public class EventFilterPluginJob {


    @Autowired
    private IEventFilterPluginService eventFilterPluginService;


    //@LockedScheduler(cron = "0 0 0 * * ?", executor = "eventFilterPluginJob", description = "插件过滤", triggerStatus = TriggerStatus.CLOSE)
    public void execute() throws Exception {
        String params = ParameterHelper.get();
        log.info("接收参数:{}",params);
        if(DataUtil.isEmpty(params)){
            log.info("参数为null,不执行");
            return;
        }
        eventFilterPluginService.startFilterTask(params);
    }
}