package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit.mix.schdule.domain.entity.TriggerStatus;
import com.quanzhi.audit.mix.schdule.domain.utils.ParameterHelper;
import com.quanzhi.audit_core.common.model.ApiCleanJobParam;
import com.quanzhi.auditapiv2.biz.schedule.task.CKAuditLogService;
import com.quanzhi.auditapiv2.biz.schedule.task.FileService;
import com.quanzhi.auditapiv2.biz.schedule.task.ip.IPClearService;
import com.quanzhi.auditapiv2.biz.schedule.task.risk.RiskClearService;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiAccountInfoService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.XxlJobServiceImpl;
import com.quanzhi.auditapiv2.core.trace.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ClearJob {

    public static final String MANUAL_CLEAR_RISK_JOB = XxlJobServiceImpl.MANUAL_CLEAR_RISK_JOB;
    public static final String MANUAL_CLEAR_LOG = XxlJobServiceImpl.MANUAL_CLEAR_LOG;
    public static final String MANUAL_CLEAR_FILE = XxlJobServiceImpl.MANUAL_CLEAR_FILE;
    public static final String MANUAL_CLEAR_IP = XxlJobServiceImpl.MANUAL_CLEAR_IP;
    public static final String MANUAL_CLEAR_ACCOUNT = XxlJobServiceImpl.MANUAL_CLEAR_ACCOUNT;

    private final RiskClearService riskClearService;
    private final CKAuditLogService auditCKAuditLogService;
    private final FileService fileService;
    private final IPClearService ipClearService;
    private final IApiAccountInfoService apiAccountInfoService;
    @LockedScheduler(cron = "-", executor = MANUAL_CLEAR_RISK_JOB, name = "清理风险", description = "磁盘超过阈值在“数据清理”中手动清理风险清单", triggerStatus = TriggerStatus.CLOSE)
    public void executeClearRisk(){
        ApiCleanJobParam apiCleanJobParam = getApiCleanJobParam();
        riskClearService.clearRisk(apiCleanJobParam);
    }

    @LockedScheduler(cron = "-", executor = MANUAL_CLEAR_IP, name = "清理IP", description = "磁盘超过阈值在“数据清理”中手动清理IP清单", triggerStatus = TriggerStatus.CLOSE)
    public void executeClearIP(){
        ApiCleanJobParam apiCleanJobParam = getApiCleanJobParam();
        ipClearService.clear(apiCleanJobParam);
    }
    @LockedScheduler(cron = "-", executor = MANUAL_CLEAR_ACCOUNT, name = "清理账号", description = "磁盘超过阈值在“数据清理”中手动清理账号清单", triggerStatus = TriggerStatus.CLOSE)
    public void executeClearAccount(){
        ApiCleanJobParam apiCleanJobParam = getApiCleanJobParam();
        apiAccountInfoService.clear(apiCleanJobParam.getStartTimestamp(), apiCleanJobParam.getEndTimestamp());
    }


    @LockedScheduler(cron = "-", executor = MANUAL_CLEAR_LOG, name = "清理日志", description = "磁盘超过阈值在“数据清理”中手动清理日志数据", triggerStatus = TriggerStatus.CLOSE)
    public void executeClearAuditLog(){
        ApiCleanJobParam apiCleanJobParam = getApiCleanJobParam();
        auditCKAuditLogService.clear(apiCleanJobParam);
    }

    @LockedScheduler(cron = "-", executor = MANUAL_CLEAR_FILE, name = "清理文件", description = "磁盘超过阈值在“数据清理”中手动清理文件数据", triggerStatus = TriggerStatus.CLOSE)
    public void executeClearFile(){
        ApiCleanJobParam apiCleanJobParam = getApiCleanJobParam();
        fileService.clear(apiCleanJobParam);
    }

    private static ApiCleanJobParam getApiCleanJobParam() {
        String param = ParameterHelper.get();
        if (StringUtils.isEmpty(param)){
            log.warn("executeClearRisk param is null");
        }
        return JSON.parseObject(param, ApiCleanJobParam.class);
    }

}
