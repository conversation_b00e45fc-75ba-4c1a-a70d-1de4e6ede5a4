package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.mongodb.client.MongoCollection;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2023/12/11 19:31
 * @description: 审计2.7升到3.0，接口表索引处理
 **/
@Component
@Slf4j
public class ApiIndexUpdate implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    public ApiIndexUpdate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }


    @Override
    public int getVersion() {
        return 20231211;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        IndexOperations api = mongoTemplate.indexOps("httpApi");
        List<IndexInfo> indexInfos = api.getIndexInfo();
        List<String> allIndexs = indexInfos.stream().map(e -> e.getName()).collect(Collectors.toList());
        // 删除掉delFlag的组合索引
        for (String index : allIndexs) {
            if (index.contains("delFlag")) {
                api.dropIndex(index);
            }
        }
        log.info("httpApi drop index finish");

    }
}