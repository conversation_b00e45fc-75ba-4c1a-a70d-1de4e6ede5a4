package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.AggRiskService;
import com.quanzhi.auditapiv2.common.dal.dao.home.HomeDao;
import com.quanzhi.auditapiv2.common.dal.dto.risk.SystemRiskIndexDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.SensitiveInfoDto;
import com.quanzhi.auditapiv2.common.dal.dto.securityPosture.StatisticsDto;
import com.quanzhi.auditapiv2.common.dal.dto.weakness.WeaknessDistributedDto;
import com.quanzhi.auditapiv2.common.dal.entity.RiskIndex;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.service.impl.SysRiskIndexService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.ApiWeaknessServiceImpl;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.HttpApiService;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: yangzixian
 * @date: 2022/8/9 10:22
 * @description:
 */
@Component
@Slf4j
public class SecurityPostureJob {

    @NacosValue(value = "${schedule.count.num:500000}", autoRefreshed = true)
    private Long scheduleCountNum;
    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    private final HttpApiService httpApiService;
    private final ApiWeaknessServiceImpl apiWeaknessService;
    private final AggRiskService aggRiskService;
    private final HomeDao homeDao;
    private final IHttpAppService httpAppService;
    private final SysRiskIndexService riskIndexService;

    public SecurityPostureJob(HttpApiService httpApiService, ApiWeaknessServiceImpl apiWeaknessService, AggRiskService aggRiskService, HomeDao homeDao, IHttpAppService httpAppService, SysRiskIndexService riskIndexService) {
        this.httpApiService = httpApiService;
        this.apiWeaknessService = apiWeaknessService;
        this.aggRiskService = aggRiskService;
        this.homeDao = homeDao;
        this.httpAppService = httpAppService;
        this.riskIndexService = riskIndexService;
    }

    @LockedScheduler(cron = "0 0 * * * ?", executor = "securityPostureJob", name = "态势大屏定时任务", description = "计算态势弱点分布、态势敏感信息、态势概览信息",
            interval = 1000 * 60 * 60 * 24, intervalConditionSpEL = "#root.check()")
    public void execute() throws Exception {
        doJob();
    }

    public boolean check() {
        return httpApiService.getCount(new MetabaseQuery()) > scheduleCountNum
                || httpAppService.getCount(new MetabaseQuery()) > scheduleCountNum
                || apiWeaknessService.getCount(new MetabaseQuery()) > scheduleCountNum
                || aggRiskService.totalCount() > scheduleCountNum;
    }

    public void doJob() throws Exception {

        //大屏弱点分布
        WeaknessDistributedDto bigScreenWeaknessDistribute = apiWeaknessService.getWeaknessDistribute();
        bigScreenWeaknessDistribute.setType("bigScreenWeaknessDistribute");

        //态势敏感信息分布
        SensitiveInfoDto sensitiveInfo = httpApiService.getSensitiveInfo();
        sensitiveInfo.setType("sensitiveInfo");

        //态势概览
        List<Long> securityPostureSpiStatistics = httpApiService.getStatistics();
        List<Long> securityPostureAppStatistics = httpAppService.getStatistics();
        List<Long> securityPostureWeaknessStatistics = apiWeaknessService.getStatistics();
        List<Long> securityPostureRiskStatistics = aggRiskService.getStatistics();
        RiskIndex riskIndexByDate = riskIndexService.getRiskIndexByDate(DateUtil.currentDate());
        if (riskIndexByDate == null) {
            riskIndexByDate = new RiskIndex();
            riskIndexByDate.setSystemRiskIndex(0);
            riskIndexByDate.setDate(com.quanzhi.metabase.common.utils.DateUtil.currentDate());
            riskIndexByDate.setSystemRiskIndexTrend("mid");
            riskIndexByDate.setSystemRiskLevel(0);
        }
        SystemRiskIndexDto systemRiskIndexDto = SystemRiskIndexDto.SystemRiskIndexDtoMapper.INSTANCE.convert(riskIndexByDate);
        StatisticsDto statisticsDto = StatisticsDto.builder()
                .totalApi(securityPostureSpiStatistics.get(0))
                .highLevelApi(securityPostureSpiStatistics.get(1))
                .totalApp(securityPostureAppStatistics.get(0))
                .highLevelApp(securityPostureAppStatistics.get(1))
                .totalWeakness(securityPostureWeaknessStatistics.get(0))
                .highLevelWeakness(securityPostureWeaknessStatistics.get(1))
                .totalRisk(securityPostureRiskStatistics.get(0))
                .highLevelRisk(securityPostureRiskStatistics.get(1))
                .riskIndex(systemRiskIndexDto).build();
        statisticsDto.setType("securityPostureStatistics");

        //入库
        homeDao.upsert(null, null, bigScreenWeaknessDistribute, null, sensitiveInfo, statisticsDto);

    }

}
