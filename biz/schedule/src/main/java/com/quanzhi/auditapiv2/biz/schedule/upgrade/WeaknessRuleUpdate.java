package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.common.dal.dao.IWeaknessRuleNacosDao;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2022/8/10 2:12 下午
 * @description: 弱点规则的一些字段升级
 **/
@Component
@Slf4j
public class WeaknessRuleUpdate implements UpgradeService {

    @Autowired
    private IWeaknessRuleNacosDao weaknessRuleNacosDaoImpl;

    @Override
    public int getVersion() {
        return 20220831;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        List<WeaknessRule> allRule = weaknessRuleNacosDaoImpl.getAll();
        for (WeaknessRule weaknessRule : allRule) {
            if (DataUtil.isEmpty(weaknessRule.getDelFlag())) {
                weaknessRule.setDelFlag(false);
            }
            if (DataUtil.isEmpty(weaknessRule.getMode())) {
                weaknessRule.setMode(WeaknessRule.ModeTypeEnum.SYSTEM.val());
            }
            weaknessRuleNacosDaoImpl.update(weaknessRule);
        }
        log.info("Weakness rules upgraded successfully");
    }
}