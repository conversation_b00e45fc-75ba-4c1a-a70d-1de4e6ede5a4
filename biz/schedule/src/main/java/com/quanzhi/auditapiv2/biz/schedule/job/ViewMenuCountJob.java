package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.ThreatInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.AggRiskService;
import com.quanzhi.auditapiv2.common.dal.common.entity.ViewMenu;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.service.manager.dto.app.CustomFieldDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpApiService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.auditapiv2.core.service.manager.web.common.ViewMenuService;
import com.quanzhi.auditapiv2.core.service.manager.web.data.DataService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.AccountInfoServiceImpl;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.ApiWeaknessServiceImpl;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.IpInfoServiceImpl;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yangzixian
 * @date: 21/3/2023 17:09
 * @description:
 */
@Component
@Slf4j
public class ViewMenuCountJob {

    @NacosValue(value = "${schedule.count.num:500000}", autoRefreshed = true)
    private Long scheduleCountNum;

    private final ViewMenuService viewMenuService;

    private final IHttpAppService httpAppService;

    private final IHttpApiService httpApiService;

    private final IpInfoServiceImpl ipInfoService;

    private final AccountInfoServiceImpl accountInfoService;

    private final ApiWeaknessServiceImpl apiWeaknessService;

    private final RiskInfoService riskInfoService;

    private final ThreatInfoService threatInfoService;

    private final DataService dataService;

    private final AggRiskService aggRiskService;

    public ViewMenuCountJob(ViewMenuService viewMenuService, IHttpAppService httpAppService, IHttpApiService httpApiService, IpInfoServiceImpl ipInfoService, AccountInfoServiceImpl accountInfoService, ApiWeaknessServiceImpl apiWeaknessService, RiskInfoService riskInfoService, ThreatInfoService threatInfoService, DataService dataService, AggRiskService aggRiskService) {
        this.viewMenuService = viewMenuService;
        this.httpAppService = httpAppService;
        this.httpApiService = httpApiService;
        this.ipInfoService = ipInfoService;
        this.accountInfoService = accountInfoService;
        this.apiWeaknessService = apiWeaknessService;
        this.riskInfoService = riskInfoService;
        this.threatInfoService = threatInfoService;
        this.dataService = dataService;
        this.aggRiskService = aggRiskService;
    }

    @LockedScheduler(cron = "0 0/15 * * * ?", executor = "viewMenuCountJob", name = "各视图默认值计算任务", description = "刷新各个模块的左侧视图的格式到最新状态")
    public void execute() throws Exception {
        doJob();
    }

    public void doJob() throws Exception {
        //统计各个模块数据量，超过10w的一天统计一次
        Long flag = 0L;
        if (DataUtil.isNotEmpty(scheduleCountNum)) {
            flag = Long.valueOf(scheduleCountNum);
        }
        long apiCount = httpApiService.getCount(new MetabaseQuery().where("delFlag", Predicate.IS, false));
        long appCount = httpAppService.getCount(new MetabaseQuery().where("delFlag", Predicate.IS, false));
        Long ipCount = ipInfoService.getCount();
        Long accountCount = accountInfoService.getCount();
        long weakCount = apiWeaknessService.getCount(new MetabaseQuery().where("delFlag", Predicate.IS, false));
        Long riskCount = riskInfoService.totalCount(new RiskSearchDto());
        Long threatCount = threatInfoService.getCount();
        Long dataCount = dataService.totalCount();
        Long aggRiskCount = aggRiskService.totalCount();
        log.warn("apiCount:{},appCount:{},ipCount:{},accountCount:{},weakCount:{},riskCount:{},threatCount:{},dataCount:{},countFlag:{}", apiCount, appCount, ipCount, accountCount, weakCount, riskCount, threatCount, dataCount, flag);
        Boolean api = apiCount < flag, app = appCount < flag, ip = ipCount < flag, account = accountCount < flag, weak = weakCount < flag, risk = riskCount < flag, threat = threatCount < flag, data = dataCount < flag, aggRisk = aggRiskCount < flag;
        Map<String, Boolean> countFlag = new HashMap<>();
        countFlag.put("ApiViewMenuProcess", api);
        countFlag.put("AppViewMenuProcess", app);
        countFlag.put("IpViewMenuProcess", ip);
        countFlag.put("AccountViewMenuProcess", account);
        countFlag.put("WeaknessViewMenuProcess", weak);
        countFlag.put("RiskViewMenuProcess", risk);
        countFlag.put("ThreatInfoViewMenuProcess", threat);
        countFlag.put("FileMenuProcess", true);
        countFlag.put("DataViewMenuProcess", data);
        countFlag.put("AggRiskViewMenuProcess", aggRisk);
        //获取所有视图清单
        List<ViewMenu> viewMenus = viewMenuService.getAll();
        if (DataUtil.isNotEmpty(viewMenus)) {
            List<String> nameList = httpAppService.listCustomFields().stream()
                    .map(CustomFieldDto::getName)
                    .collect(Collectors.toList());
            for (ViewMenu viewMenu : viewMenus) {
                boolean canCount = false;
                if (countFlag.containsKey(viewMenu.getId()) && countFlag.get(viewMenu.getId())) {
                    canCount = true;
                } else {
                    //判断时间是否在晚上
                    Calendar calendar = Calendar.getInstance();
                    int hour = calendar.get(Calendar.HOUR_OF_DAY);
                    if (hour >= 20) {
                        //判断视图上次计算时间是否超过一天
                        Long updateTime = viewMenu.getUpdateTime();
                        if (DataUtil.isNotEmpty(updateTime)) {
                            long millisecondsPerDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
                            long timeDifference = Math.abs(System.currentTimeMillis() - viewMenu.getUpdateTime()); // 两个时间戳的绝对时间差
                            if (timeDifference > millisecondsPerDay) {
                                canCount = true;
                            }
                        }
                    }
                }
                if (canCount) {
                    //刷新当前视图清单格式
                    ViewMenu viewMenuNow = viewMenuService.refreshViewMenu(viewMenu);
                    //标记过期的自定义字段视图
                    markViewMenu(viewMenuNow.getChildren(), nameList);
                    //清理过期的自定义字段视图
                    clearViewMenu(viewMenuNow.getChildren());
                    //调用count接口计算type为view的值
                    for (ViewMenu child : viewMenuNow.getChildren()) {
                        if (child.getType().equals("category") && child.getChildren() != null && child.getChildren().size() > 0) {
                            countViewMenu(child.getChildren(), viewMenuNow.getId());
                        } else if (child.getType().equals("view")) {
                            //type为view，计算count
                            Long count = viewMenuService.countViewMenu(child.getSearchParams(), viewMenuNow.getId(), child.getId());
                            child.setCount(count);
                        }
                    }
                    //更新数据库
                    viewMenuService.editViewMenuById(viewMenuNow.getId(), viewMenuNow);
                }
            }
        } else {
            log.error("get schedule error!");
        }
    }

    private void markViewMenu(List<ViewMenu> viewMenus, List<String> nameList) {
        for (ViewMenu viewMenu : viewMenus) {
            if (DataUtil.isNotEmpty(viewMenu.getChildren())) {
                markViewMenu(viewMenu.getChildren(), nameList);
            } else {
                if (DataUtil.isNotEmpty(viewMenu.getViewFeSearchParams()) && viewMenu.getViewFeSearchParams().containsKey("searchParams")) {
                    Map<String, String> searchParams = (Map<String, String>) viewMenu.getViewFeSearchParams().get("searchParams");
                    if (DataUtil.isNotEmpty(searchParams)) {
                        for (Object key : searchParams.keySet()) {
                            String strKey = (String) key;
                            if (strKey.contains("_")) {
                                String finalKey = strKey.split("_")[1];
                                if (!nameList.contains(finalKey)) {
                                    viewMenu.setDelFlag(true);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private void clearViewMenu(List<ViewMenu> viewMenus) {
        Iterator<ViewMenu> viewMenuIterator = viewMenus.iterator();
        while (viewMenuIterator.hasNext()) {
            ViewMenu viewMenu = viewMenuIterator.next();
            if (DataUtil.isNotEmpty(viewMenu.getDelFlag()) && viewMenu.getDelFlag()) {
                viewMenuIterator.remove();
            } else {
                if (DataUtil.isNotEmpty(viewMenu.getChildren())) {
                    clearViewMenu(viewMenu.getChildren());
                }
            }
        }
    }

    private void countViewMenu(List<ViewMenu> viewMenus, String type) throws Exception {
        if (DataUtil.isEmpty(viewMenus)) {
            return;
        }
        for (ViewMenu viewMenu : viewMenus) {
            if ("view".equals(viewMenu.getType())) {
                //type为view，计算count
                Long count = viewMenuService.countViewMenu(viewMenu.getSearchParams(), type, viewMenu.getId());
                viewMenu.setCount(count);
            } else {
                //type为category，计算children
                if (viewMenu.getChildren() != null) {
                    countViewMenu(viewMenu.getChildren(), type);
                }
            }
        }
        boolean filter = false;
        for (ViewMenu viewMenu : viewMenus) {
            if (viewMenu.getCount() == null) {
                filter = true;
                break;
            }
        }
        if (!filter) {
            Collections.sort(viewMenus, new Comparator<ViewMenu>() {
                @Override
                public int compare(ViewMenu v1, ViewMenu v2) {
                    return -Long.compare(v1.getCount(), v2.getCount());
                }
            });
        }
    }

}
