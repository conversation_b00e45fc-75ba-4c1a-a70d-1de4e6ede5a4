package com.quanzhi.auditapiv2.biz.schedule.task.account;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit.mix.schdule.domain.entity.TaskProcess;
import com.quanzhi.audit.mix.schdule.domain.repository.TaskProcessRepository;
import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
import com.quanzhi.auditapiv2.common.dal.entity.account.AbstractMonthStat;
import com.quanzhi.auditapiv2.common.dal.entity.account.AbstractTarget;
import com.quanzhi.auditapiv2.common.dal.entity.account.DateStat;
import com.quanzhi.auditapiv2.common.dal.entity.account.StatPoint;
import com.quanzhi.metabase.common.utils.DateUtil;
import com.quanzhi.metabase.common.utils.batch.AutoFlushBatch;
import com.quanzhi.metabase.common.utils.batch.ConcurrentAutoFlushBatch;
import com.quanzhi.metabase.common.utils.task.IndexedProcessor;
import com.quanzhi.metabase.common.utils.task.ProcessStat;
import com.quanzhi.metabase.core.model.node.NodeMeta;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.lang.reflect.ParameterizedType;
import java.time.Duration;
import java.util.*;

@Slf4j
public abstract class AbstractMonthStatService<T extends AbstractTarget, S extends AbstractMonthStat> {
    protected final TaskProcessRepository taskProcessRepository;

    protected final MongoTemplate mongoTemplate;
    protected final AutoFlushBatch<T> autoFlushBatch;

    private static final int SIZE = 1024;

    protected final Cache<String, T> cache = Caffeine.newBuilder().maximumSize(SIZE).expireAfterAccess(Duration.ofMinutes(5)).build();
    protected final Class<T> statClass;

    protected final Class<S> monthStatClass;

    public AbstractMonthStatService(TaskProcessRepository taskProcessRepository, MongoTemplate mongoTemplate) {
        this.taskProcessRepository = taskProcessRepository;
        this.mongoTemplate = mongoTemplate;
        // 特殊情况下一次定时任务处理同一个账号多次，这里选择最新的入库
        autoFlushBatch = new ConcurrentAutoFlushBatch<>(this::save, SIZE);
        ParameterizedType pt = (ParameterizedType) getClass().getGenericSuperclass();
        statClass = (Class<T>) pt.getActualTypeArguments()[0];
        monthStatClass = (Class<S>) pt.getActualTypeArguments()[1];
    }

    protected MongoCursor<S> cursor(long start, long end) {
        org.springframework.data.mongodb.core.mapping.Document entity = getMonthStatClass().getAnnotation(org.springframework.data.mongodb.core.mapping.Document.class);
        if (entity == null) {
            throw new IllegalStateException("not found Entity annotation");
        }
        return mongoTemplate.getCollection(entity.value()).aggregate(Collections.singletonList(new Document("$match", Query.query(Criteria.where("updateTime").gte(start).lte(end)).getQueryObject())), getMonthStatClass()).batchSize(1000).iterator();
    }

    protected Class<S> getMonthStatClass() {
        return monthStatClass;
    }

    protected Class<T> getStatClass() {
        return statClass;
    }

    public void stat() {
        TaskProcess taskProcess = taskProcessRepository.findOne("stat:" + getStatClass().getSimpleName());
        if (taskProcess == null) {
            taskProcess = new TaskProcess();
            taskProcess.setData(String.valueOf(DateUtil.getDateZeroTimestamp(new Date())));
            taskProcess.setName("stat:" + getStatClass().getSimpleName());
        }
        long start = Long.parseLong(taskProcess.getData());
        // 修改 start 为前30分钟
        start = start - 1000 * 60 * 30;
        long end = System.currentTimeMillis();
        if (end <= start) {
            return;
        }
        final long finalStart = start;
        new IndexedProcessor<S>() {
            @Override
            public void process(S ms) {
                processMonth(ms);
            }

            @Override
            protected List<S> fetch(String index, int limit) {
                Criteria criteria = Criteria.where("updateTime").gte(finalStart).lte(end);
                if (index != null) {
                    criteria.and("_id").gt(index);
                }
                return mongoTemplate.find(Query.query(criteria).with(Sort.by(Sort.Order.asc("_id"))).limit(limit), getMonthStatClass());
            }

            @Override
            protected String getIndex(List<S> items) {
                return items.get(items.size() - 1).getId();
            }

            @Override
            protected void onComplete(ProcessStat stat) {
                super.onComplete(stat);
                cache.invalidateAll();
                autoFlushBatch.flush();
            }
        }.process();
        taskProcess.setData(String.valueOf(end));
        taskProcessRepository.save(taskProcess);
    }

    protected void processMonth(S ms) {
        T ts = cache.get(ms.getKey(), s -> getTarget(ms));
        if (ts == null) {
            return;
        }
        StatPoint statPoint = ts.getStatPoint();
        if (statPoint == null) {
            statPoint = StatPoint.builder().month(ms.getMonth()).build();
            ts.setStatPoint(statPoint);
        }
        // 访问量计算不会回溯到一个月之前
        if (ms.getMonth().compareTo(statPoint.getMonth()) < 0) {
            return;
        }
        // 新的一个月，重置上次状态
        if (!ms.getMonth().equals(statPoint.getMonth())) {
            statPoint.setMonth(ms.getMonth());
            statPoint.setAmount(0L);
        }
        long total = 0;
        // 遍历当月所有数据
        for (Map.Entry<String, DateStat> entry : ms.getAllDateStats().entrySet()) {
            DateStat dateStat = entry.getValue();
            if (dateStat == null) {
                continue;
            }
            // 当月数据量
            total += dateStat.getAmount();
            ts.setVisitCnt(ts.getVisitCnt() + dateStat.getAmount());
            process(ts, dateStat);
        }
        // 减去上次统计
        ts.setVisitCnt(ts.getVisitCnt() - statPoint.getAmount());
        statPoint.setAmount(total);
        process(ts, ms);
        if (ts.getUaByUaType() != null) {
            if (ts.getUaTypes() == null) {
                ts.setUaTypes(new HashSet<>(ts.getUaByUaType().keySet()));
            } else {
                Set<String> uas = new HashSet<>();
                uas.addAll(ts.getUaTypes());
                uas.addAll(ts.getUaByUaType().keySet());
                ts.setUaTypes(uas);
            }
        }
        autoFlushBatch.add(ts);
    }

    protected abstract T getTarget(S s);

    protected abstract String getField();

    protected void process(T ts, S s) {
        // 多节点直接使用其他节点的访问量进行累加
        if (ts.getNodes() != null && !ts.getNodes().isEmpty()) {
            long sum = ts.getNodes().stream().map(NodeMeta::getExtData).map(m -> m.get("visit")).filter(Objects::nonNull).map(v -> Long.valueOf(v.toString())).reduce(Long::sum).orElse(ts.getVisitCnt());
            ts.setVisitCnt(sum);
            // 设置时间
            buildVisitDate(ts, s, s.getAllDateStats());
        }
        // 设置时间
        buildVisitDate(ts, s, s.getDateStats());
    }

    protected void process(T ts, DateStat dateStat) {
    }

    private static <T extends AbstractTarget, S extends AbstractMonthStat> void buildVisitDate(T ts, S s, Map<String, DateStat> dateStatMap) {
        String max = dateStatMap.keySet().stream().max(Comparator.comparingInt(Integer::parseInt)).orElse(null);
        if (max == null) {
            return;
        }
        if (max.length() == 1) {
            max = "0" + max;
        }
        ts.setLastDate(s.getMonth() + max);
        if (ts.getFirstDate() == null) {
            ts.setFirstDate(ts.getLastDate());
        }
    }

    protected abstract void save(List<T> list);

    public long count() {
        return mongoTemplate.estimatedCount(getStatClass());
    }

    protected boolean checkReviveAdd(AssetLifeStateConfig stateConfig, Long preActiveTime) {
        Integer revivePreActiveTimeDay = stateConfig.getRevivePreActiveTimeDay();
        Date date = com.quanzhi.metabase.common.utils.DateUtil.addDate(new Date(), 5, -(revivePreActiveTimeDay));
        long revivePreActiveDatePoint = com.quanzhi.metabase.common.utils.DateUtil.getDateLastTimestamp(date);
        return preActiveTime != null && preActiveTime <= revivePreActiveDatePoint;
    }

}
