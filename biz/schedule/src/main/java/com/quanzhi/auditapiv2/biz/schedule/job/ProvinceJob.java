//package com.quanzhi.auditapiv2.biz.schedule.job;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.nacos.api.config.annotation.NacosValue;
//import com.mongodb.client.MongoCursor;
//import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
//import com.quanzhi.audit.mix.schdule.domain.utils.ParameterHelper;
//import com.quanzhi.auditapiv2.common.dal.dao.impl.HttpApiDaoImpl;
//import com.quanzhi.auditapiv2.common.dal.dao.impl.HttpAppDaoImpl;
//import com.quanzhi.auditapiv2.common.dal.entity.IpDateInfo;
//import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
//import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
//import com.quanzhi.auditapiv2.openapi.sdk.util.HttpsUtils;
//import com.quanzhi.metabase.core.model.http.HttpApiResource;
//import com.quanzhi.metabase.core.model.http.HttpAppResource;
//import lombok.extern.slf4j.Slf4j;
//import okhttp3.OkHttpClient;
//import okhttp3.Request;
//import org.bson.Document;
//import org.bson.json.Converter;
//import org.bson.json.JsonWriterSettings;
//import org.bson.json.StrictJsonWriter;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.stereotype.Component;
//
//import java.io.UnsupportedEncodingException;
//import java.util.ArrayList;
//import java.util.Base64;
//import java.util.List;
//import java.util.Set;
//import java.util.concurrent.TimeUnit;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
///**
// * @Author: K, 小康
// * @Date: 2024/05/10/下午3:12
// * @Description:
// */
//
//@Component
//@Slf4j
//@ConditionalOnProperty(name = "product.type",  havingValue = "ltsk_v2")
//public class ProvinceJob {
//
//    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
//    private String productType;
//    @NacosValue(value = "${rizhiyi.url:http://************}", autoRefreshed = true)
//    private String url;
//    @NacosValue(value = "${rizhiyi.username:admin}", autoRefreshed = true)
//    private String username;
//    @NacosValue(value = "${rizhiyi.password:n1=P47`0HY}", autoRefreshed = true)
//    private String password;
//
//    private final HttpApiDaoImpl httpApiDao;
//
//    private final HttpAppDaoImpl httpAppDao;
//
//    private final MongoTemplate mongoTemplate;
//
//    public static OkHttpClient okHttpClient = null;
//
//    public ProvinceJob(HttpApiDaoImpl httpApiDao, @Qualifier("httpAppDaoImpl") HttpAppDaoImpl httpAppDao, MongoTemplate mongoTemplate) {
//        this.httpApiDao = httpApiDao;
//        this.httpAppDao = httpAppDao;
//        this.mongoTemplate = mongoTemplate;
//    }
//
//    @LockedScheduler(cron = "0 0 1 * * ?", executor = "ProvinceJob", description = "日志易获取省份信息")
//    public void execute() throws Exception {
//        if (productType.equals(ProductTypeEnum.ltsk_v2.name())){
//            doJob();
//        }
//    }
//
//    /**
//     * http://************
//     * admin
//     * n1=P47`0HY
//     */
//    public void doJob(){
//        String header = Base64.getEncoder().encodeToString((username + ":" + password).getBytes());
//        OkHttpClient okHttpClient = createOkHttpClient();
//        int logCount = 0;
//        try (MongoCursor<Document> cursor =
//                     mongoTemplate.getCollection("httpApi")
//                             .find(new Query().getQueryObject())
//                             .batchSize(1000)
//                             .noCursorTimeout(true)
//                             .cursor()) {
//            JsonWriterSettings settings = JsonWriterSettings.builder().int64Converter(new Converter<Long>() {
//                public void convert(Long value, StrictJsonWriter writer) {
//                    writer.writeNumber(value.toString());
//                }
//            }).build();
//            while (cursor.hasNext()) {
//                HttpApiResource httpApiResource = JSONObject.parseObject(cursor.next().toJson(settings), HttpApiResource.class);
//                List<String> deviceHash = httpApiResource.getDeviceHash();
//
//                HttpAppResource httpAppResource = getHttpAppResource(httpApiResource.getAppUri());
//                if (httpAppResource == null){
//                    continue;
//                }
//                Set<String> deployIps = httpAppResource.getDeployIps();
//                if (DataUtil.isEmpty(deviceHash) || DataUtil.isEmpty(deployIps)){
//                    continue;
//                }
//
//                String deviceHashStr = String.join(",",deviceHash);
//                String deployIpStr = String.join(",",deployIps);
//                String sid = getSid(deviceHashStr, deployIpStr, logCount, header, okHttpClient);
//                if (DataUtil.isNotEmpty(sid)){
//                    List<String> province = getRes(sid, logCount, header, okHttpClient);
//                    httpApiDao.updateProvince(httpApiResource.getUri(),province);
//                }
//            }
//
//        }
//
//
//    }
//
//    public static OkHttpClient createOkHttpClient(){
//        if (okHttpClient == null){
//            okHttpClient = (HttpsUtils.getTrustAllClientBuilder())
//                    .connectTimeout(30L, TimeUnit.SECONDS)
//                    .readTimeout(30L, TimeUnit.SECONDS)
//                    .build();
//        }
//        return okHttpClient;
//    }
//
//    private HttpAppResource getHttpAppResource(String appUri){
//        try {
//            return httpAppDao.selectHttpAppByUri(appUri);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    private String getSid(String deviceHashStr,String deployIpStr,Integer logCount, String header,OkHttpClient okHttpClient){
//        String reqUrl = url + "/api/v3/search/submit/?timeline=false&statsevents=true&category=search&time_range=1711209600000,1713888000000&page=0&size=20&order=desc&datasets=[]&filters=&now=&test_mode=false&fields=false&fromSearch=true&app_id=1&session_id=96387daa-6a0a-4179-aa63-cf4a677cdf3e&terminated_after_size=" +
//                "&searchMode=simple&market_day=0&highlight=false&onlySortByTimestamp=false&parameters=%7B%7D&use_spark=false&_t=1713947052593&version=5&query=index%3D*+*%7Cbucket+timestamp+span%3D168413s+as+ts%7Cstats+count()+by+ts&timezone=Asia%2FShanghai&lang=zh_CN&traceid=ba098fda67cb490a8f96502d29ffd023" +
//                "&should_trace=true&parent_spanid=&spanid=1ddf6e01e3bd4838a54f7ea92d2fc3e1&app_scope=1http://************/api/v3/search/submit/?timeline=false&statsevents=true&category=search&time_range=1711209600000,1713888000000&page=0&size=20&order=desc&datasets=[]&filters=&now=&test_mode=false&fields=false" +
//                "&fromSearch=true&app_id=1&session_id=96387daa-6a0a-4179-aa63-cf4a677cdf3e&terminated_after_size=&searchMode=simple&market_day=0&highlight=false&onlySortByTimestamp=false&parameters=%7B%7D&use_spark=false&_t=1713947052593&version=5" +
//                "&query=" +"`ip_hash(\""+deviceHashStr+"\",\""+deployIpStr+"\")`"+
//                "&timezone=Asia%2FShanghai&lang=zh_CN&traceid=ba098fda67cb490a8f96502d29ffd023&should_trace=true&parent_spanid=&spanid=1ddf6e01e3bd4838a54f7ea92d2fc3e1&app_scope=1";
//
//        //{"sid": "407000802000e0601080d050f0b0707010f0103020406000408ae73de134a3aa", "traceid": "ba098fda67cb490a8f96502d29ffd023", "rc": 0, "type": "stats", "result": true}
//        Request request = new Request.Builder()
//                .get()
//                .addHeader("Authorization","Basic "+header)
//                .url(reqUrl)
//                .build();
//        String string = "";
//        try {
//            string = okHttpClient.newCall(request).execute().body().string();
//            if (DataUtil.isNotEmpty(string)){
//                JSONObject jsonObject = JSONObject.parseObject(string);
//                return jsonObject.getString("sid");
//            }
//        }catch (Exception e){
//            logCount ++;
//            if (logCount < 100){
//                log.error("请求日志易请求的路径:{}，结果：{}失败原因：",reqUrl,string,e);
//            }
//        }
//        return "";
//    }
//    private List<String>  getRes(String sid,Integer logCount, String header,OkHttpClient okHttpClient){
//        List<String> province = new ArrayList<>();
//        String reqUrl = url + "/api/v3/search/fetch/?sid=" + sid + "&page=0&size=500";
//        //{"rc": 0, "job_status": "COMPLETED", "type": "stats", "progress": 100, "rows_progress": 100, "fields_progress": 100, "download_info": {}, "results": {"hint_message": null, "total_hits": 0, "starttime": 1711209600000, "endtime": 1713888000000, "sheets": {"rows": [{"department": "\u4e2d\u56fd\u8054\u901a\u6e56\u5317\u7701\u5206\u516c\u53f8,\u4e2d\u56fd\u8054\u901a\u6c5f\u897f\u7701\u5206\u516c\u53f8"}], "version": 1, "_field_infos_": [{"name": "department", "type": "string", "source": "STATS"}], "total": 1, "group_by_num": 0, "tokenize_info": []}}, "result": true, "traceid": "c199b6ac98cc4791937875cb3d93848c"}{"rc": 0, "job_status": "COMPLETED", "type": "stats", "progress": 100, "rows_progress": 100, "fields_progress": 100, "download_info": {}, "results": {"hint_message": null, "total_hits": 0, "starttime": 1711209600000, "endtime": 1713888000000, "sheets": {"rows": [{"department": "\u4e2d\u56fd\u8054\u901a\u6e56\u5317\u7701\u5206\u516c\u53f8,\u4e2d\u56fd\u8054\u901a\u6c5f\u897f\u7701\u5206\u516c\u53f8"}], "version": 1, "_field_infos_": [{"name": "department", "type": "string", "source": "STATS"}], "total": 1, "group_by_num": 0, "tokenize_info": []}}, "result": true, "traceid": "c199b6ac98cc4791937875cb3d93848c"}
//        Request request = new Request.Builder()
//                .get()
//                .addHeader("Authorization","Basic "+header)
//                .url(reqUrl)
//                .build();
//        String string = "";
//        try {
//            string = okHttpClient.newCall(request).execute().body().string();
//            if (DataUtil.isNotEmpty(string)){
//                JSONObject jsonObject = JSONObject.parseObject(string);
//                JSONObject results = jsonObject.getJSONObject("results");
//                if (DataUtil.isNotEmpty(request)){
//                    JSONObject sheets = results.getJSONObject("sheets");
//                    if (DataUtil.isNotEmpty(sheets)){
//                        JSONArray rows = sheets.getJSONArray("rows");
//                        if (DataUtil.isNotEmpty(rows)){
//                            for (int i = 0; i < rows.size(); i++) {
//                                String department = rows.getJSONObject(i).getString("department");
//                                if (DataUtil.isNotEmpty(department)){
//                                    String encoding = DataUtil.utf8Encoding(department);
//                                    for (String str : encoding.split(",")) {
//                                        if (!province.contains(str)){
//                                            province.add(str);
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }catch (Exception e){
//            logCount ++;
//            if (logCount < 100){
//                log.error("请求日志易请求的路径:{}，结果：{}失败原因：",reqUrl,string,e);
//            }
//        }
//        return province;
//
//
//    }
//
//}
