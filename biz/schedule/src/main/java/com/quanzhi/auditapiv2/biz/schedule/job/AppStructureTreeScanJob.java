//package com.quanzhi.auditapiv2.biz.schedule.job;
//
//import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
//import com.quanzhi.auditapiv2.biz.schedule.task.structure.AppStructureTreeScanService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * create at 2021/12/31 11:15 上午
// * @description: 应用结构树扫描定时任务
// **/
//@Component
//@Slf4j
//public class AppStructureTreeScanJob {
//
//
//    private final AppStructureTreeScanService appStructureTreeScanService;
//
//    public AppStructureTreeScanJob(AppStructureTreeScanService appStructureTreeScanService) {
//        this.appStructureTreeScanService = appStructureTreeScanService;
//    }
//
//
//    @LockedScheduler(cron = "0 0 */6 * * ?", name = "智能过滤学习", executor = "AppStructureTreeScanJob", description = "智能过滤学习")
//    public void execute() throws Exception {
//        doJob();
//    }
//
//
//    private void doJob() {
//        appStructureTreeScanService.scan();
//    }
//}