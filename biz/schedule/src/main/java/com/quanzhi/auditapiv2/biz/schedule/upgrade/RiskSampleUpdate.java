package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.mongodb.client.MongoCursor;
import com.mongodb.client.result.DeleteResult;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.core.risk.repository.RiskSampleRepository;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class RiskSampleUpdate implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    private final RiskSampleRepository riskSampleRepository;

    private String collectionName = "riskSample";

    public RiskSampleUpdate(MongoTemplate mongoTemplate, RiskSampleRepository riskSampleRepository) {
        this.mongoTemplate = mongoTemplate;
        this.riskSampleRepository = riskSampleRepository;
    }

    @Override
    public int getVersion() {
        return 20240902;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        mark();
        DeleteResult removeUnTrue = mongoTemplate.remove(new Query().addCriteria(
                Criteria.where("createTime").lt(System.currentTimeMillis())
                        .and("isLive").is(false)
        ), collectionName);
        log.warn("clear num：" + removeUnTrue.getDeletedCount());
    }

    public void mark() {
        List<String> riskIds = new ArrayList<>();
        //获取所有风险id
        try (MongoCursor<Document> cursor =
                     mongoTemplate.getCollection("riskInfo").
                             find().noCursorTimeout(true).batchSize(100).cursor()) {
            while (cursor.hasNext()) {
                Document riskInfo = cursor.next();
                riskIds.add(riskInfo.getString("_id"));
                if (riskIds.size() >= 100) {
                    //in操作标记使用中的sample
                    riskSampleRepository.updateRiskSampleLiveByRiskIds(riskIds);
                    riskIds.clear();
                }
            }
            if (!riskIds.isEmpty()) {
                //in操作标记使用中的sample
                riskSampleRepository.updateRiskSampleLiveByRiskIds(riskIds);
            }
        }
    }

}