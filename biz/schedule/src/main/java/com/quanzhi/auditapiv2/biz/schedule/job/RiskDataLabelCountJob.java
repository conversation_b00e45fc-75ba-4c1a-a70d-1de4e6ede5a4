package com.quanzhi.auditapiv2.biz.schedule.job;

import com.clearspring.analytics.stream.cardinality.HyperLogLogPlus;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.auditapiv2.biz.risk.service.DefinedEventService;
import com.quanzhi.auditapiv2.biz.risk.service.RiskDataLabelCountService;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.risk.entity.DataRevealLabelCount;
import com.quanzhi.auditapiv2.core.risk.entity.DefinedEvent;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.service.NacosDataServiceBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2021/8/13 11:04 上午
 * @description: 数据泄漏风险标签数据统计
 **/
//@Component
@Slf4j
public class RiskDataLabelCountJob{

    @Autowired
    private RiskInfoService riskInfoService;

    @Autowired
    private RiskDataLabelCountService riskDataLabelCountService;

    @Autowired
    private DefinedEventService definedEventService;

    @Autowired
    private NacosDataServiceBuilder nacosDadaServiceBuilder;


    //@LockedScheduler(cron = "0 0 0/1 * * ?", executor = "riskDataLabelCountJob", description = "数据泄漏风险标签数据统计")
    public void execute() throws Exception {
        doJob();
    }

    public void doJob() {
        log.info("开始进行数据泄漏风险标签数据统计,{}", DateUtil.currentDateTime());
        StopWatch watch = new StopWatch();
        watch.start("riskDataLabelCountJob-task");
        List<RiskInfo> riskInfoList = riskInfoService.selectDataRevealRisk(true);
        log.info("获取泄露类风险{}条", riskInfoList.size());
        List<String> hasCountRiskIds = riskDataLabelCountService.getAllRiskIds();
        List<DataRevealLabelCount> insertDataRevealLabelList=new ArrayList<>();
        try {
            for (RiskInfo riskInfo : riskInfoList) {
                if (hasCountRiskIds.contains(riskInfo.getId())) {
                    continue;
                }
                String sql = riskInfo.createEventQuerySqlSegment();
                List<DefinedEvent.LabelValue> dataLabelInfos = definedEventService.getDataLabelInfo(sql);

                List<DataRevealLabelCount.DataLabelCount> dataLabelCounts=new ArrayList<>();
                for (DefinedEvent.LabelValue labelValue:dataLabelInfos) {
                    DataRevealLabelCount.DataLabelCount dataLabelCount = new DataRevealLabelCount.DataLabelCount();
                    dataLabelCount.setDataLabel(conversionDataLabel(labelValue.getLabel()));
                    dataLabelCount.setDistinctDataLabelValList(labelValue.getValues());
                    dataLabelCounts.add(dataLabelCount);
                }
                DataRevealLabelCount dataRevealLabelCount = new DataRevealLabelCount();
                dataRevealLabelCount.setRiskId(riskInfo.getId());
                dataRevealLabelCount.setCreateTime(System.currentTimeMillis());
                dataRevealLabelCount.setDataLabelCountList(dataLabelCounts);
                insertDataRevealLabelList.add(dataRevealLabelCount);

            }
            if(insertDataRevealLabelList.size()>0){
                riskDataLabelCountService.addDataRevealLabelCount(insertDataRevealLabelList);
            }
            //计算全量标签值
            riskDataLabelCountService.statisticsDataLabel();

        } catch (Exception exception) {
            log.error("数据泄漏风险标签数据统计出错:{}", exception);
        }
        watch.stop();
        log.info("数据泄漏风险标签数据统计结束,{},耗时：{}秒", DateUtil.currentDateTime(), watch.getTotalTimeSeconds());
    }


    /**
     * @param dataLabelId
     * <AUTHOR>
     * @description: 数据标签中英文转换
     * @date: 2021/8/23
     * @Return java.lang.String
     */
    private String conversionDataLabel(String dataLabelId) {
        Map<String, DataLabel> dataLabelMap = nacosDadaServiceBuilder.build().getDataLabelMap();
        DataLabel dataLabel = dataLabelMap.get(dataLabelId);
        return DataUtil.isNotEmpty(dataLabel) ? dataLabel.getName() : dataLabelId;
    }


    public static void main(String[] args) {
        HyperLogLogPlus hyperLogLogPlus = new HyperLogLogPlus(11, 16);
        for (int i = 0; i < 10000; i++) {
            hyperLogLogPlus.offer("aaa" + i);
            hyperLogLogPlus.offer("bbb" + i);
        }
        System.out.println(hyperLogLogPlus.cardinality());
    }


}