package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import cn.hutool.core.util.RandomUtil;
import com.mongodb.DuplicateKeyException;
import com.mongodb.MongoException;
import com.mongodb.MongoWriteException;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.IndexOptions;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.risk.RiskInfo;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonDocument;
import org.bson.BsonString;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class CreateRiskUniqueIndex implements UpgradeService {

    private final MongoTemplate mongoTemplate;

    public CreateRiskUniqueIndex(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public int getVersion() {
        return 20240903;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        MongoCollection<Document> riskInfo = mongoTemplate.getCollection("riskInfo");
        boolean flag = false;
        while (!flag) {
            try {
                riskInfo.createIndex(new Document("operationId", 1), new IndexOptions().unique(true));
                flag = true;
            } catch (DuplicateKeyException e) {
                if (DataUtil.isNotEmpty(e.getResponse())) {
                    BsonDocument response = e.getResponse();
                    String existingOperationId = response.getDocument("keyValue").getString("operationId").getValue();
                    List<RiskInfo> riskInfos = mongoTemplate.find(new Query().addCriteria(Criteria.where("operationId").is(existingOperationId)), RiskInfo.class, "riskInfo");
                    for (int i = 0; i < riskInfos.size() - 1; i++) {
                        String newOperationId = generatorByDefault(riskInfos.get(i).getFirstTime());
                        Update update = new Update();
                        update.set("operationId", newOperationId);
                        mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(riskInfos.get(i).getId())), update, "riskInfo");
                    }
                } else {
                    throw e;
                }
            }
        }
    }

    private String generatorByDefault(Long timeStamp) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        String formattedDate = dateFormat.format(timeStamp);

        //随机生成
        StringBuffer buffer = new StringBuffer();
        String formattedStr = RandomUtil.randomString(5);
        buffer.append("R").append("-").append(formattedDate).append("-").append(formattedStr);
        return buffer.toString();
    }

}