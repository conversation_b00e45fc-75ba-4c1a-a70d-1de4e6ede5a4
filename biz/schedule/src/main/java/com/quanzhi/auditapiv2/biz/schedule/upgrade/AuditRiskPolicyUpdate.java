package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2023/11/14 2:12 下午
 * @description: 审计风险策略升级到API风险策略
 **/
@Component
@Slf4j
public class AuditRiskPolicyUpdate implements UpgradeService {

    private final MongoTemplate mongoTemplate;

//    private final RiskPolicyService riskPolicyService;

//    private final RiskPolicyAllowListService riskPolicyAllowListService;

    private final String collectionName = "riskPolicy";

    public AuditRiskPolicyUpdate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

//    public AuditRiskPolicyUpdate(MongoTemplate mongoTemplate, RiskPolicyService riskPolicyService, RiskPolicyAllowListService riskPolicyAllowListService) {
//        this.mongoTemplate = mongoTemplate;
//        this.riskPolicyService = riskPolicyService;
//        this.riskPolicyAllowListService = riskPolicyAllowListService;
//    }

    @Override
    public int getVersion() {
        return 20231130;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        log.info("update audit riskPolicy,{}", DateUtil.currentDateTime());

//        //获取审计风险规则
//        List<AuditRiskPolicy> auditRiskPolicies = mongoTemplate.find(new Query().addCriteria(
//                Criteria.where("isDefaultRisk").exists(false)
//        ), AuditRiskPolicy.class, collectionName);
//        //获取api所有指标枚举类
//        JSONObject riskPolicyTemplate = riskPolicyService.getRiskPolicyTemplate();
//        List<JSONObject> entities = riskPolicyTemplate.getJSONArray("entities").toJavaList(JSONObject.class);
//        List<JSONObject> templates = riskPolicyTemplate.getJSONArray("templates").toJavaList(JSONObject.class);
//        Map<String, String> entityMap = new HashMap<>();
//        //name为key path为value
//        for (JSONObject entity : entities) {
//            List<JSONObject> features = entity.getJSONArray("features").toJavaList(JSONObject.class);
//            for (JSONObject feature : features) {
//                if (!entityMap.containsKey(feature.getString("name"))) {
//                    entityMap.put(feature.getString("name"), feature.getString("path"));
//                }
//            }
//        }
//        Map<String, String> tempMap = new HashMap<>();
//        //name为key path为value
//        for (JSONObject template : templates) {
//            List<JSONObject> features = template.getJSONArray("features").toJavaList(JSONObject.class);
//            for (JSONObject feature : features) {
//                if (!feature.containsKey("default")) {
//                    if (!tempMap.containsKey(feature.getString("name"))) {
//                        tempMap.put(feature.getString("name"), feature.getString("path"));
//                    }
//                }
//            }
//        }
//        //审计策略id和api策略id映射
//        Map<String, String> audit2api = getStringStringMap();
//        for (AuditRiskPolicy auditRiskPolicy : auditRiskPolicies) {
//            boolean doContinue = false;
//            //构造API策略
//            JSONObject apiPolicy = new JSONObject();
//            //审计策略entityRule to eventRule，替换api指标名称和path
//            Rule entityRule = auditRiskPolicy.getEntityRule();
//            if (DataUtil.isNotEmpty(entityRule) && DataUtil.isNotEmpty(entityRule.getExprList()) && !entityRule.getExprList().isEmpty()) {
//                List<Expression> entityExprList = entityRule.getExprList();
//                for (Expression expression : entityExprList) {
//                    if (expression.getLeft() == null || expression.getRight() == null) {
//                        doContinue = true;
//                        break;
//                    }
//                    String name = expression.getLeft().getName();
//                    if (audit2api.containsKey(name)) {
//                        String newName = audit2api.get(name);
//                        String newPath = entityMap.get(newName);
//                        expression.getLeft().setName(newName);
//                        expression.getLeft().setValue(newPath);
//                        if (newPath.contains("accessDomains") || newPath.contains("deployDomains")) {
//                            expression.getLeft().setVarName("domains");
//                        } else if (newPath.contains("region")) {
//                            expression.getLeft().setVarName("region");
//                        } else {
//                            expression.getLeft().setVarName("object");
//                        }
//                    } else {
//                        //找不到某个对应key，转换下一个
//                        doContinue = true;
//                        break;
//                    }
//                }
//                //检查是否停止转换
//                if (doContinue) {
//                    //删除策略
//                    mongoTemplate.remove(new Query().addCriteria(Criteria.where("_id").is(auditRiskPolicy.getId())), collectionName);
//                    continue;
//                }
//                //构造新的eventRule
//                JSONObject eventRule = new JSONObject();
//                eventRule.put("logicalExpr", entityRule.getLogicalExpr());
//                eventRule.put("exprList", entityExprList);
//                apiPolicy.put("eventRule", eventRule);
//            } else {
//                apiPolicy.put("eventRule", new JSONObject());
//            }
//
//            //审计策略quotaRule替换api指标名称和path
//            Rule quotaRule = auditRiskPolicy.getQuotaRule();
//            if (DataUtil.isEmpty(quotaRule)) {
//                //删除策略
//                mongoTemplate.remove(new Query().addCriteria(Criteria.where("_id").is(auditRiskPolicy.getId())), collectionName);
//                continue;
//            }
//            List<Expression> quotaExprList = quotaRule.getExprList();
//            List<Expression> sampleExprList = new ArrayList<>();
//            for (Expression expression : quotaExprList) {
//                if (expression.getLeft() == null || expression.getRight() == null) {
//                    doContinue = true;
//                    break;
//                }
//                String name = expression.getLeft().getName();
//                if (audit2api.containsKey(name)) {
//                    String newName = audit2api.get(name);
//                    expression.getLeft().setName(newName);
//                    String newPath = tempMap.get(newName);
//                    expression.getLeft().setValue(newPath);
//                    String leftValue = getLeftValue(expression, auditRiskPolicy);
//                    if (DataUtil.isNotEmpty(leftValue)) {
//                        expression.getLeft().setValue(leftValue);
//                    }
//
//                    if (newPath.contains("accessDomains") || newPath.contains("deployDomains")) {
//                        expression.getLeft().setVarName("domains");
//                    } else if (newPath.contains("region")) {
//                        expression.getLeft().setVarName("region");
//                    } else {
//                        expression.getLeft().setVarName("object");
//                    }
//
//                    Expression sampleExpression = new Expression();
//                    sampleExpression.setLeft(expression.getLeft());
//                    sampleExpression.setLeftDesc(expression.getLeftDesc());
//                    sampleExpression.setOperator(expression.getOperator());
//                    VariableModel right = getVariableModel(expression);
//                    sampleExpression.setRight(right);
//                    sampleExpression.setLeftValue(expression.getLeftValue());
//                    sampleExprList.add(sampleExpression);
//                } else {
//                    //找不到某个对应key，转换下一个
//                    doContinue = true;
//                    break;
//                }
//            }
//            //检查是否停止转换
//            if (doContinue) {
//                //删除策略
//                mongoTemplate.remove(new Query().addCriteria(Criteria.where("_id").is(auditRiskPolicy.getId())), collectionName);
//                continue;
//            }
//            //构造新的quotaRule
//            JSONObject newQuotaRule = new JSONObject();
//            newQuotaRule.put("logicalExpr", quotaRule.getLogicalExpr());
//            newQuotaRule.put("exprList", quotaExprList);
//            apiPolicy.put("quotaRule", newQuotaRule);
//            //构造sampleConfig
//            JSONObject sampleConfig = new JSONObject();
//            sampleConfig.put("name", "dsl");
//            sampleConfig.put("limit", 50);
//            sampleConfig.put("phase", "AFTER");
//            JSONObject properties = new JSONObject();
//            JSONObject rule = new JSONObject();
//            rule.put("exprList", sampleExprList);
//            rule.put("logicalExpr", quotaRule.getLogicalExpr());
//            properties.put("rule", rule);
//            sampleConfig.put("properties", properties);
//            apiPolicy.put("sampleConfig", sampleConfig);
//
//            //补充 createTime
//            apiPolicy.put("createTime", System.currentTimeMillis());
//
//            //获取部分信息
//            List<String> allowListTypes = new ArrayList<>();
//            List<String> aggConfigEntities = new ArrayList<>();
//            auditRiskPolicy.getEntityConfig().forEach(entityConfig -> {
//                String type = entityConfig.getType();
//                if (entityConfig.getRelation().equals("ENTITY")) {
//                    aggConfigEntities.add(type);
//                    //补充 entity
//                    apiPolicy.put("entity", type);
//                }
//                if (entityConfig.getRelation().equals("CHANNEL")) {
//                    aggConfigEntities.add(type);
//                }
//                allowListTypes.add(type);
//            });
//
//            //补充 aggConfig
//            JSONObject aggConfig = new JSONObject();
//            String date = "";
//            if (auditRiskPolicy.getRiskTimeConfig() != null && auditRiskPolicy.getRiskTimeConfig().getType() != null) {
//                date = auditRiskPolicy.getRiskTimeConfig().getType().name();
//            } else {
//                date = "DATE";
//            }
//            aggConfig.put("entities", aggConfigEntities);
//            aggConfig.put("type", date);
//            apiPolicy.put("aggConfig", aggConfig);
//
//            //根据 entityConfig 补充 allowListTypes
//            apiPolicy.put("allowListTypes", allowListTypes);
//
//            //delFlag
//            apiPolicy.put("delFlag", false);
//
//            //补充 isDefaultRisk false
//            apiPolicy.put("isDefaultRisk", false);
//
//            //enable
//            apiPolicy.put("enable", true);
//
//            //entityConfig to entityConfigs
//            apiPolicy.put("entityConfigs", auditRiskPolicy.getEntityConfig());
//
//            //eventRuleTag
//            apiPolicy.put("eventRuleTag", auditRiskPolicy.getEntityRuleHash());
//
//            //granularity
//            if (auditRiskPolicy.getGranularity().equals("event")) {
//                apiPolicy.put("granularity", "EVENT");
//            } else {
//                apiPolicy.put("granularity", "STAT");
//            }
//
//            //group
//            apiPolicy.put("group", "审计升级策略");
//
//            //level
//            apiPolicy.put("level", Integer.parseInt(auditRiskPolicy.getLevel()));
//
//            //name
//            apiPolicy.put("name", "audit-" + auditRiskPolicy.getName());
//
//            //riskTimeConfig
//            apiPolicy.put("riskTimeConfig", auditRiskPolicy.getRiskTimeConfig());
//
//            //threatLabel
//            apiPolicy.put("threatLabel", "audit" + new Random().nextInt(1000));
//
//            //thresholdConfig
//            JSONObject thresholdConfig = new JSONObject();
//            thresholdConfig.put("type", "NAMED");
//            thresholdConfig.put("varName", auditRiskPolicy.getQuotaRule().getExprList().get(0).getLeft().getVarName());
//            thresholdConfig.put("value", auditRiskPolicy.getQuotaRule().getExprList().get(0).getLeft().getValue());
//            apiPolicy.put("thresholdConfig", thresholdConfig);
//
//            //type
//            String type = "API" + apiPolicy.getString("entity") + apiPolicy.getJSONObject("riskTimeConfig").getString("type");
//            apiPolicy.put("type", type);
//
//            //id
//            apiPolicy.put("id", "audit-" + auditRiskPolicy.getId());
//
            //删除原策略
//            mongoTemplate.remove(new Query().addCriteria(Criteria.where("_id").is(auditRiskPolicy.getId())), collectionName);
            //更新原策略的排除名单
//            riskPolicyAllowListService.updatePolicyId(auditRiskPolicy.getId());
            //原策略改id重新插入
//            auditRiskPolicy.setId("audit-" + auditRiskPolicy.getId());
//            mongoTemplate.save(auditRiskPolicy, collectionName);
//
//            RiskPolicy apiPolicyClass = apiPolicy.toJavaObject(RiskPolicy.class);
//            mongoTemplate.save(apiPolicyClass, collectionName);
//
//        }
//
//        //添加策略类型
//        List<RiskGroup> allGroups = riskPolicyService.getAllGroups();
//        int size = allGroups.stream().filter(riskGroup -> riskGroup.getGroupName().equals("审计升级策略")).collect(Collectors.toList()).size();
//        if (size == 0) {
//            RiskGroup riskGroup = new RiskGroup();
//            riskGroup.setGroupName("审计升级策略");
//            riskGroup.setDefault(false);
//            riskGroup.setDelFlag(false);
//            riskPolicyService.addRiskGroup(riskGroup);
//        }

        //清理无效审计策略
        mongoTemplate.remove(new Query().addCriteria(Criteria.where("isDefaultRisk").exists(false)), collectionName);

        //升级完审计的策略，转移api备份策略
        List<RiskPolicy> apiRiskPolicy = mongoTemplate.findAll(RiskPolicy.class, "apiRiskPolicy");
        for (RiskPolicy riskPolicy : apiRiskPolicy) {
            mongoTemplate.save(riskPolicy, collectionName);
        }

        //删除备份表
        mongoTemplate.dropCollection("apiRiskPolicy");

        log.info("update success!");
    }

//    private static Map<String, String> getStringStringMap() {
//        Map<String, String> audit2api = new HashMap<>();
//        audit2api.put("访问量", "访问次数");
//        audit2api.put("返回标签去重数据量", "返回数据标签去重数据量");
//        audit2api.put("去重访问域", "去重访问域数");
//        audit2api.put("去重IP数", "去重IP数");
//        audit2api.put("密码去重数量", "去重密码数");
//        audit2api.put("访问频次", "访问次数");
//        audit2api.put("请求头", "请求头关键字");
//        audit2api.put("上传文件大小", "上传文件大小（字节）");
//        audit2api.put("下载文件大小", "下载文件大小（字节）");
//        audit2api.put("返回状态码", "返回状态码");
//        audit2api.put("返回长度", "返回长度");
//        audit2api.put("IP网段", "IP网段");
//        audit2api.put("操作时间段", "操作时间范围");
//        audit2api.put("操作日期段", "操作日期范围");
//        audit2api.put("去重账号数", "去重账号数");
//        audit2api.put("账号密码去重数量", "去重账号密码组合数");
//        audit2api.put("部门", "部门");
//        audit2api.put("角色", "角色");
//        audit2api.put("账号", "账号");
//        audit2api.put("IP", "IP");
//        audit2api.put("地域", "地域");
//        audit2api.put("应用", "应用");
//        audit2api.put("API", "API");
//        audit2api.put("API标签", "API标签");
//        return audit2api;
//    }

//    private VariableModel getVariableModel(Expression expression) {
//        VariableModel right = new VariableModel();
//        if (expression.getRight().getConfigs() != null) {
//            right.setConfigs(expression.getRight().getConfigs());
//        }
//        if (expression.getRight().getName() != null) {
//            right.setName(expression.getRight().getName());
//        }
//        if (expression.getRight().getType() != null) {
//            right.setType(expression.getRight().getType());
//        }
//        if (expression.getRight().getDataType() != null) {
//            right.setDataType(expression.getRight().getDataType());
//        }
//        if (expression.getRight().getVarName() != null) {
//            right.setVarName(expression.getRight().getVarName());
//        }
//        if (expression.getRight().getDataType().equals(DslDataType.Number)) {
//            Integer value = (Integer) expression.getRight().getValue();
//            right.setValue(value * 95 / 100);
//        } else {
//            Object value = expression.getRight().getValue();
//            right.setValue(value);
//        }
//        return right;
//    }

//    private String getLeftValue(Expression expression, AuditRiskPolicy riskPolicy) {
//        //单事件风险的left对象中的值可能需要重新拼jsonPath
//        if ("TIMESTAMP".equals(riskPolicy.getRiskTimeConfig().getType().name())) {
//
//            // 事件风险 请求标签去重数据量 返回标签去重数据量 这两个指标需要根据 leftDesc 中配置的标签放入 left对象的value中
//            if (expression.getLeftDesc() != null &&
//                    ("$.reqLabelContentDistinctCountByLabel.[LABEL]".equals(expression.getLeftDesc().getUdf())
//                            || "$.rspLabelContentDistinctCountByLabel.[LABEL]".equals(expression.getLeftDesc().getUdf()))) {
//
//                String label = Optional.ofNullable(expression.getLeftDesc().getDesc())
//                        .map(leftOptions -> leftOptions.get(0))
//                        .map(leftOption -> leftOption.getValue().toString())
//                        .orElse("");
//
//                return expression.getLeftDesc().getUdf().replace("[LABEL]", label);
//            }
//        }
//
//        if (!"TIMESTAMP".equals(riskPolicy.getRiskTimeConfig().getType().name())
//                && expression.getLeftDesc() != null
//                && expression.getLeftDesc().getDesc() != null
//                && (expression.getLeftDesc().getUdf() != null || expression.getLeftDesc().getUdfParam() != null)) {
//
//            expression.getLeftDesc().setUdf("keyedDataAcRsp");
//            expression.getLeftDesc().setUdfParam(true);
//            for (Expression.LeftOption leftOption : expression.getLeftDesc().getDesc()) {
//                if (leftOption.getKey().equals("eventRspDataLabel")) {
//                    leftOption.setKey("rspDataLabel");
//                }
//            }
//
//            expression.getLeftDesc().getDesc().sort(new Comparator<Expression.LeftOption>() {
//                @Override
//                public int compare(Expression.LeftOption a, Expression.LeftOption b) {
//                    try {
//                        String value_1 = a.getKey();
//                        String value_2 = b.getKey();
//
//                        if (DataUtil.isEmpty(value_1)) {
//                            return 1;
//                        }
//
//                        if (DataUtil.isEmpty(value_2)) {
//                            return -1;
//                        }
//
//                        return value_1.compareTo(value_2);
//
//                    } catch (Exception e) {
//                        return 1;
//                    }
//                }
//            });
//
//            //对指标描述生成hash
//            StringBuffer s = new StringBuffer();
//            expression.getLeftDesc().getDesc().forEach(leftOption -> {
//
//                s.append("_");
//                s.append(leftOption.getKey());
//                s.append("_");
//                s.append(leftOption.getOp());
//                s.append("_");
//                s.append(leftOption.getValue().toString());
//            });
//
//            String finalHashStr = Optional.ofNullable(riskPolicy.getEntityRuleHash()).orElse("") + s.toString();
//
//            String leftVal = "$." + expression.getLeftDesc().getUdf() + "." + MD5Util.md5(finalHashStr);
//
//            return leftVal;
//        }
//
//        return "";
//    }

}