package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpAppService;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.json.Converter;
import org.bson.json.JsonWriterSettings;
import org.bson.json.StrictJsonWriter;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * @Auther: yangzixian
 * @Date: 2024/11/18 10:09
 * @Description:
 */
@Component
@Slf4j
public class RiskAppDomainJob {

    private final IHttpAppService httpAppService;

    private final MongoTemplate mongoTemplate;

    public RiskAppDomainJob(IHttpAppService httpAppService, MongoTemplate mongoTemplate) {
        this.httpAppService = httpAppService;
        this.mongoTemplate = mongoTemplate;
    }

    @LockedScheduler(cron = "0 0 */2 * * ?", executor = "riskAppDomainJob", name = "风险应用关联信息计算", description = "计算风险关联应用的部署域、自定义字段信息")
    public void execute() throws Exception {
        doJob();
    }

    public void doJob() {
        Query query = new Query();
        //获取所有风险id
        try (MongoCursor<Document> cursor =
                     mongoTemplate.getCollection("aggRiskInfo").
                             find(query.getQueryObject()).sort(query.getSortObject()).noCursorTimeout(true).batchSize(100).cursor()) {
            while (cursor.hasNext()) {
                JsonWriterSettings settings = JsonWriterSettings.builder().int64Converter(new Converter<Long>() {
                    public void convert(Long value, StrictJsonWriter writer) {
                        writer.writeNumber(value.toString());
                    }
                }).build();
                AggRiskInfo aggRiskInfo = JSONObject.parseObject(cursor.next().toJson(settings), AggRiskInfo.class);
                String uri = aggRiskInfo.getAppUri();
                if (DataUtil.isNotEmpty(uri)) {
                    HttpAppDto httpAppByUri = httpAppService.getHttpAppByUri(uri);
                    Set<String> deployDomains = aggRiskInfo.getDeployDomains();
                    if (DataUtil.isEmpty(deployDomains)) {
                        deployDomains = new HashSet<>();
                    }
                    deployDomains.addAll(httpAppByUri.getDeployDomains());
                    //自定义字段替换为最新情况
                    List<HttpAppResource.Department> departments = new ArrayList<>();
                    if (httpAppByUri.getDepartments() != null) {
                        departments = httpAppByUri.getDepartments();
                    } else {
                        departments = aggRiskInfo.getDepartments();
                    }
                    Update update = new Update();
                    update.set("deployDomains", deployDomains);
                    update.set("departments", departments);
                    mongoTemplate.updateFirst(new Query().addCriteria(Criteria.where("_id").is(aggRiskInfo.getId())), update, "aggRiskInfo");
                }
            }
        } catch (Exception e) {
            log.error("计算风险应用关联信息计算错误：" + e);
        }
    }

}
