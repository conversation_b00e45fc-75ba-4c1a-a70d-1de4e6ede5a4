package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit.mix.schdule.domain.utils.ParameterHelper;
import com.quanzhi.auditapiv2.biz.schedule.task.account.AccountInfoMonthAggService;
import com.quanzhi.auditapiv2.common.dal.entity.AccountInfo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IRiskStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class AccountInfoJob {
    private final AccountInfoMonthAggService accountInfoMonthAggService;
    private final IRiskStrategyService riskStrategyService;
    private final MongoTemplate mongoTemplate;

    public AccountInfoJob(AccountInfoMonthAggService accountInfoMonthAggService, IRiskStrategyService riskStrategyService, MongoTemplate mongoTemplate) {
        this.accountInfoMonthAggService = accountInfoMonthAggService;
        this.riskStrategyService = riskStrategyService;
        this.mongoTemplate = mongoTemplate;
    }

    @NacosValue(value = "${schedule.count.num:500000}", autoRefreshed = true)
    private long scheduleCountNum;

    /**
     * 执行定时任务
     *
     * @return
     * @throws Exception
     */

    @LockedScheduler(
            cron = "0 20 * * * ?",
            executor = "AccountInfoJob",
            name = "账号信息同步定时",
            interval = 1000 * 60 * 60 * 24,
            intervalConditionSpEL = "#root.check()",
            description = "统计和聚合账号每日活跃数据，用于账号列表和账号详情的数据展示"
    )
    public void execute() {
        doJob(ParameterHelper.get());
    }

    public boolean check() {
        return accountInfoMonthAggService.count() > scheduleCountNum;
    }

    /**
     * 处理任务
     */
    public void doJob(String date) {
        accountInfoMonthAggService.stat();
        List<AccountInfo> accountInfos = mongoTemplate.find(new Query().addCriteria(Criteria.where("strategyStatus").is(1)), AccountInfo.class, "accountInfo");
        for (AccountInfo accountInfo : accountInfos) {
            if (DataUtil.isNotEmpty(accountInfo.getRelatedIpList())) {
                riskStrategyService.updateStrategyByAccount(accountInfo.getAccount(), new ArrayList<>(accountInfo.getRelatedIpList()));
            }
        }
    }
}
