package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit.mix.schdule.domain.entity.TaskProcess;
import com.quanzhi.audit.mix.schdule.domain.repository.TaskProcessRepository;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.risk.repository.RiskSampleRepository;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RiskSampleClearJob {

//    @NacosValue(value = "${riskSample.clean.num:1000000}", autoRefreshed = true)
//    private String sampleCleanNum;

    private final RiskSampleRepository riskSampleRepository;

    @NacosValue(value = "${riskSample.clear.singleCount:100}", autoRefreshed = true)
    private Integer singleCount;

//    private final TaskProcessRepository taskProcessRepository;

//    private final String markName = "stat:RiskSampleClear";

    private final MongoTemplate mongoTemplate;

    public RiskSampleClearJob(RiskSampleRepository riskSampleRepository, MongoTemplate mongoTemplate) {
        this.riskSampleRepository = riskSampleRepository;
        this.mongoTemplate = mongoTemplate;
    }

    @LockedScheduler(cron = "0 0 */12 * * ?", executor = "riskSampleClearJob", name = "风险样例清理定时任务", description = "风险样例数据超过阈值时，清理关联不到最近一周活跃风险的样例数据")
    public void execute() throws Exception {
        doJob();
    }

    public void doJob() {
        //获取样例表count
//        Long riskSampleCount = riskSampleRepository.getCount();
        //判断是否超过数据量阈值
//        if (riskSampleCount > Integer.parseInt(sampleCleanNum)) {
        //进行标记-清理任务
        mark();
        clean();
//        }
    }

    /**
     * 标记在使用的sample
     */
    public void mark() {
        List<String> riskIds = new ArrayList<>();

        // 获取今天的日期
        LocalDate today = LocalDate.now();
        // 减去2天
        LocalDate twoDaysAgo = today.minusDays(2);
        // 创建ZonedDateTime对象，使用系统默认时区
        ZonedDateTime zonedTwoDaysAgo = twoDaysAgo.atStartOfDay(ZoneId.systemDefault());
        // 获取时间戳
        long timeStamp = zonedTwoDaysAgo.toInstant().toEpochMilli();

        Query query = new Query();
        query.addCriteria(Criteria.where("updateTime").gte(timeStamp));
        //获取所有风险id
        try (MongoCursor<Document> cursor =
                     mongoTemplate.getCollection("riskInfo").
                             find(query.getQueryObject()).sort(query.getSortObject()).noCursorTimeout(true).batchSize(singleCount).cursor()) {
            while (cursor.hasNext()) {
                Document riskInfo = cursor.next();
                riskIds.add(riskInfo.getString("_id"));
                if (riskIds.size() >= singleCount) {
                    //in操作标记使用中的sample
                    riskSampleRepository.updateRiskSampleLiveByRiskIds(riskIds);
                    riskIds.clear();
                }
            }
            if (!riskIds.isEmpty()) {
                //in操作标记使用中的sample
                riskSampleRepository.updateRiskSampleLiveByRiskIds(riskIds);
            }
        }
    }

    /**
     * 清理过时的sample
     */
    public void clean() {
        //清理一天前失活的sample
        Long count = riskSampleRepository.deleteRiskSampleUnLive();
        log.warn("风险样例清理成功，本次清理：{}条", count);
    }
}
