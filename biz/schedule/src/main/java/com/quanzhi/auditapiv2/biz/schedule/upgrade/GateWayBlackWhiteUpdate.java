package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.model.GatewayEventBlackWhiteList;
import com.quanzhi.auditapiv2.common.dal.dao.impl.GatewayEventBlackWhiteListNacosDaoImpl;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceFilterService;
import com.quanzhi.metabase.core.model.filter.AssetFilterRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2023/8/8 16:08
 * @description: 2.4的黑白名单升级到3.0
 **/
@Component
@Slf4j
public class GateWayBlackWhiteUpdate implements UpgradeService {

    @Autowired
    private GatewayEventBlackWhiteListNacosDaoImpl gatewayEventBlackWhiteListNacosDao;
    @Autowired
    private IResourceFilterService resourceFilterService;

    @Override
    public int getVersion() {
        return 20230831;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        List<GatewayEventBlackWhiteList> all = gatewayEventBlackWhiteListNacosDao.getAll();
        if (DataUtil.isEmpty(all)) {
            return;
        }

        GatewayEventBlackWhiteList gatewayEventBlackWhite = all.stream().findFirst().get();
        if(gatewayEventBlackWhite.getId().contains("CONFIG")){//说明已经升过了
            return;
        }

        List<GatewayEventBlackWhiteList> blackList = all.stream().filter(e -> e.getBlackWhiteType().equals(GatewayEventBlackWhiteList.BlackWhiteTypeEnum.BLACK)).collect(Collectors.toList());
        List<GatewayEventBlackWhiteList> withList = all.stream().filter(e -> e.getBlackWhiteType().equals(GatewayEventBlackWhiteList.BlackWhiteTypeEnum.WHITE)).collect(Collectors.toList());
        List<AssetFilterRule> assetFilterRules = new ArrayList<>();
        List<String> delIds=new ArrayList<>();
        if (DataUtil.isNotEmpty(blackList)) {
            for (GatewayEventBlackWhiteList gatewayRule : blackList) {
                AssetFilterRule assetFilterRule = new AssetFilterRule();
                assetFilterRule.setConfigType(AssetFilterRule.ConfigTypeEnum.FILTER);
                convert(gatewayRule, assetFilterRule);
                assetFilterRules.add(assetFilterRule);
                delIds.add(gatewayRule.getId());
            }
        }
        if (DataUtil.isNotEmpty(withList)) {
            for (GatewayEventBlackWhiteList gatewayRule : withList) {
                AssetFilterRule assetFilterRule = new AssetFilterRule();
                assetFilterRule.setConfigType(AssetFilterRule.ConfigTypeEnum.PASS);
                convert(gatewayRule, assetFilterRule);
                assetFilterRules.add(assetFilterRule);
                delIds.add(gatewayRule.getId());
            }
        }
        //先清空
        gatewayEventBlackWhiteListNacosDao.delete(delIds);
        for (AssetFilterRule assetFilterRule : assetFilterRules) {
            resourceFilterService.saveAssetFilterRule(assetFilterRule);
        }
        log.info("2.4网关过滤升级成功");
    }


    private void convert(GatewayEventBlackWhiteList gatewayRule, AssetFilterRule assetFilterRule) {
        GatewayEventBlackWhiteList.MatchTypeEnum matchType = gatewayRule.getMatchType();
        switch (matchType) {
            case DOMAINS:
                List<String> hosts = gatewayRule.getHosts();
                assetFilterRule.setResource(hosts.stream().collect(Collectors.joining(",")));
                assetFilterRule.setDelFlag(false);
                assetFilterRule.setOpenFlag(true);
                assetFilterRule.setType(AssetFilterRule.AssetResourceTypeEnum.APP.name());
                break;
            case API_URIS:
                List<String> apiUris = gatewayRule.getApiUris();
                assetFilterRule.setResource(apiUris.stream().collect(Collectors.joining(",")));
                assetFilterRule.setDelFlag(false);
                assetFilterRule.setOpenFlag(true);
                assetFilterRule.setType(AssetFilterRule.AssetResourceTypeEnum.API.name());
                break;
            case IPS:
                List<String> ips = gatewayRule.getIps();
                assetFilterRule.setResource(ips.stream().collect(Collectors.joining(",")));
                assetFilterRule.setDelFlag(false);
                assetFilterRule.setOpenFlag(true);
                assetFilterRule.setType(AssetFilterRule.AssetResourceTypeEnum.IP.name());
                break;
            case IP_SEG:
                List<String> ipSegs = gatewayRule.getIpSeg();
                assetFilterRule.setResource(ipSegs.stream().collect(Collectors.joining(",")));
                assetFilterRule.setDelFlag(false);
                assetFilterRule.setOpenFlag(true);
                assetFilterRule.setType(AssetFilterRule.AssetResourceTypeEnum.IP_SEG.name());
                break;
            case MASKS:
                List<String> ipMasks = gatewayRule.getIps();
                assetFilterRule.setResource(ipMasks.stream().collect(Collectors.joining(",")));
                assetFilterRule.setDelFlag(false);
                assetFilterRule.setOpenFlag(true);
                assetFilterRule.setType(AssetFilterRule.AssetResourceTypeEnum.IP_MASK.name());
                break;
        }
    }
}