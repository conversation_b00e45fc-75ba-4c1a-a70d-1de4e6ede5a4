package com.quanzhi.auditapiv2.biz.schedule.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 10:17 AM
 * @description
 */
@Data
public class TaskExecuteRecord {
    // 数据同步
    // 账号上次执行日期：yyyyMMdd
    private String accoutLastDate;

    // 账号上次执行时间：时间戳
    private Long accoutLastTime;

    // accountInfo 的数据量限制，超过的一天只执行一次数据同步
    private Integer dayMaxAccountCnt;

    // 每次查 accountInfo 的查询条件 accountList 的长度限制
    private Integer queryAccountInfoBatchSize;

    // ip上次执行日期：yyyyMMdd
    private String ipLastDate;

    // ip上次执行时间：时间戳
    private Long ipLastTime;

    // ipInfo 的数据量限制，超过的一天只执行一次数据同步
    private Integer dayMaxIpCnt;

    // 每次查 ipInfo 的查询条件 ipList 的长度限制
    private Integer queryIpInfoBatchSize;

    public static final String groupId = "common";
    public static final String dataId_account = "common.task.execute.account.record.json";
    public static final String dataId_ip = "common.task.execute.ip.record.json";
}
