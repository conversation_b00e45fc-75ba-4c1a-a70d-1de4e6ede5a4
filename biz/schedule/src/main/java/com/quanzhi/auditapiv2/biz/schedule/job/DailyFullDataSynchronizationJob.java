package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoCursor;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.common.dal.dao.node.ClusterNodeDao;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysLog;
import com.quanzhi.auditapiv2.common.dal.dto.ApiWeaknessDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpAppDto;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribePolicyRule;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.node.ClusterNode;
import com.quanzhi.auditapiv2.common.dal.enums.ProductTypeEnum;
import com.quanzhi.auditapiv2.common.util.utils.ConvertCustomNotifyObjUtil;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.subscribeOutput.SubscribeMsg2Kafka;
import com.quanzhi.auditapiv2.core.service.subscribeOutput.SubscribeMsg2Syslog;
import com.quanzhi.auditapiv2.core.model.subscription.customplugin.DataPushPluginTag;
import com.quanzhi.auditapiv2.core.risk.entity.RiskInfo;
import com.quanzhi.auditapiv2.core.risk.entity.RiskSample;
import com.quanzhi.auditapiv2.core.risk.repository.RiskSampleRepository;
import com.quanzhi.auditapiv2.core.risk.service.bridge.IRiskBridgeService;
import com.quanzhi.auditapiv2.core.service.manager.custom.AbstractCustomService;
import com.quanzhi.auditapiv2.core.service.manager.custom.CustomServiceProvider;
import com.quanzhi.auditapiv2.core.service.manager.web.*;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.ApiWeaknessServiceImpl;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.HttpApiService;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.http.Pair;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import com.quanzhi.metabase.core.model.http.weakness.Proof;
import com.quanzhi.metabase.core.model.http.weakness.Sample;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.json.Converter;
import org.bson.json.JsonWriterSettings;
import org.bson.json.StrictJsonWriter;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Auther: yangzixian
 * @Date: 2022/2/8 15:51
 * @Description:
 */
@Component
@Slf4j
public class DailyFullDataSynchronizationJob {

    private final MongoTemplate mongoTemplate;

    private final IResourceChangedEventNacosService resourceChangedEventNacosService;

    private final SubscribeMsg2Syslog subscribeMsg2Syslog;

    private final SubscribeMsg2Kafka subscribeMsg2Kafka;

    private final ApiWeaknessServiceImpl apiWeaknessService;

    private final HttpApiService httpApiService;

    private final IHttpAppService httpAppService;

    private final ISampleEventService sampleEventService;

    private final IRiskBridgeService riskBridgeService;

    private final IDataLabelService dataLabelService;

    private final IFeatureLabelService featureLabelService;

    private final IApiClassifierService apiClassifierService;

    private final IWeaknessRuleService weaknessRuleService;

    private final RiskSampleRepository riskSampleRepository;

    private final IAppFeatureLabelService appFeatureLabelServiceImpl;

    private final INetworkSegmentService networkSegmentServiceImpl;

    private final IDataPushPluginService dataPushPluginService;

    private final ClusterNodeDao clusterNodeDao;

    private final CustomServiceProvider customServiceProvider;

    @NacosValue(value = "${daily.send.limit:10}", autoRefreshed = true)
    private Integer limit;

    @NacosValue(value = "${machine.name:UNKNOWN}", autoRefreshed = true)
    private String machineName;

    @NacosValue(value = "${notify.version:api3}", autoRefreshed = true)
    private String notifyVersion;

    @NacosValue(value = "${product.type:api}", autoRefreshed = true)
    private String productType;

    public DailyFullDataSynchronizationJob(MongoTemplate mongoTemplate
            , IResourceChangedEventNacosService resourceChangedEventNacosService
            , SubscribeMsg2Syslog subscribeMsg2Syslog
            , SubscribeMsg2Kafka subscribeMsg2Kafka
            , ApiWeaknessServiceImpl apiWeaknessService
            , HttpApiService httpApiService, IHttpAppService httpAppService
            , ISampleEventService sampleEventService
            , IRiskBridgeService riskBridgeService
            , IDataLabelService dataLabelService
            , IFeatureLabelService featureLabelService
            , IApiClassifierService apiClassifierService
            , IWeaknessRuleService weaknessRuleService
            , RiskSampleRepository riskSampleRepository, IAppFeatureLabelService appFeatureLabelServiceImpl, INetworkSegmentService networkSegmentServiceImpl, IDataPushPluginService dataPushPluginService, ClusterNodeDao clusterNodeDao, CustomServiceProvider customServiceProvider) {
        this.mongoTemplate = mongoTemplate;
        this.resourceChangedEventNacosService = resourceChangedEventNacosService;
        this.subscribeMsg2Syslog = subscribeMsg2Syslog;
        this.subscribeMsg2Kafka = subscribeMsg2Kafka;
        this.apiWeaknessService = apiWeaknessService;
        this.httpApiService = httpApiService;
        this.httpAppService = httpAppService;
        this.sampleEventService = sampleEventService;
        this.riskBridgeService = riskBridgeService;
        this.dataLabelService = dataLabelService;
        this.featureLabelService = featureLabelService;
        this.apiClassifierService = apiClassifierService;
        this.weaknessRuleService = weaknessRuleService;
        this.riskSampleRepository = riskSampleRepository;
        this.appFeatureLabelServiceImpl = appFeatureLabelServiceImpl;
        this.networkSegmentServiceImpl = networkSegmentServiceImpl;
        this.dataPushPluginService = dataPushPluginService;
        this.clusterNodeDao = clusterNodeDao;
        this.customServiceProvider = customServiceProvider;
    }

    @LockedScheduler(cron = "0 5 0 * * ?", executor = "dailyFullDataSynchronizationJob", name = "每日全量数据同步", description = "根据配置文件推送当前库中存储所有数据")
    public void execute() throws Exception {
        doJob();
    }

    public void doJob() {
        log.info("Daily full data synchronization begins");
        //根据类名匹配相同名称的推送配置执行
        String name = this.getClass().getSimpleName();
        //筛选全量同步且生效的规则
        List<SubscribePolicyRule> subscribePolicyRules = resourceChangedEventNacosService.getAll();
        if (DataUtil.isNotEmpty(subscribePolicyRules) && !subscribePolicyRules.isEmpty()) {
            for (SubscribePolicyRule subscribePolicyRule : subscribePolicyRules) {
                if (SubscribePolicyRule.CycleEnum.FULL_DAILY.name().equals(subscribePolicyRule.getCycle()) && subscribePolicyRule.getEnabled() && DataUtil.isEmpty(subscribePolicyRule.getCron())) {
                    try {
                        sendDatas(subscribePolicyRule);
                    } catch (Exception e) {
                        log.error("Daily full push error:", e);
                    }
                }
            }
            log.info("Daily full data synchronization is completed, effective configuration: {} items", subscribePolicyRules.size());
        } else {
            log.info("Daily full data synchronization ends, no effective configuration is available");
        }
    }

    public void sendDatas(SubscribePolicyRule subscribePolicyRule) throws Exception {
        //数据标签
        Map<String, String> dataLabelMap = dataLabelService.getDataLabeMap();
        Map<String, String> featureLabelMap = featureLabelService.getFeatureLabelMap();
        Map<String, String> apiClassificationMap = apiClassifierService.getApiClassificationMap();
        Map<String, String> appFeatureLabelMap = appFeatureLabelServiceImpl.getFeatureLabelMap();
        Map<String, String> networkSegmentMap = networkSegmentServiceImpl.getNetworkSegmentMap();
        //节点信息
        Map<String, String> nodeMap = clusterNodeDao.findAll().stream().collect(Collectors.toMap(ClusterNode::getNid, ClusterNode::getName, (k1, k2) -> k1));
        //弱点规则
        Map<String, WeaknessRule> weaknessRuleMap = weaknessRuleService.findTreeAll();
        Query query = new Query();
        //排序
        query.with(Sort.by(new Sort.Order(Sort.Direction.ASC, "_id")));
        String collectionName = "";
        if (SubscribePolicyRule.TypeEnum.RESOURCE.name().equals(subscribePolicyRule.getType())) {
            collectionName = "httpApi";
            //去掉已忽略
            query.addCriteria(Criteria.where("delFlag").is(false));
        } else if (SubscribePolicyRule.TypeEnum.WEAKNESS.name().equals(subscribePolicyRule.getType())) {
            collectionName = "httpApiWeakness";
            //去掉已忽略
            query.addCriteria(Criteria.where("delFlag").is(false));
        } else if (SubscribePolicyRule.TypeEnum.RISK.name().equals(subscribePolicyRule.getType())) {
            collectionName = "aggRiskInfo";
            //去掉已忽略
            query.addCriteria(Criteria.where("state").in(RiskInfo.RiskStateEnum.HAS_HANDLE.getState(), RiskInfo.RiskStateEnum.NOT_HANDLE.getState()));
        } else if (SubscribePolicyRule.TypeEnum.APP.name().equals(subscribePolicyRule.getType())) {
            collectionName = "httpApp";
            //去掉已忽略
            query.addCriteria(Criteria.where("delFlag").is(false));
        } else if (SubscribePolicyRule.TypeEnum.AUDIT_LOG.name().equals(subscribePolicyRule.getType()) && productType.equals(ProductTypeEnum.xwyh.name())) {
            collectionName = "sysLog";
            //去掉已忽略
            query.addCriteria(new Criteria());
        }
        //设置游标
        try (MongoCursor<Document> cursor = mongoTemplate.getCollection(collectionName).find(query.getQueryObject()).sort(query.getSortObject()).noCursorTimeout(true).batchSize(limit).cursor()) {
            JsonWriterSettings settings = JsonWriterSettings.builder().int64Converter(new Converter<Long>() {
                public void convert(Long value, StrictJsonWriter writer) {
                    writer.writeNumber(value.toString());
                }
            }).build();
            JSONObject finalResult = new JSONObject();
            List<JSONObject> datas = new ArrayList<>();
            AbstractCustomService abstractCustomService = customServiceProvider.getAbstractCustomService(productType);
            while (cursor.hasNext()) {
                switch (collectionName) {
                    case "httpApi": {
                        HttpApiResource httpApiResource = JSONObject.parseObject(cursor.next().toJson(settings), HttpApiResource.class);
                        HttpApiDto httpApiDto = httpApiService.getHttpApiDto(httpApiResource, dataLabelMap, featureLabelMap, apiClassificationMap);
                        boolean findSample = ConvertCustomNotifyObjUtil.isFindSample(subscribePolicyRule.getCustomFields());
                        HttpApiSample httpApiSample = null;
                        if (findSample) {
                            httpApiSample = sampleEventService.getSampleByUri(httpApiDto.getUri());
                        }
                        JSONObject data = ConvertCustomNotifyObjUtil.convertCustomNotifyObj(JSONObject.parseObject(JSON.toJSONString(httpApiDto)), subscribePolicyRule.getCustomFields(), "RESOURCE", machineName, httpApiSample, null);
                        if (abstractCustomService != null) {
                            abstractCustomService.dailyFullDataSendDatasHttpApi(data, subscribePolicyRule, httpApiResource, httpApiSample);
                        }
                        datas.add(data);
                        break;
                    }
                    case "httpApiWeakness": {
                        ApiWeakness apiWeakness = JSONObject.parseObject(cursor.next().toJson(settings), ApiWeakness.class);
                        ApiWeaknessDto apiWeaknessDto = apiWeaknessService.getApiWeaknessDto(apiWeakness, dataLabelMap, apiClassificationMap, weaknessRuleMap);
                        boolean findSample = ConvertCustomNotifyObjUtil.isFindSample(subscribePolicyRule.getCustomFields());
                        HttpApiSample httpApiSample = null;
                        if (findSample) {
                            httpApiSample = sampleEventService.getSampleByUri(apiWeaknessDto.getUri());
                        }
                        JSONObject data = ConvertCustomNotifyObjUtil.convertCustomNotifyObj(JSONObject.parseObject(JSON.toJSONString(apiWeaknessDto)), subscribePolicyRule.getCustomFields(), "WEAKNESS", machineName, httpApiSample, null);
                        if (abstractCustomService != null) {
                            abstractCustomService.dailyFullDataSendDatasHttpApiWeakness(data, subscribePolicyRule, apiWeakness, apiWeaknessDto, httpApiSample);
                        }
                        datas.add(data);
                        break;
                    }
                    case "httpApp": {
                        HttpAppResource httpAppResource = JSONObject.parseObject(cursor.next().toJson(settings), HttpAppResource.class);
                        HttpAppDto httpAppDto = httpAppService.getHttpAppDto(httpAppResource, appFeatureLabelMap, dataLabelMap, networkSegmentMap);
                        boolean findSample = ConvertCustomNotifyObjUtil.isFindSample(subscribePolicyRule.getCustomFields());
                        HttpApiSample httpApiSample = null;
                        if (findSample) {
                            httpApiSample = sampleEventService.getSampleByHost(httpAppDto.getHost());
                        }
                        JSONObject data = ConvertCustomNotifyObjUtil.convertCustomNotifyObj(JSONObject.parseObject(JSON.toJSONString(httpAppDto)), subscribePolicyRule.getCustomFields(), "APP", machineName, httpApiSample, null);
                        datas.add(data);
                        break;
                    }
                    case "aggRiskInfo": {
                        AggRiskInfo aggRiskInfo = JSONObject.parseObject(cursor.next().toJson(settings), AggRiskInfo.class);
                        boolean findSample = ConvertCustomNotifyObjUtil.isFindSample(subscribePolicyRule.getCustomFields());
                        RiskSample riskSample = null;
                        if (findSample) {
                            riskSample = riskSampleRepository.getAggRiskLastSample(aggRiskInfo.getId());
                        }
                        String newDesc = aggRiskInfo.getDesc().replaceAll("<h>", "").replaceAll("</h>", "");
                        aggRiskInfo.setDesc(newDesc);
                        JSONObject data = ConvertCustomNotifyObjUtil.convertCustomNotifyObj(JSONObject.parseObject(JSON.toJSONString(aggRiskInfo)), subscribePolicyRule.getCustomFields(), "RISK", machineName, null, JSONObject.parseObject(JSON.toJSONString(riskSample)));
//                        if (abstractCustomService != null){
//                            abstractCustomService.dailyFullDataSendDatasRiskInfo(data,subscribePolicyRule,riskInfo,riskSample);
//                        }
                        datas.add(data);
                        break;
                    }
                    case "sysLog": {
                        SysLog sysLog = JSONObject.parseObject(cursor.next().toJson(settings), SysLog.class);
                        JSONObject data = convertCustomNotifyObj(JSONObject.parseObject(JSON.toJSONString(sysLog)), subscribePolicyRule.getCustomFields(), sysLog.getId(), "AUDIT_LOG");
                        datas.add(data);
                        break;
                    }
                    default:
                }
                if (datas.size() >= limit) {
                    finalResult.put("datas", datas);
                    datas = new ArrayList<>();
                    //自定义插件发送
                    DataPushPluginTag dataPushPluginTag = null;
                    if (subscribePolicyRule.getUseCustomPlugin() != null && Boolean.TRUE.equals(subscribePolicyRule.getUseCustomPlugin()) && DataUtil.isNotEmpty(subscribePolicyRule.getPluginId())) {
                        String pluginId = subscribePolicyRule.getPluginId();
                        String type = subscribePolicyRule.getType();
                        dataPushPluginTag = dataPushPluginService.process(pluginId, finalResult, type);
                    }
                    //自定义插件已处理完逻辑
                    if (dataPushPluginTag != null && dataPushPluginTag.isConvertAndPush()) {
                        continue;
                    }
                    //kafka发送
                    if (subscribePolicyRule.getOutput().size() == 1 && SubscribePolicyRule.OutputEnum.KAFKA.name().equals(subscribePolicyRule.getOutput().get(0))) {
                        if ("api3".equals(notifyVersion) || "lm".equals(notifyVersion) || "bhyh".equals(notifyVersion)) {
                            subscribeMsg2Kafka.subscribeMsgOutput(finalResult, subscribePolicyRule.getKafkaConfigId());
                        } else if ("guangda".equals(notifyVersion)) {
                            subscribeMsg2Kafka.subscribeMsgOutputAuth(finalResult.toJSONString(), subscribePolicyRule.getKafkaConfigId());
                        }
                    } else if (subscribePolicyRule.getOutput().size() == 1 && SubscribePolicyRule.OutputEnum.SYSLOG.name().equals(subscribePolicyRule.getOutput().get(0))) {
                        subscribeMsg2Syslog.subscribeMsgOutput(finalResult, subscribePolicyRule);
                    } else if (subscribePolicyRule.getOutput().size() == 2) {
                        if ("api3".equals(notifyVersion) || "lm".equals(notifyVersion) || "bhyh".equals(notifyVersion)) {
                            subscribeMsg2Kafka.subscribeMsgOutput(finalResult, subscribePolicyRule.getKafkaConfigId());
                        } else if ("guangda".equals(notifyVersion)) {
                            subscribeMsg2Kafka.subscribeMsgOutputAuth(finalResult.toJSONString(), subscribePolicyRule.getKafkaConfigId());
                        }
                        subscribeMsg2Syslog.subscribeMsgOutput(finalResult, subscribePolicyRule);
                    }
                }
            }
            //最后若不足100条，发送一次
            finalResult.put("datas", datas);
            //自定义插件发送
            DataPushPluginTag dataPushPluginTag = null;
            if (subscribePolicyRule.getUseCustomPlugin() != null && Boolean.TRUE.equals(subscribePolicyRule.getUseCustomPlugin()) && DataUtil.isNotEmpty(subscribePolicyRule.getPluginId())) {
                String pluginId = subscribePolicyRule.getPluginId();
                String type = subscribePolicyRule.getType();
                dataPushPluginTag = dataPushPluginService.process(pluginId, finalResult, type);
            }
            //自定义插件已处理完逻辑
            if (dataPushPluginTag != null && dataPushPluginTag.isConvertAndPush()) {
                return;
            }
            //kafka发送
            if (subscribePolicyRule.getOutput().size() == 1 && SubscribePolicyRule.OutputEnum.KAFKA.name().equals(subscribePolicyRule.getOutput().get(0))) {
                if ("api3".equals(notifyVersion) || "lm".equals(notifyVersion) || "bhyh".equals(notifyVersion)) {
                    subscribeMsg2Kafka.subscribeMsgOutput(finalResult, subscribePolicyRule.getKafkaConfigId());
                } else if ("guangda".equals(notifyVersion)) {
                    subscribeMsg2Kafka.subscribeMsgOutputAuth(finalResult.toJSONString(), subscribePolicyRule.getKafkaConfigId());
                }
            } else if (subscribePolicyRule.getOutput().size() == 1 && SubscribePolicyRule.OutputEnum.SYSLOG.name().equals(subscribePolicyRule.getOutput().get(0))) {
                subscribeMsg2Syslog.subscribeMsgOutput(finalResult, subscribePolicyRule);
            } else if (subscribePolicyRule.getOutput().size() == 2) {
                if ("api3".equals(notifyVersion) || "lm".equals(notifyVersion) || "bhyh".equals(notifyVersion)) {
                    subscribeMsg2Kafka.subscribeMsgOutput(finalResult, subscribePolicyRule.getKafkaConfigId());
                } else if ("guangda".equals(notifyVersion)) {
                    subscribeMsg2Kafka.subscribeMsgOutputAuth(finalResult.toJSONString(), subscribePolicyRule.getKafkaConfigId());
                }
                subscribeMsg2Syslog.subscribeMsgOutput(finalResult, subscribePolicyRule);
            }
        } catch (Exception e) {
            switch (collectionName) {
                case "httpApi": {
                    log.error("根据配置：{}，进行资产全量数据同步出错：", subscribePolicyRule.getName(), e);
                    break;
                }
                case "httpApiWeakness": {
                    log.error("根据配置：{}，进行弱点全量数据同步出错：", subscribePolicyRule.getName(), e);
                    break;
                }
                case "riskInfo": {
                    log.error("根据配置：{}，进行风险全量数据同步出错：", subscribePolicyRule.getName(), e);
                    break;
                }
                case "httpApp": {
                    log.error("根据配置：{}，进行应用全量数据同步出错：", subscribePolicyRule.getName(), e);
                    break;
                }
                default:
                    log.error("根据配置：{}，进行全量数据同步出错：", subscribePolicyRule.getName(), e);
            }
        }
    }

    /**
     * 转换自定义格式的推送数据
     */
    private JSONObject convertCustomNotifyObj(JSONObject sourceObj, Set<String> customFields, String sampleParam, String type) throws Exception {
        //缺少必要数据，直接返回
        if (DataUtil.isEmpty(sourceObj) || DataUtil.isEmpty(type)) {
            log.warn("Custom push is missing key information, source data: {}, push type: {}", DataUtil.isEmpty(sourceObj), DataUtil.isEmpty(type));
            return null;
        }
        JSONObject data = new JSONObject();
        //处理需要特殊处理的字段
        switch (type) {
            case "RESOURCE":
                if (customFields.contains("sampleReq") || customFields.contains("sampleRsp")) {
                    if (DataUtil.isEmpty(sampleParam)) {
                        log.warn("sampleParam is null!");
                    } else {
                        HttpApiSample httpApiSample = sampleEventService.getOneSampleEventByUri(sampleParam);
                        if (DataUtil.isEmpty(httpApiSample)) {
                            List<Sample> samples = sourceObj.getJSONArray("samples").toJavaList(Sample.class);
                            if (DataUtil.isNotEmpty(samples) && samples.size() > 0) {
                                httpApiSample = sampleEventService.getSampleById(samples.get(0).getSampleId());
                            } else {
                                log.warn("use uri：{},can't find sample", sampleParam);
                            }
                        }
                        if ("lm".equals(notifyVersion)) {
                            sourceObj.put("srcIp", httpApiSample.getNet().getSrcIp());
                            sourceObj.put("srcPort", httpApiSample.getNet().getSrcPort());
                            sourceObj.put("dstIp", httpApiSample.getNet().getDstIp());
                            sourceObj.put("dstPort", httpApiSample.getNet().getDstPort());
                            sourceObj.put("protocol", "TCP");
                        }
                        if (customFields.contains("sampleReq") && DataUtil.isNotEmpty(httpApiSample)) {
                            sourceObj.put("sampleReq", httpApiSample.getReq());
                        }
                        if (customFields.contains("sampleRsp") && DataUtil.isNotEmpty(httpApiSample)) {
                            sourceObj.put("sampleRsp", httpApiSample.getRsp());
                        }
                    }
                }
                if (customFields.contains("appName")) {
                    sourceObj.put("appName", sourceObj.getOrDefault("appName", sourceObj.getOrDefault("host", "--")));
                }
                if (customFields.contains("remark")) {
                    sourceObj.put("remark", sourceObj.getOrDefault("remark", "--"));
                }
                if (sourceObj.containsKey("apiStat") && sourceObj.get("apiStat") != null) {
                    JSONObject apiStat = sourceObj.getJSONObject("apiStat");
                    if (customFields.contains("accountCount")) {
                        JSONObject accountStat = apiStat.getJSONObject("accountStat");
                        if (DataUtil.isNotEmpty(accountStat)) {
                            sourceObj.put("accountCount", accountStat.getOrDefault("accountCount", 0L));
                        } else {
                            sourceObj.put("accountCount", 0L);
                        }
                    }
                    if (customFields.contains("totalVisits")) {
                        sourceObj.put("totalVisits", apiStat.getOrDefault("totalVisits", 0L));
                    }
                    if (customFields.contains("maxReqLabelValueCount")) {
                        sourceObj.put("maxReqLabelValueCount", apiStat.getOrDefault("maxReqLabelValueCount", 0L));
                    }
                    if (customFields.contains("weaknessCount")) {
                        sourceObj.put("weaknessCount", apiStat.getOrDefault("weaknessCount", 0L));
                    }
                    if (customFields.contains("riskCount")) {
                        sourceObj.put("riskCount", apiStat.getOrDefault("riskCount", 0L));
                    }
                    if (customFields.contains("weaknessNames")) {
                        sourceObj.put("weaknessNames", apiStat.getOrDefault("weaknessNames", new ArrayList<>()));
                    }
                    if (customFields.contains("riskNames")) {
                        sourceObj.put("riskNames", apiStat.getOrDefault("riskNames", new ArrayList<>()));
                    }
                }
                if (customFields.contains("notifyIp")) {
                    sourceObj.put("notifyIp", System.getenv("KUBE_HOST_IP"));
                }
                if (customFields.contains("rspDataLabels")) {
                    sourceObj.put("rspDataLabels", sourceObj.getOrDefault("rspDataLabelsValue", new ArrayList<>()));
                }
                if (customFields.contains("reqDataLabels")) {
                    sourceObj.put("reqDataLabels", sourceObj.getOrDefault("reqDataLabelsValue", new ArrayList<>()));
                }
                if (customFields.contains("featureLabels")) {
                    List<String> featureLabelsValue = sourceObj.getJSONArray("featureLabelsValue").toJavaList(String.class);
                    List<String> classificationValue = sourceObj.getJSONArray("classificationValue").toJavaList(String.class);
                    featureLabelsValue.addAll(classificationValue);
                    sourceObj.put("featureLabels", featureLabelsValue);
                }
                if (customFields.contains("state")) {
                    sourceObj.put("state", sourceObj.get("stateName"));
                }
                if (customFields.contains("apiLifeFlag")) {
                    sourceObj.put("apiLifeFlag", sourceObj.get("apiLifeFlagValue"));
                }
                break;
            case "WEAKNESS":
                if (sourceObj.containsKey("httpApiDto") && sourceObj.get("httpApiDto") != null) {
                    JSONObject httpApiDto = (JSONObject) sourceObj.get("httpApiDto");
                    if (customFields.contains("deployDomains")) {
                        sourceObj.put("deployDomains", httpApiDto.get("deployDomains"));
                    }
                    if (customFields.contains("apiUrl")) {
                        sourceObj.put("apiUrl", httpApiDto.get("apiUrl"));
                    }
                    if (customFields.contains("departments")) {
                        sourceObj.put("department", httpApiDto.get("department"));
                    }
                    if (customFields.contains("methods")) {
                        sourceObj.put("methods", httpApiDto.get("methods"));
                    }
                    if (customFields.contains("appName")) {
                        sourceObj.put("appName", httpApiDto.get("appName"));
                    }
                    if (customFields.contains("maxRspLabelValueCount")) {
                        sourceObj.put("maxRspLabelValueCount", httpApiDto.get("maxRspLabelValueCount"));
                    }
                    if (customFields.contains("visitDomains")) {
                        sourceObj.put("visitDomains", httpApiDto.get("visitDomains"));
                    }
                    if (customFields.contains("deployDomains")) {
                        sourceObj.put("deployDomains", httpApiDto.get("deployDomains"));
                    }
                    if (customFields.contains("terminals")) {
                        sourceObj.put("terminals", httpApiDto.get("terminals"));
                    }
                    if (customFields.contains("rspDataLabels")) {
                        sourceObj.put("rspDataLabels", httpApiDto.getOrDefault("rspDataLabelsValue", new ArrayList<>()));
                    }
                    if (customFields.contains("reqDataLabels")) {
                        sourceObj.put("reqDataLabels", httpApiDto.getOrDefault("reqDataLabelsValue", new ArrayList<>()));
                    }
                    if (customFields.contains("featureLabels")) {
                        List<String> featureLabelsValue = httpApiDto.getJSONArray("featureLabelsValue").toJavaList(String.class);
                        List<String> classificationValue = httpApiDto.getJSONArray("classificationValue").toJavaList(String.class);
                        featureLabelsValue.addAll(classificationValue);
                        sourceObj.put("featureLabels", featureLabelsValue);
                    }
                    if (customFields.contains("classifications")) {
                        List<String> classificationValue = httpApiDto.getJSONArray("classificationValue").toJavaList(String.class);
                        sourceObj.put("classifications", classificationValue);
                    }
                    //department 部门
                    if (customFields.contains("department")) {
                        if (httpApiDto.containsKey("departments")) {
                            List<HttpAppResource.Department> departments = httpApiDto.getJSONArray("departments").toJavaList(HttpAppResource.Department.class);
                            if (DataUtil.isNotEmpty(departments) && departments.size() > 0) {
                                HttpAppResource.Department department = departments.get(0);
                                for (Pair<String> property : department.getProperties()) {
                                    if (property.getKey().equals("部门")) {
                                        sourceObj.put("department", property.getValue());
                                    }
                                }
                            }
                        }
                    }
                }
                HttpApiSample httpApiSample2Weak = null;
                if (customFields.contains("sampleReq") || customFields.contains("sampleRsp")) {
                    if (DataUtil.isEmpty(sampleParam)) {
                        log.warn("sampleParam is null!");
                    } else {
                        httpApiSample2Weak = sampleEventService.getOneSampleEventByUri(sampleParam);
                        if (DataUtil.isEmpty(httpApiSample2Weak)) {
                            List<Sample> samples = sourceObj.getJSONArray("samples").toJavaList(Sample.class);
                            if (DataUtil.isNotEmpty(samples) && samples.size() > 0) {
                                httpApiSample2Weak = sampleEventService.getSampleById(samples.get(0).getSampleId());
                            } else {
                                log.warn("use uri：{},can't find sample", sampleParam);
                            }
                        }
                        if (customFields.contains("sampleReq") && DataUtil.isNotEmpty(httpApiSample2Weak)) {
                            sourceObj.put("sampleReq", httpApiSample2Weak.getReq());
                        }
                        if (customFields.contains("sampleRsp") && DataUtil.isNotEmpty(httpApiSample2Weak)) {
                            sourceObj.put("sampleRsp", httpApiSample2Weak.getRsp());
                        }
                    }
                }
                if ("bhyh".equals(notifyVersion) || "lm".equals(notifyVersion)) {
                    if (DataUtil.isEmpty(httpApiSample2Weak)) {
                        httpApiSample2Weak = sampleEventService.getOneSampleEventByUri(sampleParam);
                        if (DataUtil.isEmpty(httpApiSample2Weak)) {
                            List<Sample> samples = sourceObj.getJSONArray("samples").toJavaList(Sample.class);
                            if (DataUtil.isNotEmpty(samples) && samples.size() > 0) {
                                httpApiSample2Weak = sampleEventService.getSampleById(samples.get(0).getSampleId());
                            } else {
                                log.warn("use uri：{},can't find sample", sampleParam);
                            }
                        }
                    }
                    sourceObj.put("srcIp", httpApiSample2Weak.getNet().getSrcIp());
                    sourceObj.put("srcPort", httpApiSample2Weak.getNet().getSrcPort());
                    sourceObj.put("dstIp", httpApiSample2Weak.getNet().getDstIp());
                    sourceObj.put("dstPort", httpApiSample2Weak.getNet().getDstPort());
                    sourceObj.put("protocol", "TCP");
                    sourceObj.put("sourceIp", httpApiSample2Weak.getNet().getSrcIp());
                    sourceObj.put("sourcePort", httpApiSample2Weak.getNet().getSrcPort());
                }
                if ("bhyh".equals(notifyVersion)) {
                    //description 弱点特征
                    List<Sample> samples = sourceObj.getJSONArray("samples").toJavaList(Sample.class);
                    Sample sample = samples.get(0);
                    if (DataUtil.isNotEmpty(sample)) {
                        List<Proof> proofs = sample.getProof();
                        if (DataUtil.isNotEmpty(proofs)) {
                            StringBuilder sb = new StringBuilder();
                            for (Proof proof : proofs) {
                                sb.append(proof.getName()).append("：").append(proof.getValue()).append("，");
                            }
                            String proof = sb.toString();
                            sourceObj.put("description", proof.substring(0, proof.length() - 1));
                        }
                    }
                }
                if (customFields.contains("rspContentTypes")) {
                    sourceObj.put("rspContentTypes", "json");
                }
                if (customFields.contains("notifyIp")) {
                    sourceObj.put("notifyIp", System.getenv("KUBE_HOST_IP"));
                }
                if (customFields.contains("level")) {
                    sourceObj.put("level", sourceObj.get("levelName"));
                }
                if (customFields.contains("state")) {
                    sourceObj.put("state", sourceObj.get("stateName"));
                }
                if (customFields.contains("type")) {
                    sourceObj.put("type", sourceObj.get("typeName"));
                }
                break;
            case "RISK":
                if (customFields.contains("sampleReq") || customFields.contains("sampleRsp")) {
                    if (DataUtil.isEmpty(sampleParam)) {
                        log.warn("sampleParam is null!");
                    } else {
                        RiskSample riskSample = riskSampleRepository.getRiskLastSample(sampleParam);
                        if (DataUtil.isEmpty(riskSample)) {
                            log.warn("use riskID：{}，can't find sample", sampleParam);
                        } else {
                            if ("lm".equals(notifyVersion)) {
                                sourceObj.put("srcIp", riskSample.getHttpEvent().getNet().getSrcIp());
                                sourceObj.put("srcPort", riskSample.getHttpEvent().getNet().getSrcPort());
                                sourceObj.put("dstIp", riskSample.getHttpEvent().getNet().getDstIp());
                                sourceObj.put("dstPort", riskSample.getHttpEvent().getNet().getDstPort());
                                sourceObj.put("protocol", "TCP");
                            }
                            if (customFields.contains("sampleReq")) {
                                sourceObj.put("sampleReq", riskSample.getHttpEvent().getReq());
                            }
                            if (customFields.contains("sampleRsp")) {
                                sourceObj.put("sampleRsp", riskSample.getHttpEvent().getRsp());
                            }
                        }
                    }
                }
                JSONObject policySnapshot = (JSONObject) sourceObj.get("policySnapshot");
                JSONArray entities = sourceObj.getJSONArray("entities");
                JSONObject entity = (JSONObject) entities.get(0);
                if (!sourceObj.containsKey("attackCount") || DataUtil.isEmpty(sourceObj.get("attackCount"))) {
                    sourceObj.put("attackCount", 0L);
                }
                if (!sourceObj.containsKey("remark") || DataUtil.isEmpty(sourceObj.get("remark"))) {
                    sourceObj.put("remark", "--");
                }
                if (customFields.contains("name")) {
                    sourceObj.put("name", policySnapshot.get("name"));
                }
                if (customFields.contains("type")) {
                    sourceObj.put("type", policySnapshot.get("group"));
                }
                if (customFields.contains("level")) {
                    switch (sourceObj.getIntValue("level")) {
                        case 1:
                            sourceObj.put("level", "低危");
                            break;
                        case 2:
                            sourceObj.put("level", "中危");
                            break;
                        case 3:
                            sourceObj.put("level", "高危");
                            break;
                        default:
                    }
                }
                if (customFields.contains("state")) {
                    switch (sourceObj.getIntValue("state")) {
                        case 0:
                            sourceObj.put("state", "待确认");
                            break;
                        case 1:
                            sourceObj.put("state", "已忽略");
                            break;
                        case 2:
                            sourceObj.put("state", "已确认");
                            break;
                        default:
                    }
                }
                if (customFields.contains("riskStrategy")) {
                    sourceObj.put("riskStrategy", policySnapshot.get("name"));
                }
                if (customFields.contains("subjectType")) {
                    sourceObj.put("subjectType", entity.get("type"));
                }
                if (customFields.contains("riskSubject")) {
                    sourceObj.put("riskSubject", entity.get("value"));
                }
                if (customFields.contains("riskRemark")) {
                    sourceObj.put("riskRemark", sourceObj.get("riskDesc"));
                }
                if (customFields.contains("rspLabelList")) {
                    sourceObj.put("rspLabelList", sourceObj.get("rspLabelListValue"));
                }
                if (customFields.contains("notifyIp")) {
                    sourceObj.put("notifyIp", System.getenv("KUBE_HOST_IP"));
                }
                break;
            default:
        }
        // 推送时间
        sourceObj.put("notifyTime", System.currentTimeMillis());
        // 机器名称
        sourceObj.put("machineName", machineName);
        // 推送类型
        sourceObj.put("notifyType", type);
        for (String customField : customFields) {
            if (sourceObj.containsKey(customField)) {
                data.put(customField, sourceObj.get(customField));
            }
        }
        return data;
    }


}
