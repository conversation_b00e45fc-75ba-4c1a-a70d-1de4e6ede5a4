package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.risk.Expression;
import com.quanzhi.audit_core.common.risk.Policy;
import com.quanzhi.audit_core.common.risk.Rule;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicy;
import com.quanzhi.dsl.syntax.DslDataType;
import com.quanzhi.dsl.syntax.VariableModel;
import com.quanzhi.dsl.syntax.VariableType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @class BaselineRuleUpgrade
 * @created 2024/5/23 10:27
 * @desc
 **/
@Slf4j
@Component
public class BaselineRuleUpgrade implements UpgradeService {

    @Autowired
    private MongoTemplate mongoTemplate;

    public static final String collection = "riskPolicy";

    @Override
    public int getVersion() {
        return 20240830;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
//        log.info("update baseline rule");
//        List<RiskPolicy> policies = mongoTemplate.findAll(RiskPolicy.class, collection);
//        if (CollectionUtils.isEmpty(policies)) {
//            return;
//        }
//
//        for (RiskPolicy policy : policies) {
//            if (policy == null || policy.getQuotaRule() == null
//                || CollectionUtils.isEmpty(policy.getQuotaRule().getExprList())) {
//                continue;
//            }
//
//            boolean updated = false;
//            for (Expression exp : policy.getQuotaRule().getExprList()) {
//                updated = updateBaselineVariable(exp, policy.getEntity(), policy.getRiskTimeConfig().getType());
//            }
//            if (updated) {
//                // 修改采样
//                if (policy.getSampleConfig() != null && policy.getSampleConfig().getName() != null) {
//                    if ("dsl".equals(policy.getSampleConfig().getName())) {
//                        Map<String, Object> properties = policy.getSampleConfig().getProperties();
//                        Rule rule = JSON.parseObject(JSON.toJSONString(properties.get("rule")), Rule.class);
//                        rule.getExprList().forEach(exp -> updateBaselineVariable(exp, policy.getEntity(), policy.getRiskTimeConfig().getType()));
//                        properties.put("rule", rule);
//                    }
//                }
//
//                Update update = Update.update("quotaRule", policy.getQuotaRule())
//                                      .set("sampleConfig", policy.getSampleConfig());
//                Query query = Query.query(Criteria.where("_id").is(policy.getId()));
//                try {
//                    // 内置基线再加上类型字段
//                    if (policy.getId().equals("22")) {
//                        update.set("type", "APIIPDATE");
//                    } else if ("23".equals(policy.getId())) {
//                        update.set("type", "APIACCOUNTDATE");
//                    }
//                    mongoTemplate.updateFirst(query, update, RiskPolicy.class, collection);
//                } catch (Exception e) {
//                    log.error("upgrade risk policy {} error", policy.getId(), e);
//                }
//            }
//        }
//
    }

//    private static boolean updateBaselineVariable(
//            Expression exp, String entity,
//            Policy.RiskTimeTypeEnum timeType
//    ) {
//        boolean updated = false;
//        if (exp == null || exp.getLeft() == null || exp.getLeft().getName() == null || exp.getLeft().getValue() == null) {
//            return false;
//        }
//        if ("访问次数动态基线".equals(exp.getLeft().getName())
//            || "$.keyedCount.baseline".equals(String.valueOf(exp.getLeft().getValue()))) {
//            // 修改左变量
//            exp.getLeft().setValue("$.keyedCount.all");
//            exp.getLeft().setName("访问次数");
//            // 修改左变量描述
//            if (exp.getLeftDesc() == null) {
//                // 兼容前端 bug 导致的 desc 为空
//                exp.setLeftDesc(new Expression.LeftDesc());
//            }
//            exp.getLeftDesc().setUdf("keyedCount");
//            List<Expression.LeftOption> desc = new ArrayList<>();
//            Expression.LeftOption leftOption = new Expression.LeftOption();
//            leftOption.setKey("all");
//            leftOption.setValue("all");
//            leftOption.setOp("==");
//            leftOption.setType("String");
//            desc.add(leftOption);
//            exp.getLeftDesc().setDesc(desc);
//
//            // 修改右变量
//            // 兼容前端 bug
//            if (exp.getRight() == null) {
//                exp.setRight(new VariableModel());
//            }
//            VariableModel right = exp.getRight();
//            right.setType(VariableType.NAMED);
//            right.setVarName("baseline");
//            right.setValue("$.keyedCount.all");
//            right.setDataType(DslDataType.Number);
//            HashMap<String, Object> configs = new HashMap<>();
//            configs.put("classify", "baseline");
//            configs.put("name", "API基线");
//            configs.put("alias", "API基线");
//            configs.put("baseline", "API");
//            configs.put("time", timeType);
//            configs.put("type", "keyedCount");
//            configs.put("entity", entity);
//            configs.put("keyed", "all");
//
//            right.setConfigs(configs);
//            updated = true;
//        }
//        return updated;
//    }
}
