package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.common.dal.dataobject.SysUser;
import com.quanzhi.auditapiv2.common.dal.entity.Notify;
import com.quanzhi.auditapiv2.common.dal.entity.NotifyConfig;
import com.quanzhi.auditapiv2.common.util.entity.EmailConfig;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.common.util.utils.HttpResponseUtil;
import com.quanzhi.auditapiv2.core.service.SysUserService;
import com.quanzhi.auditapiv2.core.service.manager.web.IEmailConfigService;
import com.quanzhi.auditapiv2.core.service.manager.web.INotifyConfigService;
import com.quanzhi.auditapiv2.core.service.manager.web.INotifyService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class NotifyScheduleJob {
    private Logger logger = LoggerFactory.getLogger(NotifyScheduleJob.class);

    //通知设置service
    @Autowired
    private INotifyConfigService notifyConfigServiceImpl;

    //通知service
    @Autowired
    private INotifyService notifyServiceImpl;

    //邮箱配置service
    @Autowired
    private IEmailConfigService emailConfigServiceImpl;

    //用户service
    @Autowired
    private SysUserService sysUserServiceImpl;
//
//    @Autowired
//    private AutoRegister autoRegister;

    //notifyScheduleJob任务cron表达式
    @NacosValue(value = "${notifyScheduleJob.cron:0 0/10 * * * ?}", autoRefreshed = true)
    private String cron;

    //notifyScheduleJob任务运行间隔时间 单位（分）
    @NacosValue(value = "${notifyScheduleJob.minute:10}", autoRefreshed = true)
    private Integer minute;

    //上一次任务触发时间
    private Long oldDate;

    /**
     * 执行定时任务
     *
     * @return
     * @throws Exception
     */
    @LockedScheduler(cron = "0 0/10 * * * ?", executor = "notifyScheduleJob", description = "聚合统计发送弱点类和异常类通知消息")
    public void execute() throws Exception {
        logger.warn("聚合统计发送弱点类和异常类通知消息");
        doJob();
        //根据配置修改corn表达式
//        autoRegister.updateCronByHandler("notifyScheduleJob", cron);

    }

    /**
     * 处理任务
     */
    public void doJob() throws Exception {

        //获取当前时间
        Long date = DateUtil.getTime3(DateUtil.getDateTime(DateUtil.DATE_PATTERN.YYYY_MM_DD_HH_MM));
        //初始化上一次任务触发时间
        if (DataUtil.isEmpty(oldDate)) {
            oldDate = date - 60 * minute * 1000;
        }
        //开始时间
        Long startDate = oldDate;
        //结束时间
        Long endDate = date - 1;
        //更新上一次任务触发时间
        oldDate = date;

        logger.warn("统计通知消息时间段：" + DateUtil.tm2time(startDate) + " - " + DateUtil.tm2time(endDate));
        //设置查询条件
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        //获取收件人
        SysUser sysUser = sysUserServiceImpl.getByUserName("webadmin");

        //获取通知配置列表
        List<NotifyConfig> list = notifyConfigServiceImpl.getNotifyConfigList();

        try {
            //遍历筛选出弱点类和异常类配置
            for (NotifyConfig notifyConfig : list) {

                if (NotifyConfig.NotifyTypeEnum.WEAKNESS.equals(notifyConfig.getType())
                        || NotifyConfig.NotifyTypeEnum.RISK.equals(notifyConfig.getType())) {

                    for (NotifyConfig.NotifyMethodEnum notifyMethodEnum : notifyConfig.getMethods()) {

                        //发送方式包含邮件
                        if (NotifyConfig.NotifyMethodEnum.EMAIL.equals(notifyMethodEnum)) {

                            //获取邮箱配置
                            List<EmailConfig> emailConfigList = emailConfigServiceImpl.getEmailConfigList();
                            if (DataUtil.isNotEmpty(emailConfigList)) {

                                EmailConfig emailConfig = emailConfigList.get(0);
                                if (DataUtil.isNotEmpty(sysUser) && DataUtil.isNotEmpty(sysUser.getEmail())) {

                                    //设置查询条件
                                    map.put("code", notifyConfig.getCode());
                                    //获取时间段内触发弱点或者异常数量
                                    List<Notify> notifyList = notifyServiceImpl.getNotifyList(map);
                                    if (DataUtil.isNotEmpty(notifyList) && notifyList.size() > 0) {

                                        Long startTime = null;
                                        Long endTime = null;

                                        //获取关联ip
                                        List<String> titleList = notifyServiceImpl.getDistinctNotifyList(map);
                                        //通知正文
                                        StringBuilder content = new StringBuilder();
                                        for (Notify notify : notifyList) {

                                            if (DataUtil.isNotEmpty(notify.getTriggerTime())) {

                                                if (DataUtil.isEmpty(startTime) || notify.getTriggerTime() < startTime) {
                                                    startTime = notify.getTriggerTime();
                                                }
                                                if (DataUtil.isEmpty(endTime) || notify.getTriggerTime() > endTime) {
                                                    endTime = notify.getTriggerTime();
                                                }
                                            }

                                            content.append(DateUtil.tm2time(notify.getTriggerTime()) + " " + notify.getTitle()).append("\r\n");
                                        }
                                        //通知标题
                                        String title = "在时间段 " + DateUtil.tm2time(startTime) + " - " + DateUtil.tm2time(endTime) + " 期间共发生 " + notifyConfig.getName() + " " + notifyList.size() + "次 ，涉及IP数量 " + titleList.size() + " 个！";

                                        //发送邮件
                                        emailConfigServiceImpl.sendEmail(emailConfig, sysUser.getEmail(), title, content.toString());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("聚合统计发送弱点类和异常类通知消息", e);
            HttpResponseUtil.error(e.getMessage());
        }
    }
}
