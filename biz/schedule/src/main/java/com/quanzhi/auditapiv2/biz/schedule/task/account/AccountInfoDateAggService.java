//package com.quanzhi.auditapiv2.biz.schedule.task.account;
//
//import com.alibaba.fastjson.JSON;
//import com.mongodb.client.MongoCursor;
//import com.quanzhi.audit.mix.schdule.domain.entity.TaskProcess;
//import com.quanzhi.audit.mix.schdule.domain.repository.TaskProcessRepository;
//import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
//import com.quanzhi.audit_core.common.model.AssetLifeStateConfig;
//import com.quanzhi.audit_core.common.model.RiskLevel;
//import com.quanzhi.auditapiv2.biz.schedule.task.AccountRiskRepository;
//import com.quanzhi.auditapiv2.common.dal.dao.AccountInfoDao;
//import com.quanzhi.auditapiv2.common.dal.dao.account.AccountDateInfoDao;
//import com.quanzhi.auditapiv2.common.dal.entity.AccountDateInfo;
//import com.quanzhi.auditapiv2.common.dal.entity.AccountInfo;
//import com.quanzhi.auditapiv2.common.dal.entity.account.StatPoint;
//import com.quanzhi.auditapiv2.common.util.utils.DateFormat;
//import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
//import com.quanzhi.auditapiv2.common.util.utils.StringUtils;
//import com.quanzhi.auditapiv2.core.model.event.BatchRiskChangedEvent;
//import com.quanzhi.auditapiv2.core.model.event.RiskChangedEvent;
//import com.quanzhi.auditapiv2.core.risk.service.AutoFlushBatch;
//import com.quanzhi.auditapiv2.core.risk.service.defaults.ConcurrentAutoFlushBatch;
//import com.quanzhi.metabase.common.utils.DataUtil;
//import com.quanzhi.metabase.core.model.dto.RiskLevelMatchDto;
//import com.quanzhi.metabase.core.model.merge.Merger;
//import com.quanzhi.audit_core.common.risk.RiskInfo;
//import lombok.Data;
//import lombok.Setter;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.map.LRUMap;
//import org.springframework.context.event.EventListener;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//
//@Service
//@Slf4j
//public class AccountInfoDateAggService {
//
//    // 上一次记录的处理值
//    private final AccountInfoDao accountInfoDao;
//
//    private final AccountDateInfoDao accountDateInfoDao;
//
//    private final TaskProcessRepository taskProcessRepository;
//
//    private final AccountRiskRepository accountRiskRepository;
//
//    private static final String EXECUTOR_NAME = "AccountInfoDateAggService";
//
//    private final AccountMerger accountMerger = new AccountMerger();
//
//    private final AutoFlushBatch<AccountInfo> accountInfoAutoFlushBatch;
//
//    private static final int BATCH_SIZE = 512;
//
//    @DynamicValue(dataId = "common.asset.life.state.config.json", groupId = "common", typeClz = AssetLifeStateConfig.class)
//    @Setter
//    private List<AssetLifeStateConfig> assetLifeStateConfigs;
//
//    @DynamicValue(dataId = "common.risk.level.config.json", groupId = "common", typeClz = RiskLevel.class)
//    private List<RiskLevel> riskLevelList;
//
//    public AccountInfoDateAggService(AccountInfoDao accountInfoDao, TaskProcessRepository taskProcessRepository, AccountDateInfoDao accountDateInfoDao, AccountRiskRepository accountRiskRepository) {
//        this.accountInfoDao = accountInfoDao;
//        this.taskProcessRepository = taskProcessRepository;
//        this.accountDateInfoDao = accountDateInfoDao;
//        this.accountRiskRepository = accountRiskRepository;
//        accountInfoAutoFlushBatch = new ConcurrentAutoFlushBatch<>(list -> {
//            // 特殊情况下一次定时任务处理同一个账号多次，这里选择最新的入库
//            Map<String, AccountInfo> accountInfoMap = new HashMap<>();
//            for (AccountInfo accountInfo : list) {
//                accountInfoMap.put(accountInfo.getAccount(), accountInfo);
//            }
//            accountInfoDao.save(new ArrayList<>(accountInfoMap.values()));
//        }, BATCH_SIZE);
//    }
//
//    public void agg(String param) {
//        AccountAccParam accountAccParam = getAccountAccParam(param);
//        TaskProcess taskProcess = null;
//        if (accountAccParam == null) {
//            taskProcess = taskProcessRepository.findOne(EXECUTOR_NAME);
//            if (taskProcess == null) {
//                taskProcess = new TaskProcess();
//                taskProcess.setData(String.valueOf(DateUtil.getDateZeroTimestamp(new Date())));
//                taskProcess.setName(EXECUTOR_NAME);
//            }
//        }
//        long startTime = taskProcess != null ? Long.parseLong(taskProcess.getData()) : accountAccParam.getStartTimestamp();
//        long endTime = accountAccParam == null ? System.currentTimeMillis() : accountAccParam.getEndTimestamp();
//        AssetLifeStateConfig stateConfig = assetLifeStateConfigs == null ? null : assetLifeStateConfigs.stream().filter(config -> config.getAssetType() == AssetLifeStateConfig.AssetTypeEnum.ACCOUNT && Boolean.TRUE.equals(config.getReviveOpenFlag())).findFirst().orElse(null);
//        // 避免定时任务处理多个日期的数据，这里做一个缓存，刚好是 BATCH_SIZE
//        Map<String, AccountInfo> accountInfoMap = new LRUMap(BATCH_SIZE);
//        long cnt = 0;
//        long success = 0;
//        try (MongoCursor<AccountDateInfo> cursor = (MongoCursor<AccountDateInfo>) accountDateInfoDao.iteratorByUpdateTime(startTime, endTime, 500, accountAccParam == null ? null : accountAccParam.getDate(), accountAccParam == null ? null : accountAccParam.getAccount())) {
//            while (cursor.hasNext()) {
//                cnt++;
//                AccountDateInfo accountDateInfo = cursor.next();
//                AccountInfo accountInfo = accountInfoMap.get(accountDateInfo.getAccount());
//                if (accountInfo == null) {
//                    accountInfo = getAccountInfo(accountDateInfo);
//                    accountInfoMap.put(accountDateInfo.getAccount(), accountInfo);
//                }
//                String lastDate = accountInfo.getLastDate();
//                try {
//                    process(accountDateInfo, accountInfo);
//                    checkAccountReviveState(stateConfig, accountInfo, lastDate);
//                    accountInfoAutoFlushBatch.add(accountInfo);
//                    success++;
//                } catch (Exception e) {
//                    log.error("process account [{}] error", accountDateInfo.getAccount(), e);
//                }
//            }
//        } catch (Exception e) {
//            log.error("account agg error", e);
//        }
//        accountInfoAutoFlushBatch.flush();
//        // 记录上次处理的时间点
//        if (taskProcess != null) {
//            taskProcess.setData(String.valueOf(endTime));
//            taskProcessRepository.save(taskProcess);
//        }
//        log.info("account agg complete total account: [{}], success count: [{}]", cnt, success);
//    }
//
//    public long count(){
//        return accountInfoDao.getCount();
//    }
//
//    private static AccountAccParam getAccountAccParam(String param) {
//        try {
//            AccountAccParam accountAccParam = null;
//            if (!StringUtils.isNullOrEmpty(param)) {
//                accountAccParam = JSON.parseObject(param, AccountAccParam.class);
//                if (accountAccParam.isEmpty()) {
//                    return null;
//                }
//            }
//            return accountAccParam;
//        } catch (Exception e) {
//            log.error("parse error, param:[{}]", param, e);
//            return null;
//        }
//    }
//
//    @EventListener(RiskChangedEvent.class)
//    public void onEvent(RiskChangedEvent event) {
//        RiskInfo riskInfo = event.getRiskInfo();
//        bindRisk(riskInfo);
//    }
//
//    private void bindRisk(RiskInfo riskInfo) {
//        if (riskInfo == null) {
//            return;
//        }
//        try {
//            if (riskInfo.getEntities() != null) {
//                String acc = riskInfo.getEntities().stream().filter(e -> "ACCOUNT".equals(e.getType())).map(RiskInfo.Entity::getValue).findAny().orElse(null);
//                if (acc == null) {
//                    return;
//                }
//                AccountInfo accountInfo = accountInfoDao.getAccount(acc);
//                if (accountInfo != null) {
//                    bindRisk(accountInfo, null, true);
//                }
//                accountInfoDao.save(Collections.singletonList(accountInfo));
//            }
//        } catch (Exception e) {
//            log.error("bind risk error, riskId:[{}]", riskInfo.getId(), e);
//        }
//    }
//
//    @EventListener(BatchRiskChangedEvent.class)
//    public void onEvent(BatchRiskChangedEvent event) {
//        List<RiskInfo> riskInfos = event.getRiskInfos();
//        if (riskInfos != null) {
//            for (RiskInfo riskInfo : riskInfos) {
//                bindRisk(riskInfo);
//            }
//        }
//    }
//
//    private void process(AccountDateInfo accountDateInfo, AccountInfo accountInfo) {
//        accountMerger.merge(accountInfo, accountDateInfo);
//        bindRisk(accountInfo, accountDateInfo.getDate(), refreshRisk);
//    }
//
//    private void bindRisk(AccountInfo accountInfo, String date, boolean refresh) {
//        RiskLevelMatchDto.Risk risk = accountRiskRepository.getRiskInfoBy(accountInfo.getAccount(), date, refresh);
//        if (risk != null) {
//            accountInfo.setRiskInfo(risk);
//            accountInfo.setRiskLevelName(DataUtil.isEmpty(risk.getLevelName()) ? "其他" : risk.getLevelName());
//            accountInfo.setRiskLevel(DataUtil.isEmpty(risk.getRiskLevel()) ? 4 : risk.getRiskLevel());
//        }
//        if (riskLevelList != null) {
//            for (RiskLevel riskLevel : riskLevelList) {
//                if ("ACCOUNT".equals(riskLevel.getLevelType()) && riskLevel.getLevel().equals(accountInfo.getRiskLevel())) {
//                    if (riskLevel.getEnableFlag() == null || !riskLevel.getEnableFlag()) {
//                        accountInfo.setRiskLevel(4);
//                        accountInfo.setRiskLevelName("其他");
//                    }
//                }
//            }
//        }
//        if (accountInfo.getRiskLevel() == null){
//            accountInfo.setRiskLevel(4);
//            accountInfo.setRiskLevelName("其他");
//        }
//    }
//
//
//    private void checkAccountReviveState(AssetLifeStateConfig stateConfig, AccountInfo accountInfo, String lastDate) {
//        if (stateConfig == null) {
//            return;
//        }
//        // 复活判断
//        try {
//            String curDate = accountInfo.getLastDate();
//            Long eventTimestamp = null;
//            if (DataUtil.isNotEmpty(curDate)) {
//                eventTimestamp = DateFormat.date2TimeStamp(curDate, DateFormat.FORMAT_YMD);
//            }
//            //上次活跃时间
//            Long preActiveTime;
//            if (DataUtil.isNotEmpty(lastDate)) {
//                preActiveTime = DateFormat.date2TimeStamp(lastDate, DateFormat.FORMAT_YMD);
//            } else {
//                return;
//            }
//            boolean reviveAdd = this.checkReviveAdd(stateConfig, preActiveTime == null ? eventTimestamp : preActiveTime);
//            if (reviveAdd) {
//                accountInfo.setReviveTime(eventTimestamp);
//            }
//        } catch (Exception e) {
//            log.error("checkAccountReviveState error. account:[{}]", accountInfo.getAccount(), e);
//        }
//    }
//
//    public boolean checkReviveAdd(AssetLifeStateConfig stateConfig, Long preActiveTime) {
//        Integer revivePreActiveTimeDay = stateConfig.getRevivePreActiveTimeDay();
//        Date date = com.quanzhi.metabase.common.utils.DateUtil.addDate(new Date(), 5, -(revivePreActiveTimeDay));
//        long revivePreActiveDatePoint = com.quanzhi.metabase.common.utils.DateUtil.getDateLastTimestamp(date);
//        if (preActiveTime != null && preActiveTime <= revivePreActiveDatePoint) {
//            return true;
//        }
//        return false;
//    }
//
//    private AccountInfo getAccountInfo(AccountDateInfo accountDateInfo) {
//        AccountInfo accountInfo = accountInfoDao.getAccount(accountDateInfo.getAccount());
//        if (accountInfo == null) {
//            accountInfo = AccountInfo.builder().account(accountDateInfo.getAccount()).firstDate(accountDateInfo.getDate()).lastDate(accountDateInfo.getDate()).build();
//        }
//        return accountInfo;
//    }
//
//    public static final class AccountMerger extends Merger<AccountInfo> {
//        @Override
//        public void merge(AccountInfo target, Object source, String[] ignoreProperties) {
//            super.merge(target, source, ignoreProperties);
//            if (source instanceof AccountDateInfo) {
//                AccountDateInfo sourceDateInfo = (AccountDateInfo) source;
//                if (sourceDateInfo.getDate() == null) {
//                    return;
//                }
//                if (target.getLastDate() == null || target.getLastDate().compareTo(sourceDateInfo.getDate()) < 0) {
//                    target.setLastDate(sourceDateInfo.getDate());
//                }
//                // 覆盖所有的组织架构数据
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffDepart())) {
//                    target.setStaffDepart(sourceDateInfo.getStaffDepart());
//                }
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffId())) {
//                    target.setStaffId(sourceDateInfo.getStaffId());
//                }
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffName())) {
//                    target.setStaffName(sourceDateInfo.getStaffName());
//                }
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffBankCard())) {
//                    target.setStaffBankCard(sourceDateInfo.getStaffBankCard());
//                }
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffMobile())) {
//                    target.setStaffMobile(sourceDateInfo.getStaffMobile());
//                }
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffEmail())) {
//                    target.setStaffEmail(sourceDateInfo.getStaffEmail());
//                }
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffChinese())) {
//                    target.setStaffChinese(sourceDateInfo.getStaffChinese());
//                }
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffNickName())) {
//                    target.setStaffNickName(sourceDateInfo.getStaffNickName());
//                }
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffIdCard())) {
//                    target.setStaffIdCard(sourceDateInfo.getStaffIdCard());
//                }
//                if (!StringUtils.isNullOrEmpty(sourceDateInfo.getStaffRole())) {
//                    target.setStaffRole(sourceDateInfo.getStaffRole());
//                }
//            }
//        }
//    }
//
//    @Data
//    public static final class AccountAccParam {
//        private String date;
//        private String account;
//        private long startTimestamp;
//        private long endTimestamp;
//
//        public boolean isEmpty() {
//            return StringUtils.isNullOrEmpty(date) && StringUtils.isNullOrEmpty(account) && startTimestamp == 0 && endTimestamp == 0;
//        }
//    }
//}
