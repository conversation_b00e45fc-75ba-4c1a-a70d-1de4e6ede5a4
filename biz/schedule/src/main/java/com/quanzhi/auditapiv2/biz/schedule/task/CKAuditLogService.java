package com.quanzhi.auditapiv2.biz.schedule.task;

import com.quanzhi.audit_core.common.model.ApiCleanJobParam;
import com.quanzhi.auditapiv2.core.trace.dao.ClickHouseDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class CKAuditLogService {

    private final ClickHouseDao clickHouseDao;

    public void clear(ApiCleanJobParam param) {
        clear("future_http_defined_event", "audit", new Date(param.getEndTimestamp()));
        clear("future_http_stripped_event", "audit", new Date(param.getEndTimestamp()));
        clear("http_defined_event", "audit", new Date(param.getEndTimestamp()));
        clear("http_stripped_event", "audit", new Date(param.getEndTimestamp()));
    }

    private void clear(String table, String db, Date limit) {
        try {
            Set<String> partitions = queryPartition(table, db);
            if (partitions == null || partitions.isEmpty()) {
                return;
            }
            //2.按照日期删除分区
            for (String s : partitions) {
                try {
                    Date indexDate = DateUtils.parseDate(s, "yyyy-MM-dd");
                    if (indexDate.before(limit) || indexDate.equals(limit)) {
                        String sql = String.format("ALTER TABLE %s.%s DROP PARTITION '%s'", db, table, s);
                        clickHouseDao.exec(sql);
                        log.info("clear table:{},db:{},partition:{}", table, db, s);
                    }
                } catch (Exception e) {
                    log.error("remove partition error, partition:{},db:{}", s, db, e);
                }
            }
        } catch (Exception e) {
            log.error("execute error, table:{},db:{}", table, db, e);
        }
    }

    public Set<String> queryPartition(String table, String db) {
        String sql = String.format("select partition,partition_id,name,path from system.parts where table='%s' and database='%s' and active='1'", table, db);
        Set<String> set = new HashSet<>();
        try {
            List<Map<String, Object>> results = clickHouseDao.query(sql);
            for (Map<String, Object> entryMap : results) {
                String partition = entryMap.get("partition").toString();
                set.add(partition);
            }
        } catch (Exception e) {
            log.error("query partition error", e);
        }
        return set;
    }

}
