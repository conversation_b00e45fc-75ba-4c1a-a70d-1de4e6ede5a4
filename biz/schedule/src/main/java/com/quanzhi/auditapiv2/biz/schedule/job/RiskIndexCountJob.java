package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.common.dal.dto.schedule.ScheduleLogDto;
import com.quanzhi.auditapiv2.common.dal.dto.schedule.ScheduleSearchDto;
import com.quanzhi.auditapiv2.common.dal.entity.RiskIndex;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.service.impl.SysRiskIndexService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpApiService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.ApiWeaknessServiceImpl;
import com.quanzhi.auditapiv2.core.service.schedule.AuditScheduleService;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;


/**
 * @Auther: yangzixian
 * @Date: 2021/8/30 10:09
 * @Description:
 */
@Component
@Slf4j
public class RiskIndexCountJob {

    @Autowired
    private SysRiskIndexService sysRiskIndexService;

    @NacosValue(value = "${schedule.count.num:500000}", autoRefreshed = true)
    private Long scheduleCountNum;

    private final IHttpApiService httpApiService;

    private final ApiWeaknessServiceImpl apiWeaknessService;

    private final RiskInfoService riskInfoService;

    private final AuditScheduleService auditScheduleService;

    public RiskIndexCountJob(IHttpApiService httpApiService, ApiWeaknessServiceImpl apiWeaknessService, RiskInfoService riskInfoService, AuditScheduleService auditScheduleService) {
        this.httpApiService = httpApiService;
        this.apiWeaknessService = apiWeaknessService;
        this.riskInfoService = riskInfoService;
        this.auditScheduleService = auditScheduleService;
    }

    @LockedScheduler(cron = "0 0 0/1 * * ?", executor = "riskIndexCountScheduleJob", name = "系统风险指数计算", description = "计算概览页面风险指数信息")
//            ,
//            interval = 1000 * 60 * 60 * 24, intervalConditionSpEL = "#root.check()")
    public void execute() throws Exception {
        doJob();
    }

    public boolean check() {
        return httpApiService.getCount(new MetabaseQuery()) > scheduleCountNum
                || apiWeaknessService.getCount(new MetabaseQuery()) > scheduleCountNum
                || riskInfoService.totalCount(new RiskSearchDto()) > scheduleCountNum;
    }

    public void doJob() {
        log.info("Calculate the current system risk index");
        RiskIndex riskIndex = sysRiskIndexService.getRiskIndex();
        sysRiskIndexService.saveRiskIndex(riskIndex);
        log.info("The calculation is successful and the current risk index is:" + riskIndex.getSystemRiskIndex());
    }

}
