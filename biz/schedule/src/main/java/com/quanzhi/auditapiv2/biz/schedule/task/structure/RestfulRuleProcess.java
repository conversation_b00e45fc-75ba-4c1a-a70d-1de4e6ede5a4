package com.quanzhi.auditapiv2.biz.schedule.task.structure;

import com.alibaba.fastjson.JSON;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiDao;
import com.quanzhi.auditapiv2.common.dal.dao.IHttpApiSampleDao;
import com.quanzhi.auditapiv2.common.dal.dao.IResourceDefineDao;
import com.quanzhi.auditapiv2.common.dal.dao.IXxlJobDao;
import com.quanzhi.auditapiv2.common.dal.entity.UrlStructureLeafInfo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateFormat;
import com.quanzhi.metabase.common.utils.UrlUtil;
import com.quanzhi.metabase.core.model.http.CompositeRule;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.query.MetabaseQuery;
import com.quanzhi.metabase.core.model.query.Predicate;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2021/12/31 11:39 上午
 * @description: restful 未合并、部分合并情况处理
 **/
//@Component
@Slf4j
public class RestfulRuleProcess implements AppStructureProcess {


    private final IHttpApiDao httpApiDao;
    private final IHttpApiSampleDao httpApiSampleDao;
    private final IResourceDefineDao resourceDefineDao;
    private final IXxlJobDao xxlJobDao;


    public RestfulRuleProcess(IHttpApiDao httpApiDao, IHttpApiSampleDao httpApiSampleDao, IResourceDefineDao resourceDefineDao, IXxlJobDao xxlJobDao) {
        this.httpApiDao = httpApiDao;
        this.httpApiSampleDao = httpApiSampleDao;
        this.resourceDefineDao = resourceDefineDao;
        this.xxlJobDao = xxlJobDao;
    }


    @Override
    public String getType() {
        return "RestfulRuleProcess";
    }

    /**
     * 0.单个目录下节点数量超过5，且节点下已存在合并的规则
     * 1.对其他规则进行扩展
     * a.如果节点下已存在的规则为"字母+数字"规则的，取未合并的接口中的关键字，如果存在与原先不一样的关键字的，可添加关键字产生新的规则   str_numfile
     * b.如果节点下已存在的规则为有长度限制规则的，检查剩余未合并规则是否因为长度不够而未合并，如果因长度不匹配，未合并的，修改原先合并规则的长度 str_mixchar
     * c.合并规则为数字的，但是项目节点下添加了包含数字字母的规则，将规则拓展为数字字母的 str_mixchar
     * #d.已有"字母+数字"规则，但大量节点都存在字母数字外的特殊字符的，可以为规则中添加特殊字符
     * e.已存在文件后缀规则，但是还存在少量后缀名未合并的，可以往现有规则中添加后缀名   str_file
     */
    @Override
    public Object process(UrlStructureLeafInfo urlStructureLeafInfo) {
        List<String> leafs = urlStructureLeafInfo.getLeafs();
        if (leafs.size() <= 5) {
            return null;
        }
        Optional<String> restfulOp = leafs.stream().filter(e -> e.contains("$(param")).findFirst();
        if (!restfulOp.isPresent()) {
            return null;
        }
        String apiUrl = restfulOp.get();
        String apiUri = "httpapi:" + apiUrl;
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("uri", apiUri);
        HttpApiResource httpApiResource = httpApiDao.findQueryOne(queryMap);
        if (httpApiResource == null) {
            return null;
        }
        //获取合并规则
        CompositeRule compositeRule = resourceDefineDao.findByRuleId(httpApiResource.getCompositeRuleId());
        if (compositeRule == null) {
            return null;
        }
        CompositeRule.RegexParam regexParam = compositeRule.getRegexParams().get(compositeRule.getRegexParams().size() - 1);
        String regexName = regexParam.getRegexName();
        String regex = regexParam.getRegex();
        List<String> paths = urlStructureLeafInfo.getPaths() == null ? Collections.EMPTY_LIST : urlStructureLeafInfo.getPaths();
        boolean isNeedCover = false;
        if ("str_file".equals(regexName)) {
            Pattern filePattern = Pattern.compile(regex);
            List<String> extraSuffixs = new ArrayList<>();
            for (String pathName : paths) {
                boolean isMatches = filePattern.matcher(pathName).matches();
                if (pathName.contains(".") && !isMatches) {
                    extraSuffixs.add(pathName.split("\\.")[1]);
                }
            }
            if (DataUtil.isNotEmpty(extraSuffixs)) {
                //扩展规则
                String collect = extraSuffixs.stream().collect(Collectors.joining("|", "(", ")"));
                String newRegex = "(" + regex + ")|(.+\\." + collect + ")";
                regexParam.setRegex(newRegex);
                isNeedCover = true;
            }
        }
        //((article|blog|content|post|mpost|art|index|art|%s)|([a-zA-Z])?)?[0-9\-_\.]{2,}(\.[a-zA-Z]{1,6}){0,2}
        if ("str_numfile".equals(regexName)) {
            List<String> extraKeywords = new ArrayList<>();
            for (String pathName : paths) {
                Matcher matcher = strNumPrefixPattern.matcher(pathName);
                if (matcher.find()) {
                    String prefix = matcher.group(1);
                    if (DataUtil.isNotEmpty(prefix)) {
                        extraKeywords.add(prefix);
                    }
                }
            }
            if (DataUtil.isNotEmpty(extraKeywords)) {
                // 扩展规则
                String collect = String.join("|", extraKeywords);
                String substring = regex.substring(2);
                String newRegex = "((" + collect + "|" + substring;
                regexParam.setRegex(newRegex);
                isNeedCover = true;
            }
        }
        //[0-9a-zA-Z@\-_=\|,\.]{%d,}(\.[a-zA-Z0-9]{1,6}){0,1}
        //[0-9a-zA-Z@\-_=\|,\.]{8,}(\.[a-zA-Z0-9]{1,6}){0,1}
        if ("str_mixchar".equals(regexName)&&regex.startsWith("[0-9a-fA-F")) {
            String strMixcharRegex = "[0-9a-fA-F@\\-_=\\|,\\.]{%d,}(\\.[a-zA-Z0-9]{1,6}){0,1}";
            int newLength = 0;
            for (String pathName : paths) {
                if (pathName.startsWith("$(param")) {
                    continue;
                }
                String newRegex = String.format(strMixcharRegex, pathName.length());
                Pattern strmixPattern = Pattern.compile(newRegex);
                if (strmixPattern.matcher(pathName).matches()) {
                     if(newLength==0||newLength>pathName.length()){
                         newLength=pathName.length();
                     }
                }
            }
            if(newLength>=2){
                //扩展规则
                String newRegex=String.format(strMixcharRegex,newLength);
                regexParam.setRegex(newRegex);
                isNeedCover=true;
            }
        }


        if (isNeedCover) {
            //入库
            CompositeRule saveCompositeRule = resourceDefineDao.saveCompositeRule(compositeRule);
            // 触发一次定时任务
            xxlJobDao.triggerCompositeJob(JSON.toJSONString(saveCompositeRule));
            log.info("离线接口规则进行了扩展:{}", compositeRule.getRule());
        }

        return null;
    }


    @Override
    public Object process(List<String> apiUrls) {
        return null;
    }

    @Override
    public void processApp(String app, List<Object> nodeProcessResults) {
        return;
    }

    /**
     * 关键字组合
     */
    private static final List<String> KEW_WORD_LIST = Arrays.asList(new String[]{
            "get", "query", "find", "update", "login", "register", "list", "delete", "insert", "create", "token", "service",
            "download", "check", "save", "remove", "detail", "bind", "prepare", "find"
    });

    /**
     * 取样例数
     */
    private static final int MAX_SAMPLE_NUM = 5;

    private Pattern strNumPrefixPattern = Pattern.compile("(([a-zA-Z]){2,})[0-9\\-_\\.]{2,}(\\.[a-zA-Z]{1,6}){0,2}");

    /**
     * 处理应用下叶子节点restful的判断
     * <p>
     * a.如果结果内容为json的，检查结果中的某个结果id是否为这个待合并节点的value，再排查过常用的方法名关键字后，即可进行合并
     * b.检查上一个节点是否是getByXxx，如果是，最后的这个节点可能是可合并的，再排查过常用的方法名关键字后，即可进行合并
     * # c.如果结果内容为html的，检查结果中是否存在input的值为这个待合并节点的value，再排查过常用的方法名关键字后，即可进行合并
     * d.检查是否每个请求都有referer，而最后一个节点是否在referer中是参数，再排查过常用的方法名关键字后，即可进行合并
     * e.大量节点都存在字母数字外的特殊字符的，可以为规则中添加特殊字符，再排查过常用的方法名关键字后，即可进行合并
     * f.节点都为处理不了（非可过滤，非常规节点，且内容非html、json、html）的一个或少量特殊后缀名，可以添加特殊后缀名过滤
     *
     * @param urlStructureLeafInfoList
     */
    public void processLastNode(List<UrlStructureLeafInfo> urlStructureLeafInfoList) {
        if (DataUtil.isEmpty(urlStructureLeafInfoList)) {
            return;
        }
        for (UrlStructureLeafInfo urlStructureLeafInfo : urlStructureLeafInfoList) {
            List<String> paths = urlStructureLeafInfo.getPaths();
            if (paths.stream().filter(e -> e.startsWith("$(param")).count() > 1) {
                continue;
            }
            List<String> apiIds = urlStructureLeafInfo.getApiIds();
            List<HttpApiResource> apiResourceList = null;
            String url = urlStructureLeafInfo.getUrl();
            boolean isMerge;
            isMerge = checkMergeByPreviousNode(url, paths);
            if (!isMerge) {
                apiResourceList = getHttpApisByIds(apiIds);
            }
            if (!isMerge) {
                isMerge = checkMergeByJson(apiResourceList);
            }
            if (!isMerge) {
                isMerge = checkMergeByReferer(apiResourceList);
            }
            if (isMerge) {
                //生成一条合并规则
                String compositeRule = url + "/$(param)";
                String regexRule = url + "/[^/]+";
                createApiMergeRule(compositeRule, regexRule);
            }

        }


    }


    /**
     * 生成一条接口合并规则
     *
     * @param rule
     */
    private void createApiMergeRule(String rule, String regexRule) {
        CompositeRule compositeRule = new CompositeRule();
        compositeRule.setCompositeType(CompositeRule.CompositeTypeEnum.MERGE.getCompositeType());
        compositeRule.setRscType(CompositeRule.RscTypeEnum.API.getRscType());
        compositeRule.setIsRepeal(false);
        compositeRule.setExecuteTime(DateFormat.getCurTimeFormat(DateFormat.FORMAT_YMDHMS));
        compositeRule.setRule(rule);
        compositeRule.setRegexRule(regexRule);
        compositeRule.setHost(UrlUtil.getHost(rule));
        CompositeRule.RegexParam regexParam = new CompositeRule.RegexParam();
        regexParam.setKey("param");
        regexParam.setRegex("[^/]+");
        List<CompositeRule.RegexParam> regexParams = new ArrayList<>();
        regexParams.add(regexParam);
        compositeRule.setRegexParams(regexParams);
        //入库
        CompositeRule saveCompositeRule = resourceDefineDao.saveCompositeRule(compositeRule);
        // 触发一次定时任务
        xxlJobDao.triggerCompositeJob(JSON.toJSONString(saveCompositeRule));
        log.info("离线接口合并生成规则:{}", rule);

    }

    private List<HttpApiResource> getHttpApisByIds(List<String> apiIds) {
        List<HttpApiResource> httpApiResources = new ArrayList<>();
        for (String apiId : apiIds) {
            HttpApiResource httpApiResource = httpApiDao.findOne(apiId);
            httpApiResources.add(httpApiResource);
        }
        return httpApiResources;
    }

    /**
     * 如果结果内容为json的，检查结果中的某个结果id是否为这个待合并节点的value，再排查过常用的方法名关键字后，即可进行合并
     *
     * @param httpApiResourceList
     * @return
     */
    private boolean checkMergeByJson(List<HttpApiResource> httpApiResourceList) {
        int needMergeCount = 0;
        for (HttpApiResource httpApiResource : httpApiResourceList) {
            if (!httpApiResource.getRspContentTypes().contains("json")) {
                continue;
            }
            MetabaseQuery metabaseQuery = createQueryByUri(httpApiResource.getUri(), Arrays.asList("rsp.body"));
            List<HttpApiSample> sampleList = httpApiSampleDao.getList(metabaseQuery);
            if (DataUtil.isEmpty(sampleList)) {
                continue;
            }
            String lastNodeName = getUrlLastNodeName(httpApiResource.getApiUrl());
            for (HttpApiSample sample : sampleList) {
                String rspBody = sample.getRsp().getBody();
                HashMap rspBodyMap = JSON.parseObject(rspBody, HashMap.class);
                if (rspBodyMap.containsKey(lastNodeName)) {
                    if (!isBelongKewWord(lastNodeName)) {
                        needMergeCount++;
                        break;
                    }
                }
            }
        }
        if (needMergeCount >= (httpApiResourceList.size() / 2)) {
            return true;
        }
        return false;
    }

    private boolean isBelongKewWord(String lastNodeName) {
        long count = KEW_WORD_LIST.stream().filter(e -> e.contains(lastNodeName)).count();
        return count > 0 ? true : false;
    }

    private String getUrlLastNodeName(String apiUrl) {
        if (apiUrl == null) {
            return null;
        }
        String[] splitArr = apiUrl.split("/");
        return splitArr[splitArr.length - 1];
    }

    /**
     * 检查是否每个请求都有referer，而最后一个节点是否在referer中是参数，再排查过常用的方法名关键字后，即可进行合并
     *
     * @param apiResourceList
     * @return
     */
    private boolean checkMergeByReferer(List<HttpApiResource> apiResourceList) {
        int needMergeCount = 0;
        for (HttpApiResource httpApiResource : apiResourceList) {
            MetabaseQuery metabaseQuery = createQueryByUri(httpApiResource.getUri(), Arrays.asList("req.header"));
            List<HttpApiSample> sampleList = httpApiSampleDao.getList(metabaseQuery);
            if (DataUtil.isEmpty(sampleList)) {
                continue;
            }
            String lastNodeName = getUrlLastNodeName(httpApiResource.getApiUrl());
            for (HttpApiSample sample : sampleList) {
                Map<String, Object> headerMap = sample.getReq().getHeader();
                String referer = (String) headerMap.get("referer");
                if (referer == null) {
                    continue;
                }
                String[] refererArr = referer.split("\\?");
                if (refererArr.length != 2) {
                    continue;
                }
                String referParamStr = refererArr[1];
                Pattern pattern = Pattern.compile("=" + lastNodeName + "&?");
                if (pattern.matcher(referParamStr).find()) {
                    if (isBelongKewWord(lastNodeName)) {
                        needMergeCount++;
                        break;
                    }
                }
            }
        }
        if (needMergeCount >= (apiResourceList.size() / 2)) {
            return true;
        }
        return false;
    }

    private MetabaseQuery createQueryByUri(String uri, List<String> fields) {
        MetabaseQuery query = new MetabaseQuery();
        query.where("uri", Predicate.IS, uri);
        query.fields(fields);
        query.limit(MAX_SAMPLE_NUM);
        return query;
    }

    /**
     * 检查上一个节点是否是getByXxx，如果是，最后的这个节点可能是可合并的，再排查过常用的方法名关键字后，即可进行合并
     *
     * @param url
     * @param paths 最后一个节点名称集
     */
    private boolean checkMergeByPreviousNode(String url, List<String> paths) {
        String previousNode = getUrlLastNodeName(url);
        if (!previousNode.toLowerCase().contains("getby")) {
            return false;
        }
        int needMergeCount = 0;
        for (String pathName : paths) {
            if (!isBelongKewWord(pathName)) {
                needMergeCount++;
            }
        }
        if (needMergeCount >= (paths.size() / 2)) {
            return true;
        }
        return false;
    }
}
