package com.quanzhi.auditapiv2.biz.schedule.job;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.mix.plugin.domain.Plugin;
import com.quanzhi.audit.mix.plugin.service.AbstractDataPushService;
import com.quanzhi.audit.mix.plugin.service.PluginService;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.auditapiv2.biz.risk.service.RiskInfoService;
import com.quanzhi.auditapiv2.biz.risk.service.RiskPolicyService;
import com.quanzhi.auditapiv2.common.dal.dao.ISampleEventDao;
import com.quanzhi.auditapiv2.common.dal.dto.ApiWeaknessDto;
import com.quanzhi.auditapiv2.common.dal.dto.HttpApiSearchDto;
import com.quanzhi.auditapiv2.common.dal.entity.SubscribePolicyRule;
import com.quanzhi.auditapiv2.common.util.dto.ListOutputDto;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.common.util.utils.PagingUtil;
import com.quanzhi.auditapiv2.core.model.kafkaConfiguration.KafkaConfiguration;
import com.quanzhi.auditapiv2.core.model.kafkaConfiguration.ProducerConfig;
import com.quanzhi.auditapiv2.core.model.subscription.customplugin.RiskPushDataPlayLoad;
import com.quanzhi.auditapiv2.core.risk.dto.RiskInfoDto;
import com.quanzhi.auditapiv2.core.risk.dto.search.RiskSearchDto;
import com.quanzhi.auditapiv2.core.risk.entity.RiskPolicy;
import com.quanzhi.auditapiv2.core.risk.service.bridge.IRiskBridgeService;
import com.quanzhi.auditapiv2.core.service.manager.dto.SampleEventDto;
import com.quanzhi.auditapiv2.core.service.manager.web.IApiWeaknessService;
import com.quanzhi.auditapiv2.core.service.manager.web.IHttpApiService;
import com.quanzhi.auditapiv2.core.service.manager.web.IResourceChangedEventNacosService;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.customplugin.tyy.TYYCustomDataPushServiceImpl;
import com.quanzhi.metabase.core.model.http.HttpApiResource;
import com.quanzhi.metabase.core.model.http.HttpApiSample;
import com.quanzhi.metabase.core.model.http.weakness.Sample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * create at 2022/3/31 10:24 下午
 * @description:
 **/
@Component
@ConditionalOnProperty(name = "tyy.push.enable",  havingValue = "true")
@Slf4j
public class TYYDataPolicyPushJob {


    private final RiskPolicyService riskPolicyService;
    private final PluginService pluginService;
    private final IResourceChangedEventNacosService resourceChangedEventNacosService;
    private final IHttpApiService httpApiService;
    private final IApiWeaknessService apiWeaknessService;
    private final RiskInfoService riskInfoService;
    private final IRiskBridgeService riskBridgeService;
    private final ISampleEventDao sampleEventDao;


    public TYYDataPolicyPushJob(RiskPolicyService riskPolicyService, PluginService pluginService, IResourceChangedEventNacosService resourceChangedEventNacosService, IHttpApiService httpApiService, IApiWeaknessService apiWeaknessService, RiskInfoService riskInfoService, IRiskBridgeService riskBridgeService, ISampleEventDao sampleEventDao) {
        this.riskPolicyService = riskPolicyService;
        this.pluginService = pluginService;
        this.resourceChangedEventNacosService = resourceChangedEventNacosService;
        this.httpApiService = httpApiService;
        this.apiWeaknessService = apiWeaknessService;
        this.riskInfoService = riskInfoService;
        this.riskBridgeService = riskBridgeService;
        this.sampleEventDao = sampleEventDao;
    }

    @NacosValue(value = "${tyy.push.enable:false}", autoRefreshed = true)
    private boolean pushEnable;

    /**
     * 初始化的时候推送下全量数据
     */
    @NacosValue(value = "${tyy.initialize.push.enable:false}", autoRefreshed = true)
    private boolean initializePushEnable;

    @NacosValue(value = "${tyy.kafka.ip:************}", autoRefreshed = true)
    private String kafkaIp;

    @NacosValue(value = "${tyy.kafka.port:9092}", autoRefreshed = true)
    private String kafkaPort;

    @NacosValue(value = "${tyy.kafka.policy.topic:s_Data_13004}", autoRefreshed = true)
    private String kafkaPolicyTopic;

    @NacosValue(value = "${tyy.kafka.notice.topic:Data_Transmission_Notice}", autoRefreshed = true)
    private String noticeTopic;

    private Map<String, KafkaTemplate> kafkaTemplateMap = new HashMap<>();


    Snowflake snowflake = IdUtil.getSnowflake(1, 1);

    @Autowired
    private TYYCustomDataPushServiceImpl dataPushService;


    @LockedScheduler(cron = "0 0 5 * * ?", executor = "tYYDataPolicyPushJob", description = "天翼云数据安全策略推送定时任务")
    public void execute() throws Exception {
        doJob();
    }

    public void doJob() {
        log.info("pushEnable:{}", pushEnable);
        log.info("initializePushEnable:{}", initializePushEnable);
        if (pushEnable) {
            doPolicyJob();
        }
        if (initializePushEnable) {
            doHttpApiJob();
            doWeaknessJob();
            doRiskJob();
        }
    }

    /**
     * 风险数据推送
     */
    private void doRiskJob() {

        //AbstractDataPushService dataPushService = getTyyDataPushService("RISK");
        if (dataPushService == null) {
            dataPushService = (TYYCustomDataPushServiceImpl) getTyyDataPushService("RISK");
            return;
        }
        RiskSearchDto riskSearchDto = new RiskSearchDto();
        int limit = 300;
        riskSearchDto.setLimit(limit);
        riskSearchDto.setSortField("firstTime");
        riskSearchDto.setSort(2);
        try {

            Long totalCount = riskInfoService.totalCount(riskSearchDto);
            int pageCnt = PagingUtil.calculatePageSize(totalCount, limit);
            for (int page = 1; page <= pageCnt; page++) {
                riskSearchDto.setPage(page);
                ListOutputDto<RiskInfoDto> riskInfoList = riskInfoService.getRiskInfoList(riskSearchDto);
                List<RiskInfoDto> rows = riskInfoList.getRows();
                for (RiskInfoDto riskInfoDto : rows) {
                    SampleEventDto riskSample = (SampleEventDto) riskBridgeService.getRiskOneSampleByRiskId(riskInfoDto.getId());
                    RiskPushDataPlayLoad riskPushDataPlayLoad = new RiskPushDataPlayLoad();
                    riskPushDataPlayLoad.setRiskInfoDto(riskInfoDto);
                    riskPushDataPlayLoad.setRiskSampleDto(riskSample);
                    dataPushService.convertAndPush(riskPushDataPlayLoad, "RISK");
                }
            }
        } catch (Exception exception) {
            log.error("天翼云风险存量数据推送出错:", exception);
        }
        log.info("天翼云存量风险数据推送完成");
    }

    /**
     * 弱点数据推送
     */
    private void doWeaknessJob() {
        //AbstractDataPushService dataPushService = getTyyDataPushService("WEAKNESS");
        if (dataPushService == null) {
            dataPushService = (TYYCustomDataPushServiceImpl) getTyyDataPushService("WEAKNESS");
            return;
        }
        Map<String, Object> queryMap = new HashMap<>();
        List<String> stateList = Arrays.asList(new String[]{"NEW", "SUSPEND", "REPAIRING", "FIXED"});
        queryMap.put("state", stateList);
        try {
            int limit = 200;
            Long totalCount = apiWeaknessService.totalCount(queryMap, null);
            int pageCnt = PagingUtil.calculatePageSize(totalCount, limit);
            for (int page = 1; page <= pageCnt; page++) {
                ListOutputDto<ApiWeaknessDto> listOutputDto = apiWeaknessService.getApiWeaknessList(queryMap, "updateTime", 2, page, limit, false, null);
                List<ApiWeaknessDto> rows = listOutputDto.getRows();
                for (ApiWeaknessDto apiWeaknessDto : rows) {
                    List<Sample> samples = apiWeaknessDto.getSamples();
                    if (DataUtil.isNotEmpty(samples)) {
                        String sampleId = samples.get(0).getSampleId();
                        HttpApiSample apiSample = sampleEventDao.findOne(sampleId);
                        apiWeaknessDto.setHttpApiSampleDetail(apiSample);
                    }
                    dataPushService.convertAndPush(apiWeaknessDto, "WEAKNESS");
                }
            }
        } catch (Exception exception) {
            log.error("天翼云存量数据推送出错:", exception);
        }
        log.info("天翼云存量弱点数据推送完成");

    }


    /**
     * 接口数据推送
     */
    private void doHttpApiJob() {
        // AbstractDataPushService dataPushService = getTyyDataPushService("RESOURCE");
        if (dataPushService == null) {
            dataPushService = (TYYCustomDataPushServiceImpl) getTyyDataPushService("RESOURCE");
            return;
        }
        HttpApiSearchDto httpApiSearchDto = new HttpApiSearchDto();
        httpApiSearchDto.setDelFlag(false);
        try {
            int limit = 500;
            Long totalCount = httpApiService.getSearchCount(httpApiSearchDto);
            int pageCnt = PagingUtil.calculatePageSize(totalCount, limit);
            for (int page = 1; page <= pageCnt; page++) {
                ListOutputDto<HttpApiResource> listOutputDto = httpApiService.getHttpApis(page, limit, "discoverTime", 2, httpApiSearchDto);
                List<HttpApiResource> rows = listOutputDto.getRows();
                for (HttpApiResource httpApiResource : rows) {
                    dataPushService.convertAndPush(httpApiResource, "RESOURCE");
                }
            }
        } catch (Exception exception) {
            log.error("天翼云存量接口数据推送出错:", exception);
        }
        log.info("天翼云存量接口数据推送完成");
    }

    private AbstractDataPushService getTyyDataPushService(String resource) {
        String filter = "";
        if ("RESOURCE".equals(resource)) {
            filter = "天翼云接口";
        } else if ("WEAKNESS".equals(resource)) {
            filter = "天翼云弱点";
        } else if ("RISK".equals(resource)) {
            filter = "天翼云风险";
        }
        List<SubscribePolicyRule> subscribePolicyRules = resourceChangedEventNacosService.getAll();
        String finalFilter = filter;
        SubscribePolicyRule subscribePolicyRule = subscribePolicyRules.stream().filter(e -> e.getName().contains(finalFilter) && Boolean.TRUE.equals(e.getEnabled())).findFirst().get();
        String pluginId = subscribePolicyRule.getPluginId();
        AbstractDataPushService dataPushService = null;
        if (pluginId != null) {
            Plugin compilePluginById = pluginService.getCompilePluginById(pluginId);
            Object o = compilePluginById.getPlugin();
            if (o == null) {
                return null;
            }
            dataPushService = (AbstractDataPushService) o;
        }
        return dataPushService;
    }

    public void doPolicyJob() {
        if (kafkaIp == null || kafkaPort == null || kafkaPolicyTopic == null) {
            log.error("天翼云安全策略推送缺失必要配置");
            return;
        }
        KafkaTemplate kafkaTemplate = getKafkaTemplate();
        List<Map<String, Object>> riskPolicyList = getRiskPolicyData();

        if (DataUtil.isNotEmpty(riskPolicyList)) {
            //先生成一个批次id
            String batchId = UUID.randomUUID().toString();
            //数据传输开始时间
            String timeStart = DateUtil.getDateTime(DateUtil.DATE_PATTERN.YYYYMMDDHHMMSS);
            for (Map<String, Object> map : riskPolicyList) {
                fillingCommonField(map, batchId);
                String msg = JSON.toJSONString(map);
                log.info("policy:{}", msg);
                kafkaTemplate.send(kafkaPolicyTopic, msg);
            }
            //数据传输结束时间
            String timeEnd = DateUtil.getDateTime(DateUtil.DATE_PATTERN.YYYYMMDDHHMMSS);
            log.info("天翼云风险策略数据推送完成:{}条", riskPolicyList.size());

            //再传到通知记录的topic
            Map<String, Object> noticeMap = new HashMap<>();
            fillingCommonField(noticeMap, batchId);
            noticeMap.put("time_start", timeStart);
            noticeMap.put("time_end", timeEnd);
            noticeMap.put("line_count", riskPolicyList.size());
            kafkaTemplate.send(noticeTopic, JSON.toJSONString(noticeMap));
            log.info("天翼云数据传输通知topic推送完成");
        }
    }

    /**
     * 填充通用字段
     */
    private void fillingCommonField(Map<String, Object> map, String batchId) {
        map.put("sys_code", "10200");
        map.put("date", DateUtil.currentDate());
        map.put("api_unit_code", "13004");
        map.put("batchId", batchId);
    }

    /**
     * 获取风险规则转换后的数据
     */
    private List<Map<String, Object>> getRiskPolicyData() {
        List<Map<String, Object>> riskPolicyConvertList = new ArrayList<>();
        try {
            List<RiskPolicy> riskPolicyList = riskPolicyService.getRiskPolicyList();
            for (RiskPolicy riskPolicy : riskPolicyList) {
                if (Boolean.FALSE.equals(riskPolicy.getEnable())) {
                    continue;
                }
                Map<String, Object> map = new HashMap<>();
                map.put("index", snowflake.nextIdStr());
                //策略名称
                map.put("name", riskPolicy.getName());
                //策略类型
                String type = transformRiskType(riskPolicy.getGroup());
                map.put("type", type);
                //策略场景及规则说明
                map.put("rule", riskPolicy);
                //策略配置内容
                map.put("content", "");
                //责任人账号
                map.put("account", "default");
                //责任人姓名
                map.put("username", "default");
                //责任人所属部门
                map.put("dept_id", "");
                //策略上报时间
                map.put("upload_time", DateUtil.currentDateTime());
                //策略生效时间
                map.put("create_time", DateUtil.format(riskPolicy.getCreateTime(), DateUtil.DATE_PATTERN.YYYYMMDDHHMMSS));
                //策略来源
                map.put("source", "10200");
                riskPolicyConvertList.add(map);
            }
        } catch (Exception exception) {
            log.error("获取风险策略出错:{}", exception);
        }
        return riskPolicyConvertList;
    }

    private String transformRiskType(String group) {
        switch (group) {
            case "数据泄漏类":
                return "104";
            case "web攻击类":
                return "109";
            case "账号安全类":
                return "110";
            default:
                return "999";
        }
    }


    private KafkaTemplate getKafkaTemplate() {
        String kafkaIpPort = kafkaIp + ":" + kafkaPort;
        if (kafkaTemplateMap.containsKey(kafkaIpPort)) {
            return kafkaTemplateMap.get(kafkaIpPort);
        }
        ProducerConfig.producerConfigMap.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaIpPort);
        KafkaTemplate<Integer, String> kafkaTemplate = KafkaConfiguration.getKafkaTemplate();
        kafkaTemplateMap.put(kafkaIpPort, kafkaTemplate);
        return kafkaTemplate;
    }
}