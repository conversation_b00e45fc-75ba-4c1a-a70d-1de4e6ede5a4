package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.auditapiv2.core.service.impl.search.GlobalSearchService;
import org.springframework.stereotype.Component;

@Component
public class GlobalSearchUpdate implements UpgradeService {

    private final GlobalSearchService globalSearchService;

    public GlobalSearchUpdate(GlobalSearchService globalSearchService){
        this.globalSearchService = globalSearchService;
    }

    @Override
    public int getVersion() {
        return 20231225;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        globalSearchService.clear();
        globalSearchService.rebuild();
    }
}
