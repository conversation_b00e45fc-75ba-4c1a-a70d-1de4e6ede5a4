package com.quanzhi.auditapiv2.biz.schedule.upgrade;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.upgrade.UpgradeService;
import com.quanzhi.audit_core.common.model.NetworkSegment;
import com.quanzhi.audit_core.common.model.WeaknessRule;
import com.quanzhi.auditapiv2.common.dal.entity.securityPosture.BigScreenConfig;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.common.util.utils.DateUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.INetworkSegmentService;
import com.quanzhi.auditapiv2.core.service.manager.web.securityPosture.IBigScreenConfigService;
import com.quanzhi.metabase.core.model.http.HttpAppResource;
import com.quanzhi.metabase.core.model.http.weakness.ApiWeakness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2022/8/10 2:12 下午
 * @description: 升级大屏信息
 **/
@Component
@Slf4j
public class bigScreenConfigUpdate implements UpgradeService {

    private final INetworkSegmentService networkSegmentService;

    private final IBigScreenConfigService bigScreenConfigService;

    public bigScreenConfigUpdate(INetworkSegmentService networkSegmentService, IBigScreenConfigService bigScreenConfigService) {
        this.networkSegmentService = networkSegmentService;
        this.bigScreenConfigService = bigScreenConfigService;
    }

    @Override
    public int getVersion() {
        return 20240109;
    }

    @Override
    public long getVersionV2() {
        return 0;
    }

    @Override
    public void upgrade() throws Exception {
        log.info("update bigScreenConfig,{}", DateUtil.currentDateTime());

        //获取所有网段
        List<NetworkSegment> networkSegments = networkSegmentService.getAll();
        //获取大屏配置
        BigScreenConfig bigScreenConfig = bigScreenConfigService.selectBigScreenConfig();
        BigScreenConfig.network networkOne = bigScreenConfig.getNetworkOne();
        BigScreenConfig.network networkTwo = bigScreenConfig.getNetworkTwo();
        for (NetworkSegment networkSegment : networkSegments) {
            if (networkSegment.getDomain() != null && networkSegment.getDomain().getSecondLevel().equals(networkOne.getName())) {
                String secondLevel = networkOne.getName();
                networkOne.setName(networkSegment.getDomain().getFirstLevel() + "-" + secondLevel);
            }
            if (networkSegment.getDomain() != null && networkSegment.getDomain().getSecondLevel().equals(networkTwo.getName())) {
                String secondLevel = networkTwo.getName();
                networkTwo.setName(networkSegment.getDomain().getFirstLevel() + "-" + secondLevel);
            }
            if (networkOne.getName().contains("-") && networkTwo.getName().contains("-")) {
                break;
            }
        }
        bigScreenConfig.setNetworkOne(networkOne);
        bigScreenConfig.setNetworkTwo(networkTwo);
        bigScreenConfigService.saveBigScreenConfig(bigScreenConfig);

        log.info("update success，networkOne:{},networkTwo:{}", networkTwo.getName(), networkTwo.getName());
    }

}