package com.quanzhi.auditapiv2.biz.schedule.job;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit_core.common.model.DataLabel;
import com.quanzhi.auditapiv2.biz.risk.service.aggRisk.AggRiskService;
import com.quanzhi.auditapiv2.common.dal.entity.DataLabelSensiLevel;
import com.quanzhi.auditapiv2.common.dal.entity.account.DateStat;
import com.quanzhi.auditapiv2.common.dal.entity.aggRisk.AggRiskInfo;
import com.quanzhi.auditapiv2.common.dal.entity.data.DataInfo;
import com.quanzhi.auditapiv2.common.dal.entity.data.DataMonthInfo;
import com.quanzhi.auditapiv2.common.util.utils.DataUtil;
import com.quanzhi.auditapiv2.core.service.manager.web.IDataLabelService;
import com.quanzhi.auditapiv2.core.service.manager.web.data.DataService;
import com.quanzhi.metabase.core.model.ResourceEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yangzixian
 * @date: 2022/8/9 10:22
 * @description:
 */
@Component
@Slf4j
public class DataInfoJob {

    private final DataService dataService;

    private final MongoTemplate mongoTemplate;

    private final IDataLabelService dataLabelService;

    private final AggRiskService aggRiskService;

    private final String ipInfo = "ipInfo";
    private final String accountInfo = "accountInfo";
    private final String httpApp = "httpApp";

    public DataInfoJob(DataService dataService, MongoTemplate mongoTemplate, IDataLabelService dataLabelService, AggRiskService aggRiskService) {
        this.dataService = dataService;
        this.mongoTemplate = mongoTemplate;
        this.dataLabelService = dataLabelService;
        this.aggRiskService = aggRiskService;
    }

    @LockedScheduler(cron = "0 */20 * * * ?", executor = "dataInfoJob", name = "数据定时任务", description = "计算数据信息", version = 2)
    public void execute() throws Exception {
        doJob();
    }

    public void doJob() throws Exception {

        List<DataMonthInfo> dataMonthInfos = dataService.getAllDataMonthInfo();
        if (dataMonthInfos.isEmpty()) {
            log.error("can not find dataMonthInfo");
            return;
        }

        Map<String, List<DataMonthInfo>> groupedByDataId = dataMonthInfos.stream()
                .collect(Collectors.groupingBy(DataMonthInfo::getDataId));
        for (String dataId : groupedByDataId.keySet()) {
            DataInfo dataInfo = null;
            //dataName、dataType、typeName
            DataLabel dataLabel = dataLabelService.getDataById(dataId);
            String dataName = dataLabel.getName();
            String firstClass = dataLabel.getFirstClass();
            String typeName = "";
            if (firstClass.contains(" ")) {
                typeName = firstClass.split(" ")[1];
            } else if (firstClass.equals("unclassified")) {
                typeName = "未分类";
            } else {
                typeName = firstClass;
            }

            //dataLevel、levelName
            Integer dataLevel = null;
            String dataLevelName = "未分级";
            List<DataLabelSensiLevel> sensiLevel = dataLabelService.getSensiLevel();
            for (DataLabelSensiLevel dataLabelSensiLevel : sensiLevel) {
                if (dataLabelSensiLevel.getLevel().equals(dataLabel.getLevel())) {
                    dataLevel = dataLabelSensiLevel.getLevel();
                    dataLevelName = dataLabelSensiLevel.getName();
                    break;
                }
            }

            //关联主体为数据的异常
            List<AggRiskInfo> aggRiskInfos = aggRiskService.getByDataId(dataId);
            int riskNum = aggRiskInfos.size();
            Set<String> relatedAggRisks = aggRiskInfos.stream()
                    .map(AggRiskInfo::getName)
                    .collect(Collectors.toSet());

            List<DataMonthInfo> dataMonthInfoList = groupedByDataId.get(dataId);

            //总数
            Long totalReqAmount = 0L;
            Long totalRspAmount = 0L;
            Long totalAmount = 0L;

            List<ResourceEntity.Node> nodes = new ArrayList<>();
            List<String> nids = new ArrayList<>();

            for (DataMonthInfo dataMonthInfo : dataMonthInfoList) {
                for (ResourceEntity.Node node : dataMonthInfo.getNodes()) {
                    String nid = node.getNid();
                    if (!nids.contains(nid)) {
                        nids.add(nid);
                        nodes.add(node);
                    }
                }
                if (dataInfo == null) {
                    dataInfo = DataInfo.DataInfoMapper.INSTANCE.convert(dataMonthInfo);
                    dataInfo.setLastTime(dataMonthInfo.getUpdateTime());
                } else {
                    if (dataInfo.getLastTime() < dataMonthInfo.getUpdateTime()) {
                        dataInfo.setLastTime(dataMonthInfo.getUpdateTime());
                    }
                }

                ObjectMapper mapper = new ObjectMapper();

                //多节点特殊处理
                if (DataUtil.isNotEmpty(dataMonthInfo.getNodeInfoMap())) {
                    Map<String, Map<String, Object>> nodeInfoMap = dataMonthInfo.getNodeInfoMap();
                    for (String nid : nodeInfoMap.keySet()) {
                        Map<String, Object> nodeInfo = nodeInfoMap.get(nid);
                        if (nodeInfo.containsKey("reqDataDateStats")) {
                            Object raw = nodeInfo.get("reqDataDateStats");
                            Map<String, LinkedHashMap<String, Object>> rawMap = (Map<String, LinkedHashMap<String, Object>>) raw;
                            Map<String, DateStat> dataReqDateStats = rawMap.entrySet().stream()
                                    .collect(Collectors.toMap(
                                            Map.Entry::getKey,
                                            e -> mapper.convertValue(e.getValue(), DateStat.class)
                                    ));
                            long req_count = calculateTotalAmount(dataReqDateStats);
                            totalReqAmount = totalReqAmount + req_count;
                        }
                        if (nodeInfo.containsKey("rspDataDateStats")) {
                            Object raw = nodeInfo.get("rspDataDateStats");
                            Map<String, LinkedHashMap<String, Object>> rawMap = (Map<String, LinkedHashMap<String, Object>>) raw;
                            Map<String, DateStat> dataRspDateStats = rawMap.entrySet().stream()
                                    .collect(Collectors.toMap(
                                            Map.Entry::getKey,
                                            e -> mapper.convertValue(e.getValue(), DateStat.class)
                                    ));
                            long rsp_count = calculateTotalAmount(dataRspDateStats);
                            totalRspAmount = totalRspAmount + rsp_count;
                        }
                    }
                } else {
                    totalReqAmount += calculateTotalAmount(dataMonthInfo.getReqDataDateStats());
                    totalRspAmount += calculateTotalAmount(dataMonthInfo.getRspDataDateStats());
                }
            }
            totalAmount += totalReqAmount + totalRspAmount;

            //关联ip、api、app、account数量
            long distinctIpCount = getDistinctIpCount(dataId);
            long distinctAccountCount = getDistinctAccountCount(dataId);
            long distinctAppCount = getDistinctAppCount(dataId);

            if (dataInfo != null) {
                //风险等级
                dataService.bindRisk(dataInfo);
                dataInfo.setDataName(dataName);
                dataInfo.setDataType(firstClass);
                dataInfo.setDataLevel(dataLevel);
                dataInfo.setDataLevelName(dataLevelName);
                dataInfo.setDataTypeName(typeName);
                dataInfo.setTotalCount(totalAmount);
                dataInfo.setReqCount(totalReqAmount);
                dataInfo.setRspCount(totalRspAmount);
                dataInfo.setRelatedRisks(relatedAggRisks);
                dataInfo.setRiskNum((long) riskNum);
                dataInfo.setAppNum(distinctAppCount);
                dataInfo.setIpNum(distinctIpCount);
                dataInfo.setAccountNum(distinctAccountCount);
                dataInfo.setNodes(nodes);

                dataService.upsertDataInfo(dataInfo);
            }

        }

    }

    public static long calculateTotalAmount(Map<String, DateStat> dataStats) {
        return dataStats.values().stream()
                .mapToLong(DateStat::getAmount)
                .sum();
    }

    public long getDistinctAccountCount(String dataId) {
        // 构造查询条件
        Criteria criteria = new Criteria().orOperator(
                Criteria.where("reqDataLabelList").is(dataId),
                Criteria.where("rspDataLabelList").is(dataId)
        );

        // 查询并去重 account
        List<String> accounts = mongoTemplate.findDistinct(new Query().addCriteria(criteria), "account", accountInfo, String.class);

        // 返回去重后的总数
        return accounts.size();
    }

    public long getDistinctIpCount(String dataId) {
        // 构造查询条件
        Criteria criteria = new Criteria().orOperator(
                Criteria.where("reqDataLabelList").is(dataId),
                Criteria.where("rspDataLabelList").is(dataId)
        );

        // 查询并去重 ip
        List<String> ips = mongoTemplate.findDistinct(new Query().addCriteria(criteria), "ip", ipInfo, String.class);

        // 返回去重后的总数
        return ips.size();
    }

    public long getDistinctAppCount(String dataId) {
        // 构造查询条件
        Criteria criteria = new Criteria().orOperator(
                Criteria.where("reqDataLabels").is(dataId),
                Criteria.where("rspDataLabels").is(dataId)
        );

        // 查询并去重 appUri
        List<String> apps = mongoTemplate.findDistinct(new Query().addCriteria(criteria), "uri", httpApp, String.class);

        // 返回去重后的总数
        return apps.size();
    }

}
