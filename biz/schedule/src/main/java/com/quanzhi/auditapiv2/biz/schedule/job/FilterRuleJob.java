package com.quanzhi.auditapiv2.biz.schedule.job;

import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.mix.schdule.application.annotation.LockedScheduler;
import com.quanzhi.audit.mix.schdule.domain.entity.TriggerStatus;
import com.quanzhi.audit.mix.schdule.domain.utils.ParameterHelper;
import com.quanzhi.auditapiv2.common.dal.dto.filter.EnableFilterDto;
import com.quanzhi.auditapiv2.core.service.manager.web.impl.resource.ResourceFilterCleanService;
import com.quanzhi.auditapiv2.core.trace.util.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create at 2023/3/29 11:12 上午
 * @description:
 **/
@Component
@Slf4j
public class FilterRuleJob {

    @Autowired
    private ResourceFilterCleanService filterCleanService;


    @LockedScheduler(cron = "0 0 0 * * ?", executor = "filterRuleJob", description = "根据过滤规则清理数据", triggerStatus = TriggerStatus.CLOSE)
    public void execute() throws Exception {
        String params = ParameterHelper.get();
        log.info("filterRuleJob params:{}", params);
        if (DataUtil.isEmpty(params)) {
            log.error("filterRuleJob params is empty");
            return;
        }
        EnableFilterDto enableFilterDto = JSON.parseObject(params, EnableFilterDto.class);
        filterCleanService.executeClean(enableFilterDto);
    }
}