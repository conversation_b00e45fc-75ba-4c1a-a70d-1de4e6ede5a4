package com.quanzhi.auditapiv2.common.util.utils;


import org.apache.shiro.codec.Hex;

import java.security.MessageDigest;
import java.util.Random;

/**
 * Created by she<PERSON><PERSON> on 2017/11/9.
 */
public class PasswordUtil {
    /**
     * 生成含有随机盐的密码
     */
    public static String generate(String password) {
        Random r = new Random();
        StringBuilder sb = new StringBuilder(16);
        sb.append(r.nextInt(99999999)).append(r.nextInt(99999999));
        int len = sb.length();
        if (len < 16) {
            for (int i = 0; i < 16 - len; i++) {
                sb.append("0");
            }
        }
        String salt = sb.toString();
        password = md5Hex(password + salt);
        char[] cs = new char[48];
        for (int i = 0; i < 48; i += 3) {
            cs[i] = password.charAt(i / 3 * 2);
            char c = salt.charAt(i / 3);
            cs[i + 1] = c;
            cs[i + 2] = password.charAt(i / 3 * 2 + 1);
        }
        return new String(cs);
    }

    /**
     * 校验密码是否正确
     */
    public static boolean verify(String password, String md5) {
        char[] cs1 = new char[32];
        char[] cs2 = new char[16];
        for (int i = 0; i < 48; i += 3) {
            cs1[i / 3 * 2] = md5.charAt(i);
            cs1[i / 3 * 2 + 1] = md5.charAt(i + 2);
            cs2[i / 3] = md5.charAt(i + 1);
        }
        String salt = new String(cs2);
        return md5Hex(password + salt).equals(new String(cs1));
    }

    /**
     * 获取十六进制字符串形式的MD5摘要
     */
    public static String md5Hex(String src) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] bs = md5.digest(src.getBytes());
            return new String(new Hex().encode(bs));
        } catch (Exception e) {
            return null;
        }
    }

    public static void main(String[] args) {
       // String password = generate(md5Hex("cjjc123!"));
       // System.out.println(password);
       // System.out.println(verify(md5Hex("cjjc123!"),password));

        System.out.println("Viewadmin:"+generate("e9635d9fa0643cf5d3cdd7b7c4a8bcde"));
        System.out.println("Opeadmin:"+generate("1598947d610c156b9356ba81e0105b56"));
        System.out.println("Riskadmin:"+generate("f79e4110e264e5210ab54a74a8f4d418"));
        System.out.println("Weakadmin:"+generate("28fcc9feb2a4002ab2ccd322579fb4b0"));
    }
}
