package com.quanzhi.auditapiv2.common.util.utils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;

import javax.net.ssl.*;
import java.io.IOException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class OkHttpClientUtil {

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(60L, TimeUnit.SECONDS)
            .readTimeout(60L, TimeUnit.SECONDS)
            .writeTimeout(60L, TimeUnit.SECONDS).build();

    private static OkHttpClient httpsClient = (getTrustAllClientBuilder())
            .connectTimeout(30L, TimeUnit.SECONDS)
            .readTimeout(30L, TimeUnit.SECONDS)
            .build();

    public static OkHttpClient client() {
        return client;
    }

    public static final OkHttpClient.Builder getTrustAllClientBuilder() {
        MyTrustManager myTrustManager = new MyTrustManager();
        OkHttpClient.Builder mBuilder = new OkHttpClient.Builder();
        mBuilder.sslSocketFactory(createSSLSocketFactory(myTrustManager), myTrustManager)
                .hostnameVerifier(new TrustAllHostnameVerifier());
        return mBuilder;
    }
    //实现HostnameVerifier接口
    public static final class TrustAllHostnameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }
    private static final SSLSocketFactory createSSLSocketFactory(MyTrustManager myTrustManager) {
        SSLSocketFactory ssfFactory = null;
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, new TrustManager[]{myTrustManager}, new SecureRandom());
            ssfFactory = sc.getSocketFactory();
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }

        return ssfFactory;
    }

    //实现X509TrustManager接口
    public static final class MyTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
    }


    public static Response post(String url, String json) throws IOException {
        return post(url, json, null);
    }

    public static Response post(String url, String json, Map<String, String> reqHeaders) throws IOException {
        RequestBody body = RequestBody.create(JSON, json);
        Request.Builder builder = new Request.Builder()
                .url(url)
                .post(body);

        if (MapUtils.isNotEmpty(reqHeaders)) {
            Headers headers = Headers.of(reqHeaders);
            builder.headers(headers);
        }

        Request request = builder.build();
        return client.newCall(request).execute();
    }


    public static Response postHttps(String url, String json, Map<String, String> reqHeaders) throws IOException {
        RequestBody body = RequestBody.create(JSON, json);
        Request.Builder builder = new Request.Builder()
                .url(url)
                .post(body);

        if (MapUtils.isNotEmpty(reqHeaders)) {
            Headers headers = Headers.of(reqHeaders);
            builder.headers(headers);
        }

        Request request = builder.build();
        return httpsClient.newCall(request).execute();
    }


    /**
     * 执行httpClient post请求命令
     * @param httpRequestUrl
     * @param jsonObject
     */
    public static Response executePost (String httpRequestUrl,Map<String,String> jsonObject) {

        FormBody.Builder FormBody  = new FormBody.Builder();

        if(DataUtil.isNotEmpty( jsonObject )) {
            for( Map.Entry<String,String> param : jsonObject.entrySet()) {
                FormBody.add(param.getKey(),param.getValue());
            }
        }

        Request request = new Request.Builder()
                .post(FormBody.build())
                .url(httpRequestUrl)
                .build();

        try {
            Response resp = client.newCall(request).execute();
            return resp;

        } catch (IOException e) {
            throw new ServiceException("request error:",e);
        }

    }

    public static ResponseBody executePost(String httpRequestUrl,Map<String,String> jsonObject,Headers headers) {
        FormBody.Builder FormBody  = new FormBody.Builder();
        if(DataUtil.isNotEmpty( jsonObject )) {
            for( Map.Entry<String,String> param : jsonObject.entrySet()) {
                FormBody.add(param.getKey(),param.getValue());
            }
        }
        Request request = new Request.Builder()
                .headers(headers)
                .post(FormBody.build())
                .url(httpRequestUrl)
                .build();
        try {
            Response resp = client.newCall(request).execute();
            return resp.body();
        } catch (Exception e) {
            log.error("req url:{} error:",httpRequestUrl,e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 执行httpClient get请求命令
     * @param httpRequestUrl
     * @param jsonObject
     */
    public static Response executeGet(String httpRequestUrl, Map<String, String> jsonObject) {
        return executeGet(httpRequestUrl, jsonObject, null);
    }

    /**
     * 执行httpClient get请求命令
     * @param httpRequestUrl
     * @param jsonObject
     */
    public static Response executeGet (String httpRequestUrl, Map<String,String> jsonObject, Map<String, String> reqHeaders) {

        HttpUrl.Builder httpBuilder = HttpUrl.parse(httpRequestUrl).newBuilder();


        if (DataUtil.isNotEmpty(jsonObject)) {
            for (Map.Entry<String, String> param : jsonObject.entrySet()) {

                httpBuilder.addQueryParameter(param.getKey(), param.getValue());
            }
        }

        Request.Builder builder = new Request.Builder().url(httpBuilder.build());
        if (MapUtils.isNotEmpty(reqHeaders)) {
            Headers headers = Headers.of(reqHeaders);
            builder.headers(headers);
        }

        Request request = builder.build();
        try {
            Response resp = client.newCall(request).execute();
            return resp;
        } catch (IOException e) {
            throw new ServiceException("请求失败：", e);
        }

    }

    /**
     * 执行httpClient 异步post请求命令
     * @param httpRequestUrl
     * @param jsonObject
     */
    public static void asyncExecutePost (String httpRequestUrl,Map<String,String> jsonObject,Callback callback) {

        FormBody.Builder FormBody  = new FormBody.Builder();

        if(DataUtil.isNotEmpty( jsonObject )) {
            for( Map.Entry<String,String> param : jsonObject.entrySet()) {
                FormBody.add(param.getKey(),param.getValue());
            }
        }

        if(callback == null) {
            callback = new Callback() {

                @Override
                public void onFailure(Call call, IOException e) {

                    log.error("调用失败 url is {}",httpRequestUrl,e);
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {


                }
            };
        }


        Request request = new Request.Builder()
                .post(FormBody.build())
                .url(httpRequestUrl)
                .build();

        client.newCall(request).enqueue(callback);
    }

    public static void asyncExecutePostBody (String httpRequestUrl,String json,Callback callback) {

        RequestBody body = RequestBody.create(JSON, json);

        if(callback == null) {
            callback = new Callback() {

                @Override
                public void onFailure(Call call, IOException e) {

                    log.error("调用失败 url is {}",httpRequestUrl,e);
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {


                }
            };
        }


        Request request = new Request.Builder()
                .post( body )
                .url(httpRequestUrl)
                .build();

        client.newCall(request).enqueue(callback);
    }
}
