package com.quanzhi.auditapiv2.common.util.utils;

import lombok.Data;

/**
 * Created by she<PERSON><PERSON> on 2017/11/07.
 */
@Data
public class ErrorCodeUtil {
    private int errCode;
    private String errMessage;
    private boolean success;

    public ErrorCodeUtil(int errCode) {
        this.errCode = errCode;
    }

    public ErrorCodeUtil(int errCode, String errMessage) {
        this.errCode = errCode;
        this.errMessage = errMessage;
    }

    @Override
    public String toString() {
        return errCode + "-" + errMessage;
    }
}
