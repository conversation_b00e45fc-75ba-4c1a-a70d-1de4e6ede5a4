package com.quanzhi.auditapiv2.common.util.utils;


import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: HaoJun
 * @Date: 2020/3/5 9:35 上午
 */
public class RestfulUtil {

    private static final Pattern PLACEHOLDER_PATTERN
            = Pattern.compile("\\$\\{([^}]*)\\}");


    /**
     * 匹配该restful接口的所有形式的接口的正则
     *
     * @param restfulUrl
     * @return 正则表达式字符串
     */
    public static final String getRestfulRegex(String restfulUrl) {
        String regex = restfulUrl.replaceAll("\\$\\{([^}]*)\\}", "[^/]+");
        regex += "[/]?$";
        return regex;
    }

    /**
     * 提取restful接口中类似${id1}中的 id1 列表
     * @param restfulUrl
     * @return
     */
    public static final List<String> extractPlaceHolders(String restfulUrl) {
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(restfulUrl);
        List<String> list = new ArrayList<>();
        while (matcher.find()) {
            list.add(matcher.group().substring(2, matcher.group().length() - 1));
        }
        return list;
    }

    /**
     * 检测该接口是否属于restful接口
     *
     * @param url
     * @return
     */
    public static final boolean isRestfulUrl(String url) {
        return !StringUtils.isNullOrEmpty(url) && url.contains("${");
    }
}
