package com.quanzhi.auditapiv2.common.util.entity;

/**
 * <AUTHOR>
 * @date 2019-12-08
 * @time 11:50
 */
public enum ExportTaskType {
    /**
     *
     */
    EXPORT_APP("导出应用"),

    EXPORT_API("导出接口"),

    EXPORT_SAMPLE("导出样例"),

    EXPORT_URL_STRUCTURE("导出应用结构列表"),

    EXPORT_URL_PATHS("导出应用结构列表"),

    EXPORT_API_WEAKNESS("导出web应用弱点"),

    EXPORT_API_WEAKNESS_REPORT("导出web应用弱点报告"),

    EXPORT_SYSLOG("导出审计日志"),

    EXPORT_API_REPORT("导出API状态"),

    EXPORT_WEAKNESS_REPORT("导出API弱点运营"),

    EXPORT_REPORT_DATA_LABEL("导出数据暴露面治理"),

    ACCOUNT_PARSE_CONFIG_EXPORT("导出账号解析配置"),

    USER_INFO_EXPORT("导出组织架构配置"),

    ORG_CONFIG_EXPORT("导出自动解析配置"),

    ACCOUNT_INFO_EXPORT("导出账号"),

    EXPORT_IP_INFO("导出IP列表"),

    EXPORT_APIACCOUNT_INFO("导出账号列表"),

    EXPORT_RISK_INFO_AGG("导出异常列表"),

    EXPORT_RISK_INFO("导出异常清单"),

    EXPORT_AGGRISK_INFO("导出风险清单"),

    EXPORT_RISKV2_INFO("导出异常清单"),

    EXPORT_DATA_MONTH_INFO("导出数据清单"),

    EXPORT_THREAT_IP("导出威胁IP"),

    EXPORT_THREAT_INFO("导出威胁信息"),

    ACTION_CONFIG_EXPORT("ACTION_CONFIG导出"),

    ACTION_CONFIG_FRONT_TRANSFORM_EXPORT("导出日志检索事件列表"),

    RISK_INFO_AGG_EXPORT("导出风险"),

    RISK_POLICY_EXPORT("导出风险策略"),

    EXPORT_DATA_LABEL_TEMPLATE("导出数据标签模板")
    ;


    /**
     * 类型名称
     */
    String typeName;

    ExportTaskType(String typeName) {
        this.typeName = typeName;
    }

    public static String getExportTaskTypeName(String taskType) {

        for (ExportTaskType exportTaskType : ExportTaskType.values()) {

            if (exportTaskType.toString().equals(taskType)) {

                return exportTaskType.typeName;
            }
        }

        return "";
    }

    public String getTypeName() {
        return typeName;
    }
}
