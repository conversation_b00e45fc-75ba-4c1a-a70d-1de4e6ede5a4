package com.quanzhi.auditapiv2.common.util.utils;


import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.archivers.ArchiveStreamFactory;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;

import java.io.*;
import java.util.Arrays;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @date 2018/4/20
 * @desc 升级包解密工具类
 */
public class DecryptUtils {

    /**
     * 文件读取缓冲区大小
     */
    private static final int CACHE_SIZE = 1024;

    /**
     * RSA公钥
     */
    private static final String PUB_KEY ="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDV6LgtO1M41K8ARYoLHq2JtYdD" +
            "uNofVJoMjczOEBCCRQcP8O6E64kXQtEV6tVsVnQVOMKrDH9oWoWaa49iHp1ai7o+" +
            "GP76AHSX82ewCKcgyJIEwj970As6FZQlsMcNaiopZG7sNjtp/I17O7qx6eDXRyIC" +
            "WyAA2Zvejh8r3xKwxQIDAQAB";

    private static int byteArray2Int(byte[] bytes) {
        int value = 0;
        // 由高位到低位
        for (int i = 0; i < 4; i++) {
            int shift = (4 - 1 - i) * 8;
            value += (bytes[i] & 0xff) << shift;// 往高位游
        }
        return value;
    }

    /**
     * 解密升级包
     * @param file
     * @throws Exception
     */
    public static void decrypt(File file ,String targetFile)throws Exception{
        try(InputStream is = new FileInputStream(file);
            OutputStream os = new FileOutputStream(targetFile)){
            byte[] cache = new byte[CACHE_SIZE];
            byte[] decrypt = null;
            int nRead = 0;
            int bLength = is.read(cache);
            if (bLength <= 8)
                throw new Exception("无效的升级文件,长度校验错误");
            // 读取前4个字节，判断加密方式
            String encryptType = new String(Arrays.copyOfRange(cache,0,4));
            if ("UAES".equals(encryptType)){
                // 再读取4字节获取加密key长度
                int rsaKeyLength = byteArray2Int(Arrays.copyOfRange(cache,4,8));
                byte[] rsaKeyBytes = new byte[rsaKeyLength];
                int cacheIndex = 8;
                for (int i=0; i < rsaKeyLength; i++, cacheIndex++){
                    if (cacheIndex == bLength){
                        bLength = is.read(cache);
                        cacheIndex = 0;
                    }
                    rsaKeyBytes[i] = cache[cacheIndex];
                }
                byte[] aesKey = RSADecryptUtils.decryptByPublicKey(rsaKeyBytes, PUB_KEY);
                // 将已读取的字节写入
                if (cacheIndex != CACHE_SIZE){
                    byte[] left = new byte[CACHE_SIZE];
                    byte[] padding = new byte[cacheIndex];
                    is.read(padding);
                    System.arraycopy(cache, cacheIndex,left,0,CACHE_SIZE - cacheIndex);
                    System.arraycopy(padding, 0,left,CACHE_SIZE - cacheIndex,cacheIndex);
                    decrypt = AESDecryptUtils.decrypt(left, aesKey);
                    os.write(decrypt);
                    left = null;
                    padding = null;
                }
                // 写入剩余的
                while ((nRead=is.read(cache)) != -1) {
                    if (CACHE_SIZE == nRead){
                        decrypt = AESDecryptUtils.decrypt(cache, aesKey);
                    }else {
                        byte[] temp = Arrays.copyOfRange(cache,0,nRead);
                        decrypt = AESDecryptUtils.decrypt(temp, aesKey);
                    }
                    os.write(decrypt);
                    os.flush();
                }
            }else {
                // 将已读取的写入
                if (CACHE_SIZE == bLength){
                    decrypt = RSADecryptUtils.decryptByPublicKey(cache, PUB_KEY);
                }else {
                    byte[] temp = Arrays.copyOfRange(cache,0,nRead);
                    decrypt = RSADecryptUtils.decryptByPublicKey(temp, PUB_KEY);
                }
                os.write(decrypt);
                // 写入剩余
                while ((nRead=is.read(cache)) != -1) {
                    if (CACHE_SIZE == nRead){
                        decrypt = RSADecryptUtils.decryptByPublicKey(cache, PUB_KEY);
                    }else {
                        byte[] temp = Arrays.copyOfRange(cache,0,nRead);
                        decrypt = RSADecryptUtils.decryptByPublicKey(temp, PUB_KEY);
                    }
                    os.write(decrypt);
                    os.flush();
                }
            }
        }catch (Exception e){
            throw new Exception("无效的升级文件，"+e.getMessage());
        }
    }

    /**
     * 将tar包解压到当前目录
     * @param targetPath 解压目标目录
     * @param targetFile tar包文件路径
     * @return
     * @throws Exception
     */
    public static boolean unGzipFile(String targetPath, String targetFile) throws Exception {
        File tarfile = new File(targetFile);
        ArchiveInputStream in = null;
        BufferedInputStream bis = null;
        try {
            GZIPInputStream gis = new GZIPInputStream(new BufferedInputStream(new FileInputStream(tarfile)));
            in = new ArchiveStreamFactory().createArchiveInputStream("tar", gis);
            bis = new BufferedInputStream(in);
            TarArchiveEntry entry = (TarArchiveEntry) in.getNextEntry();
            while (entry != null) {
                String name = entry.getName();
                String[] names = name.split(File.separator);
                String fileName = targetPath;
                for (int i = 0; i < names.length; i++) {
                    String str = names[i];
                    fileName = fileName + File.separator + str;
                }
                if (name.endsWith(File.separator)) {
                    File file = new File(fileName);
                    file.mkdirs();
                } else {
                    File file = getRealFileName(targetPath, name);
                    BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(file));
                    int b = -1;
                    while ((b = bis.read()) != -1) {
                        bos.write(b);
                    }
                    bos.flush();
                    bos.close();
                }
                entry = (TarArchiveEntry) in.getNextEntry();
            }
        } catch (Exception e) {
            throw new Exception("升级包不完整，升级失败！");
        } finally {
            bis.close();
            tarfile.delete();
        }

        return true;
    }

    /**
     * 获取非文件夹的文件
     * @param packPath
     * @param absFileName
     * @return
     */
    public static File getRealFileName(String packPath, String absFileName) {
        String[] dirs = absFileName.split(File.separator, absFileName.length());
        File ret = new File(packPath);
        if (dirs.length > 1) {
            for (int i = 0; i < dirs.length - 1; i++) {
                ret = new File(ret, dirs[i]);
            }
        }

        if (!ret.exists()) {
            ret.mkdirs();
        }
        ret = new File(ret, dirs[dirs.length - 1]);

        return ret;
    }
}
