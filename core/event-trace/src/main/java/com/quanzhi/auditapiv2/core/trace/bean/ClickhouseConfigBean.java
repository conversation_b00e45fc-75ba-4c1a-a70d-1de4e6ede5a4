package com.quanzhi.auditapiv2.core.trace.bean;

import com.alibaba.druid.DbType;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.quanzhi.audit.analysis.util.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class ClickhouseConfigBean {

    private static final String JDBC_CLASS = "ru.yandex.clickhouse.ClickHouseDriver";

    private static final String JDBC_URI = "*********************************************************************";

    @Value("${qz.ck.host}")
    private String host;
    @Value("${qz.ck.port}")
    private int port;
    @Value("${qz.ck.database}")
    private String database;
    @Value("${qz.ck.username}")
    private String username;
    @Value("${qz.ck.password}")
    private String password;
    @NacosValue(value = "${qz.ck.master.address:}",autoRefreshed = true)
    private String masterAddress;

    public DruidDataSource create(){
        log.warn("init clickhouse datasource pool!");
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setPoolPreparedStatements(false);
        String jdbcUrl = String.format(JDBC_URI, host+":"+port,database);
        dataSource.setUrl(jdbcUrl);
        if(DataUtil.isNotEmpty(masterAddress)){
            String url = String.format(JDBC_URI, masterAddress,database);
            dataSource.setUrl(url);
        }
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setDbType(DbType.clickhouse);
        dataSource.setDriverClassName(JDBC_CLASS);
        dataSource.setInitialSize(10);
        dataSource.setMaxActive(50);
        dataSource.setMaxWait(120000);
        dataSource.setMinIdle(15);
        dataSource.setValidationQuery("select 1");
        dataSource.setValidationQueryTimeout(30000);
        dataSource.setKeepAlive(true);
        dataSource.setKeepAliveBetweenTimeMillis(300000);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        dataSource.setTestWhileIdle(true);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(600000);
        dataSource.setMaxEvictableIdleTimeMillis(900000);
        return dataSource;
    }
}
