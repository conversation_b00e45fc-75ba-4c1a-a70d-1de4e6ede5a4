package com.quanzhi.auditapiv2.core.trace.facade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quanzhi.audit_core.common.config.annotation.DynamicValue;
import com.quanzhi.audit_core.common.utils.DataUtil;
import com.quanzhi.auditapiv2.core.encrypt.decrypt.IQzcrypt;
import com.quanzhi.auditapiv2.core.trace.bean.common.Constant;
import com.quanzhi.auditapiv2.core.trace.domain.CronService;
import com.quanzhi.auditapiv2.core.trace.dto.TaskRecord;
import com.quanzhi.auditapiv2.core.trace.dto.TraceTaskTemplate;
import com.quanzhi.auditapiv2.core.trace.dto.business.TraceTaskConfig;
import com.quanzhi.auditapiv2.core.trace.enums.TaskStatus;
import com.quanzhi.auditapiv2.core.trace.factory.SqlDataSourceFactory;
import com.quanzhi.auditapiv2.core.trace.model.DataLabelExtra;
import com.quanzhi.auditapiv2.core.trace.model.ScheduleTraceTask;
import com.quanzhi.auditapiv2.core.trace.service.IScheduleTraceTaskService;
import com.quanzhi.auditapiv2.core.trace.service.TaskRecordService;
import com.quanzhi.auditapiv2.core.trace.service.TraceTaskTemplateService;
import com.quanzhi.auditapiv2.core.trace.task.AbstractTraceTask;
import com.quanzhi.auditapiv2.core.trace.task.TraceTaskHelper;
import com.quanzhi.auditapiv2.core.trace.task.TraceTaskTypeEnum;
import com.quanzhi.auditapiv2.core.trace.task.callableWrapper.TaskCallableWrapper;
import com.quanzhi.auditapiv2.core.trace.task.job.TaskManager;
import com.quanzhi.metabase.common.utils.ServiceException;
import com.quanzhi.metabase.common.utils.task.NamedThreadFactory;
import com.quanzhi.operate.atomDefinition.ActionConfigFrontTransform;
import com.quanzhi.operate.operateHandler.IOperateHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component("taskManagerFacadeTrace")
@Slf4j
public class TaskManagerFacade {
    @Autowired
    TaskRecordService taskRecordService;

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    @Qualifier("sqlDataSourceFactoryTrace")
    SqlDataSourceFactory sqlDataSourceFactory;

    @Autowired
    TraceTaskTemplateService traceTaskTemplateService;

    @Autowired
    private IQzcrypt qzcrypt;

    @Autowired
    private IOperateHandlerService operateHandlerService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private TaskManager taskManager;

    @Autowired
    private IScheduleTraceTaskService scheduleTraceTaskService;

    @DynamicValue(dataId = "common.datalabel.json"
            , groupId = "common"
            , typeClz = DataLabelExtra.class)
    private List<DataLabelExtra> dataLabelExtras;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        // 每次后端启动,将溯源任务中 RUNNING 状态的任务设置为 ERROR， 一般是由于后端重启导致跑一半的任务直接结束了
        Criteria criteria = Criteria.where("taskStatus").is(TaskStatus.RUNNING.name());
        Query query = new Query(criteria);
        Update update = new Update();
        update.set("taskStatus", TaskStatus.ERROR.name());
        update.set("errorMsg","后端组件重启导致任务终结");
        mongoTemplate.updateMulti(query, update, Constant.TASK_RECORD_COLLECTION_NAME);

        // 接口和弱点离线审计重启也把运行的结束了
        Query reportQuery = new Query();
        reportQuery.addCriteria(Criteria.where("reportType").is("OFFLINE_REPORT"));
        reportQuery.addCriteria(Criteria.where("status").is("RUNNING"));
        Update reportUpdate = new Update();
        reportUpdate.set("status","FAIL");
        reportUpdate.set("errorMsg","后端组件重启导致任务终结");
        mongoTemplate.updateMulti(reportQuery, reportUpdate, "reportTask");

        Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory("traceTaskCommitWaitTask"))
                .scheduleAtFixedRate(() -> {
                    try {
                        commitWaitTaskRecord();
                    } catch (Throwable e) {
                        log.error("commitWaitTaskRecord Schedule error", e);
                    }
                }, FIX_DELAY_SECONDS, FIX_DELAY_SECONDS, TimeUnit.SECONDS);
    }

    @Scheduled(cron = "0 0/1 * * * ?")
    public void runScheduledTask() {

        long timestamp = System.currentTimeMillis();
        List<ScheduleTraceTask> scheduleTraceTasks = scheduleTraceTaskService.getAll();

        if(CollectionUtils.isEmpty(scheduleTraceTasks)) return;

        for(ScheduleTraceTask scheduleTraceTask: scheduleTraceTasks) {

            if(CronService.hitCronWithRange( scheduleTraceTask.getTraceTaskConfig().getTaskCron().getCron(),timestamp,3)) {

                TraceTaskConfig traceTaskConfig = scheduleTraceTask.getTraceTaskConfig();
                traceTaskConfig.fillScheduleTaskByCron(timestamp);

                try {
                    commitTraceTaskWrapper(traceTaskConfig,null);
                } catch (Exception e) {

                }
            }
        }
    }

    /**
     * 每隔FIX_DELAY_MINUTES分钟检测一次
     */
    public static final int FIX_DELAY_SECONDS = 1 * 60;

    /**
     * 获取模板简介分页
     * @param page
     * @param limit
     * @return
     */
    public List<TraceTaskTemplate> getTraceTaskTemplates( int page, int limit ) {
        List<TraceTaskTemplate> traceTaskTemplates = traceTaskTemplateService.getTemplates( page,limit );

        for(TraceTaskTemplate traceTaskTemplate : traceTaskTemplates) {

            if(traceTaskTemplate.getTraceTaskTypeEnum().equals( TraceTaskTypeEnum.RESOURCE_AND_WEAKNESS )) {

                try {

                    String queryStr = "{\n" +
                            "    \"id\":\"APP_AUDIT_2.1_OFFLINE_REPORT_LIST\",\n" +
                            "    \"actionFrontTransformPaths\":[\n" +
                            "        {\n" +
                            "            \"path\":\"$.repositoryOperateList[0].page\",\n" +
                            "            \"value\":1\n" +
                            "        },\n" +
                            "        {\n" +
                            "            \"path\":\"$.repositoryOperateList[0].limit\",\n" +
                            "            \"value\":3\n" +
                            "        },\n" +
                            "        {\n" +
                            "            \"path\":\"$.repositoryOperateList[0].frontCriteriaList\",\n" +
                            "            \"value\":[\n" +
                            "                {\n" +
                            "                    \"property\":\"reportType\",\n" +
                            "                    \"predicate\":\"IS\",\n" +
                            "                    \"value\":\"OFFLINE_REPORT\"\n" +
                            "                }\n" +
                            "            ]\n" +
                            "        }\n" +
                            "    ]\n" +
                            "}";
                    ActionConfigFrontTransform actionConfigFrontTransform = JSON.parseObject( queryStr,ActionConfigFrontTransform.class);
                    Map<String,Object> result = operateHandlerService.handleActions(actionConfigFrontTransform,null);
                    List<JSONObject> rows = JSON.parseArray( JSON.toJSONString( result.get("rows") ), JSONObject.class) ;

                    List<TaskRecord> taskRecords = new ArrayList<>();

                    taskRecords = rows.stream().map(i -> {
                        TaskRecord taskRecord = new TaskRecord();
                        taskRecord.setId( i.getString("_id") );
                        taskRecord.setTaskId( i.getString("_id") );
                        taskRecord.setName( i.getString("name") );
                        taskRecord.setTaskStatus( i.getString("status").equals( "SUCCESS") ? TaskStatus.FINISH.name() :
                                ( i.getString("status").equals("RUNNING") ? TaskStatus.RUNNING.name() : TaskStatus.ERROR.name()));
                        return taskRecord;
                    }).collect(Collectors.toList());
                    traceTaskTemplate.setTaskRecords( taskRecords );


                } catch (Exception e) {

                }

            } else {
                List<TaskRecord> taskRecords = taskRecordService.pageList(0,3,traceTaskTemplate.getTraceTaskTypeEnum());
                traceTaskTemplate.setTaskRecords( taskRecords );
            }
        }

        return traceTaskTemplates;
    }

    /**
     * 保存审计模板
     * @param traceTaskTemplate
     */
    public void saveTraceTaskTemplate( TraceTaskTemplate traceTaskTemplate) {
        traceTaskTemplateService.insert( traceTaskTemplate );
    }

    /**
     * 更新模板名称
     */
    public void updateTraceTaskTemplateName(String id ,String name) {
        traceTaskTemplateService.updateTraceTaskTemplateName( id,name );
    }

    /**
     * 删除审计模板
     * @param id
     */
    public void deleteTraceTaskTemplate( String id) {
        traceTaskTemplateService.deleteTemplate( id );
    }


    public List<ScheduleTraceTask> getScheduleTasksByType(TraceTaskTypeEnum traceTaskTypeEnum) {

         List<ScheduleTraceTask> scheduleTraceTasks =  scheduleTraceTaskService.getScheduleTasksByType( traceTaskTypeEnum );


         Map<String,String> dataLabelNameMap = dataLabelExtras.stream().collect(Collectors.toMap(
                 DataLabelExtra::getId,DataLabelExtra::getName,(i,j) -> i
         ));

        for (ScheduleTraceTask scheduleTraceTask : scheduleTraceTasks) {

            if(  scheduleTraceTask.getTraceTaskConfig() != null &&
                    scheduleTraceTask.getTraceTaskConfig().getTraceDto() != null &&
                    scheduleTraceTask.getTraceTaskConfig().getTraceDto().getLabels() != null
            ) {
                scheduleTraceTask.getTraceTaskConfig().getTraceDto().setLabelNames(
                        scheduleTraceTask.getTraceTaskConfig().getTraceDto().getLabels().stream()
                                .map(i -> Optional.ofNullable( dataLabelNameMap.get(i)).orElse(i) ).collect(Collectors.toList())
                );
            }
        }
         return scheduleTraceTasks;
    }

    /**
     * 删除周期性任务
     * @param id
     */
    public void deleteScheduleTask(String id) {
         scheduleTraceTaskService.deleteScheduleTask(id);
    }

    /**
     * 提交周期性任务
     * @param traceTaskConfig
     */
    public ScheduleTraceTask commitScheduleTraceTaskWrapper(TraceTaskConfig traceTaskConfig) throws Exception{

        if( scheduleTraceTaskService.existByType( traceTaskConfig.getTraceTaskTypeEnum() ) ) {
            throw new Exception("此模板只能创建一个定时任务，请先删除旧的任务！");
        }

        ScheduleTraceTask scheduleTraceTask = new ScheduleTraceTask();
        scheduleTraceTask.setTraceTaskConfig( traceTaskConfig );

        return scheduleTraceTaskService.save(  scheduleTraceTask );
    }

    /**
     * 提交审计任务
     * @param traceTaskConfig
     */
    public TaskRecord commitTraceTaskWrapper(TraceTaskConfig traceTaskConfig, MultipartFile multipartFile) throws Exception {

        // 线索溯源需要把前端传过来的线索加密后放入 查询条件中
        if(traceTaskConfig.getTraceTaskTypeEnum().equals(TraceTaskTypeEnum.SENSI_DATA_TRACE)) {
            List<String> fileContent = extractFileContent(multipartFile);
            // 自己手动写的和文件上传的合并到一起
            if(DataUtil.isNotEmpty(traceTaskConfig.getTraceDto().getOriginalSensiDataValues())) {
                List<String> collect = traceTaskConfig.getTraceDto().getOriginalSensiDataValues().stream().filter(i -> i != null && !"".equals(i) && !"null".equals(i)).collect(Collectors.toList());
                traceTaskConfig.getTraceDto().setOriginalSensiDataValues(collect);
                traceTaskConfig.getTraceDto().getOriginalSensiDataValues().addAll(fileContent);
            }else{
                traceTaskConfig.getTraceDto().setOriginalSensiDataValues(fileContent);
            }
            if(DataUtil.isEmpty(traceTaskConfig.getTraceDto().getOriginalSensiDataValues())){
                throw new ServiceException("追查的数据不能为空!");
            }
            traceTaskConfig.getTraceDto().setSensiDataValues(qzcrypt.encrypt(traceTaskConfig.getTraceDto().getOriginalSensiDataValues()));
        }
        TraceTaskTemplate traceTaskTemplate = traceTaskTemplateService.getByTraceTaskTypeEnum( traceTaskConfig.getTraceTaskTypeEnum() );
        TaskRecord taskRecord = TaskRecord.forNewRecord(traceTaskConfig,traceTaskTemplate);
        try {
            taskRecordService.checkRecordName( taskRecord );
        } catch (Exception e) {
            throw e;
        }
        taskRecordService.insert(taskRecord);
        if (taskManager.containsTraceTaskTemplateTask( taskRecord.getTraceTaskTypeEnum() )) return taskRecord;
        AbstractTraceTask abstractTraceTask = TraceTaskHelper.getAbstractTraceTaskByType( traceTaskConfig.getTraceTaskTypeEnum() );
        abstractTraceTask.initMessage(taskRecord,traceTaskConfig);
        TaskCallableWrapper taskCallableWrapper = new TaskCallableWrapper(abstractTraceTask,applicationEventPublisher);
        taskManager.addTask(taskCallableWrapper);
        return taskRecord;
    }

    private List<String> extractFileContent(MultipartFile multipartFile){
        if(multipartFile == null || multipartFile.isEmpty()){
            return new ArrayList<>();
        }
        // 校验文件类型必须是txt
        if (!multipartFile.getContentType().equals("text/plain")) {
            throw new IllegalArgumentException("文件类型必须是txt");
        }
        // 校验文件大小最大1MB
        if (multipartFile.getSize() > 1 * 1024 * 1024) {
            throw new IllegalArgumentException("文件大小不能超过1MB");
        }
        List<String> contentList = getStrings(multipartFile);
        return contentList;
    }

    private static List<String> getStrings(MultipartFile multipartFile) {
        List<String> contentList = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(multipartFile.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 使用中英文逗号或者换行分隔内容
                String[] parts = line.split("[,，\\n]");
                for (String part : parts) {
                    if (!part.trim().isEmpty()) {
                        contentList.add(part.trim());
                    }
                }
            }
        } catch (IOException e) {
            log.error("read file error:",e);
        }
        return contentList;
    }

    /**
     * 将数据库中等待的任务进行尝试进行溯源任务
     * @param
     */
    private void commitWaitTaskRecord() {

        Arrays.stream(TraceTaskTypeEnum.values()).forEach(i -> {

            TaskRecord taskRecord =  taskRecordService.getTaskRecord(TaskStatus.WAIT,i);
            if(taskRecord != null) {
                if ( !taskManager.containsTraceTaskTemplateTask( taskRecord.getTraceTaskTypeEnum() )) {
                    AbstractTraceTask abstractTraceTask = TraceTaskHelper.getAbstractTraceTaskByType( taskRecord.getTraceTaskTypeEnum() );
                    abstractTraceTask.initMessage(taskRecord,taskRecord.getTraceTaskConfig());
                    TaskCallableWrapper taskCallableWrapper = new TaskCallableWrapper(abstractTraceTask,applicationEventPublisher);
                    taskManager.addTask(taskCallableWrapper);
                }
            }
        });
    }

    public void cancelTraceTaskWrapper(String id) {
        TaskRecord taskRecord = taskRecordService.findById(id);
        if (taskRecord == null || taskRecord.getTaskId() == null) {
            log.warn(String.format("task record %s is not exists in mongod",id));
            return;
        }
        if (taskManager.containsTaskCallableWrapper(id)) {
            log.info("cancel task , id:{} taskId: {}", taskRecord.getId(), taskRecord.getTaskId());
            TaskCallableWrapper taskCallableWrapper = taskManager.getCallableWrapper(taskRecord.getTaskId());
            if (taskCallableWrapper != null){
                taskCallableWrapper.getAbstractTraceTask().exit();
            }
        }else {
            log.warn(String.format("task record %s is not exists in taskManager",id));
            Criteria criteria = Criteria.where("_id").is(id).and("taskStatus").ne(TaskStatus.FINISH.name());
            taskRecordService.updateTaskStatus(new Query(criteria),TaskStatus.CANCEL);
        }
    }

    public void deleteRecord(String id) {
        // 有可能删除的是正在跑的任务，导致任务都删除了，后台还在一直跑，后面的任务一直处于等待中,但是讲道理应该取消而不是直接删除
        cancelTraceTaskWrapper(id);
        taskRecordService.deleteRecord(id);
    }

    public void removeRecord(List<String> idList) {
        taskRecordService.removeRecord(idList);
    }

    public TaskRecord findById(String taskId){
        return taskRecordService.findById(taskId);
    }

    public TraceTaskTemplate findByIdTraceTemplate(String id){
        return traceTaskTemplateService.getById(id);
    }

    public void downloadTemplate(HttpServletResponse response) throws IOException {
        String content = "350982195712251103,15552879441,12345678,<EMAIL>,2201128450";
        response.setContentType("text/plain;charset=UTF-8");
        String fileName = URLEncoder.encode("追查数据模版.txt", "UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        ServletOutputStream out = response.getOutputStream();
        out.write(content.getBytes(Charset.defaultCharset()));
        IoUtil.close(out);
    }
}
