package com.quanzhi.auditapiv2.core.trace.task;

/**
 * <AUTHOR>
 * @date 2022/3/8 4:38 下午
 */
public enum TraceTaskTypeEnum {

    /**
     * 数据溯源
     */
    SENSI_DATA_TRACE,

    /**
     * 主体溯源
     */
    ENTITY_TRACE,

    /**
     * 特权账号
     */
    SPECIAL_ACCOUNT,

    /**
     * 离职人员审计
     */
    OFF_ACCOUNT,

    /**
     * 搜索日志转存为离线任务数据
     */
    ROLLOVER_SEARCH_EVENT,

    /**
     * 资产与弱点离线审计
     * 这个是原来的离线报告，产品非要加到这边
     * 这个比较特殊，需要特殊处理
     */
    RESOURCE_AND_WEAKNESS
}
