package com.quanzhi.auditapiv2.core.trace.dao.impl;

import com.mongodb.client.result.DeleteResult;
import com.quanzhi.auditapiv2.core.trace.dao.MongoDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;


@Slf4j
@Repository("mongoDaoImplTrace")
public class MongoDaoImpl implements MongoDao {
    @Autowired
    private MongoTemplate mongoTemplate;

//    public void createIndex(String collection){
//        mongoTemplate.getCollection("");
//    }

    @Override
    public <T> void  save(T o,String collection){
        mongoTemplate.save(o,collection);
    }

    @Override
    public boolean existCollection(String collectionName) {
        if (collectionName != null) {
            return mongoTemplate.collectionExists(collectionName);
        }else {
            return false;
        }
    }
    @Override
    public Long count(Query query,String collection){
        return mongoTemplate.count(query,collection);
    }
    @Override
    public boolean existQuery(Query query,String collection){
        return mongoTemplate.exists(query,collection);
    }

    @Override
    public void delCollection(String collectionName) {
        if (existCollection(collectionName)){
            log.warn("clean collection["+String.valueOf(collectionName)+"]");
            mongoTemplate.dropCollection(collectionName);
        }
    }

    @Override
    public Set<String> listAllCollection() {
        return mongoTemplate.getCollectionNames();
    }

    @Override
    public void delMultiCollection(List<String> collectionNameList) {
        if (collectionNameList != null && collectionNameList.size() >0){
            Set<String> collection = listAllCollection();
            for (String c : collectionNameList) {
                if (collection.contains(c)){
                    mongoTemplate.dropCollection(c);
                }
            }
        }
    }
    @Override
    public <T> List<T> find(Query query, Class<T> clzss, String collectionName){
        return mongoTemplate.find(query,clzss,collectionName);
    }
    @Override
    public <T> T findOne(Query query, Class<T> clzss, String collectionName){
        return mongoTemplate.findOne(query,clzss,collectionName);
    }
    @Override
    public <T> void  insert(Collection<T> docs, String collection){
        mongoTemplate.insert(docs,collection);
    }
    @Override
    public void updateMulti(Query query, Update update, String collection){
        mongoTemplate.updateMulti(query,update,collection);
    }

    @Override
    public long remove(Query query, String collection){
        DeleteResult deleteResult =  mongoTemplate.remove(query,collection);
        return deleteResult.getDeletedCount();
    }

    @Override
    public <T> List<T> findAllAndRemove(Query query, Class<T> clzss, String collection) {
        return mongoTemplate.findAllAndRemove(query,clzss,collection);
    }
}
