package com.quanzhi.auditapiv2.core.trace.service.impl;

import com.quanzhi.auditapiv2.core.trace.dao.TraceTaskTemplateDao;
import com.quanzhi.auditapiv2.core.trace.dto.TraceTaskTemplate;
import com.quanzhi.auditapiv2.core.trace.service.TraceTaskTemplateService;
import com.quanzhi.auditapiv2.core.trace.task.TraceTaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class TraceTaskTemplateServiceImpl implements TraceTaskTemplateService {

    @Autowired
    private TraceTaskTemplateDao traceTaskTemplateDao;

    @Override
    public void insert(TraceTaskTemplate traceTaskTemplate) {
        traceTaskTemplateDao.save(traceTaskTemplate);
    }

    @Override
    public void deleteTemplate (String id) {
        traceTaskTemplateDao.deleteTemplate(id);
    }

    @Override
    public List<TraceTaskTemplate> getTemplates(int page, int limit) {
        return traceTaskTemplateDao.getTemplates(page,limit);
    }

    @Override
    public TraceTaskTemplate getById(String id) {
        return traceTaskTemplateDao.findById(id);
    }

    @Override
    public TraceTaskTemplate getByTraceTaskTypeEnum(TraceTaskTypeEnum traceTaskTypeEnum) {
        return traceTaskTemplateDao.findByTraceTaskTypeEnum( traceTaskTypeEnum );
    }

    @Override
    public void updateTraceTaskTemplateName( String id ,String name) {
        traceTaskTemplateDao.updateTraceTaskTemplateName(id,name);
    }
}
