package com.quanzhi.auditapiv2.core.trace.dao;
import com.quanzhi.auditapiv2.core.trace.dto.TaskRecord;
import com.quanzhi.auditapiv2.core.trace.enums.TaskStatus;
import com.quanzhi.auditapiv2.core.trace.task.TraceTaskTypeEnum;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

public interface TaskRecordDao {

   void save(TaskRecord taskRecord);

   void deleteRecord(String taskId);

  void updateTaskStatus(Query query, TaskStatus taskStatus);

    List<TaskRecord> pageList(int start, int limit, TraceTaskTypeEnum traceTaskTypeEnum);

    void findAndRemove(Query query);

  TaskRecord getTaskRecord(TaskStatus taskStatus,TraceTaskTypeEnum traceTaskTypeEnum);

    List<TaskRecord> pageListRunningTask(int start, int limit);

    /**
     * 根据id查找
     * @param id
     * @return
     */
    TaskRecord findById(String id);

    boolean existByName(String name,TraceTaskTypeEnum traceTaskTypeEnum);

  boolean existByName(String name,TraceTaskTypeEnum traceTaskTypeEnum,String province);


  /**
     * count
     * @return
     */
    long count();

    /**
     * countRunningTask
     * @return
     */
    long countRunningTask();

    void removeRecord(List<String> idList);
}
